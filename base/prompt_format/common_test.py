"""Unit tests for base/prompt_format/common."""

from base.prompt_format.common import (
    PromptChunk,
    ChatRequestNode,
    ChatRequestNodeType,
    ChatRequestText,
    ChatRequestToolResult,
    ChatRequestImage,
    ImageFormatType,
    is_request_message_tool_result,
    get_request_message_as_text_always,
)


def test_chunk_comp_different_files():
    """Test chunk comparison with different files."""

    # Matching char_start
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=1, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileB.txt", char_start=0, char_end=1, blob_name="fileB"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1

    # Not matching char_start (direction 1)
    chunk1 = PromptChunk(
        text="a", path="file1.txt", char_start=0, char_end=1, blob_name="file1"
    )
    chunk2 = PromptChunk(
        text="a", path="file2.txt", char_start=5, char_end=6, blob_name="file2"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1

    # Not matching char_start (direction 2)
    chunk1 = PromptChunk(
        text="a", path="file1.txt", char_start=5, char_end=6, blob_name="file1"
    )
    chunk2 = PromptChunk(
        text="a", path="file2.txt", char_start=1, char_end=2, blob_name="file2"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1


def test_chunk_comp_same_file():
    """Test chunk comparison with same file."""

    # Matching char_start
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=1, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=1, blob_name="fileA"
    )
    assert not chunk1 < chunk2, "Chunks are equal"
    assert not chunk2 < chunk1, "Chunks are equal"

    # Not matching char_start, no overlap
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=1, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileA.txt", char_start=5, char_end=6, blob_name="fileA"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1

    # Not matching char_start, overlap
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=0, char_end=5, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileA.txt", char_start=3, char_end=8, blob_name="fileA"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1

    # Matching char_start, different lengths
    chunk1 = PromptChunk(
        text="a", path="fileA.txt", char_start=3, char_end=5, blob_name="fileA"
    )
    chunk2 = PromptChunk(
        text="a", path="fileA.txt", char_start=3, char_end=8, blob_name="fileA"
    )
    assert chunk1 < chunk2
    assert not chunk2 < chunk1


def test_is_request_message_tool_result_with_string():
    """Test is_request_message_tool_result with string input."""
    result = is_request_message_tool_result("Hello world")
    assert result is False


def test_is_request_message_tool_result_with_text_node():
    """Test is_request_message_tool_result with text node."""
    text_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="Hello"),
        tool_result_node=None,
    )
    result = is_request_message_tool_result([text_node])
    assert result is False


def test_is_request_message_tool_result_with_tool_result_node():
    """Test is_request_message_tool_result with tool result node."""
    tool_result_node = ChatRequestNode(
        id=2,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id="test_id", content="Tool result", is_error=False
        ),
    )
    result = is_request_message_tool_result([tool_result_node])
    assert result is True


def test_is_request_message_tool_result_with_mixed_nodes():
    """Test is_request_message_tool_result with mixed nodes."""
    text_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="Hello"),
        tool_result_node=None,
    )
    tool_result_node = ChatRequestNode(
        id=2,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id="test_id", content="Tool result", is_error=False
        ),
    )

    # Should return True if any node is a tool result
    result = is_request_message_tool_result([text_node, tool_result_node])
    assert result is True


def test_is_request_message_tool_result_with_empty_list():
    """Test is_request_message_tool_result with empty list."""
    result = is_request_message_tool_result([])
    assert result is False


def test_get_request_message_as_text_always_with_string():
    """Test get_request_message_as_text_always with string input."""
    result = get_request_message_as_text_always("Hello world")
    assert result == "Hello world"


def test_get_request_message_as_text_always_with_text_node():
    """Test get_request_message_as_text_always with text node."""
    text_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="Hello world"),
        tool_result_node=None,
    )
    result = get_request_message_as_text_always([text_node])
    assert result == "Hello world"


def test_get_request_message_as_text_always_with_multiple_text_nodes():
    """Test get_request_message_as_text_always with multiple text nodes."""
    text_node1 = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="Hello "),
        tool_result_node=None,
    )
    text_node2 = ChatRequestNode(
        id=2,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="world!"),
        tool_result_node=None,
    )
    result = get_request_message_as_text_always([text_node1, text_node2])
    assert result == "Hello world!"


def test_get_request_message_as_text_always_with_tool_result_node():
    """Test get_request_message_as_text_always with tool result node (should skip it)."""
    tool_result_node = ChatRequestNode(
        id=2,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id="test_id", content="Tool result", is_error=False
        ),
    )
    result = get_request_message_as_text_always([tool_result_node])
    assert result == ""  # Should return empty string since no text nodes


def test_get_request_message_as_text_always_with_mixed_nodes():
    """Test get_request_message_as_text_always with mixed nodes (should only extract text)."""
    text_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="Hello world"),
        tool_result_node=None,
    )
    tool_result_node = ChatRequestNode(
        id=2,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id="test_id", content="Tool result", is_error=False
        ),
    )

    # Should only extract text from text nodes, ignore tool result
    result = get_request_message_as_text_always([text_node, tool_result_node])
    assert result == "Hello world"


def test_get_request_message_as_text_always_with_empty_list():
    """Test get_request_message_as_text_always with empty list."""
    result = get_request_message_as_text_always([])
    assert result == ""


def test_is_request_message_tool_result_with_image_node():
    """Test is_request_message_tool_result with image node (should return False)."""
    image_node = ChatRequestNode(
        id=3,
        type=ChatRequestNodeType.IMAGE,
        text_node=None,
        tool_result_node=None,
        image_node=ChatRequestImage(
            image_data="fake_base64_image_data", format=ImageFormatType.PNG
        ),
    )
    result = is_request_message_tool_result([image_node])
    assert result is False


def test_get_request_message_as_text_always_with_image_node():
    """Test get_request_message_as_text_always with image node (should skip it)."""
    image_node = ChatRequestNode(
        id=3,
        type=ChatRequestNodeType.IMAGE,
        text_node=None,
        tool_result_node=None,
        image_node=ChatRequestImage(
            image_data="fake_base64_image_data", format=ImageFormatType.PNG
        ),
    )
    result = get_request_message_as_text_always([image_node])
    assert result == ""  # Should return empty string since no text nodes


def test_get_request_message_as_text_always_with_all_node_types():
    """Test get_request_message_as_text_always with all node types (should only extract text)."""
    text_node = ChatRequestNode(
        id=1,
        type=ChatRequestNodeType.TEXT,
        text_node=ChatRequestText(content="Hello world"),
        tool_result_node=None,
    )
    tool_result_node = ChatRequestNode(
        id=2,
        type=ChatRequestNodeType.TOOL_RESULT,
        text_node=None,
        tool_result_node=ChatRequestToolResult(
            tool_use_id="test_id", content="Tool result", is_error=False
        ),
    )
    image_node = ChatRequestNode(
        id=3,
        type=ChatRequestNodeType.IMAGE,
        text_node=None,
        tool_result_node=None,
        image_node=ChatRequestImage(
            image_data="fake_base64_image_data", format=ImageFormatType.PNG
        ),
    )

    # Should only extract text from text nodes, ignore other types
    result = get_request_message_as_text_always(
        [text_node, tool_result_node, image_node]
    )
    assert result == "Hello world"
