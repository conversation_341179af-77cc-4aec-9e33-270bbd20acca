#!/bin/sh -e

if [ -z "$BUILD_WORKSPACE_DIRECTORY" ]; then
	echo "BUILD_WORKSPACE_DIRECTORY not set" >&2
	exit 1
fi

OUTDIR="$BUILD_WORKSPACE_DIRECTORY/base/tokenizers"
echo "Current directory: $(pwd)"
cp --remove-destination ../deepseek_coder_instruct/tokenizer.json "$OUTDIR/deepseek_coder_instruct_tokenizer.json"
cp --remove-destination ../dbrx_instruct_vocab/tokenizer.json "$OUTDIR/dbrx_instruct_vocab.json"
cp --remove-destination ../llama3_instruct/tokenizer.json "$OUTDIR/llama3_instruct_tokenizer.json"
cp --remove-destination ../starcoder_base_vocab/vocab.json "$OUTDIR/starcoder_vocab.json"
cp --remove-destination ../deepseek_coder_v2_base_vocab/tokenizer.json "$OUTDIR/deepseek_coder_v2_base_vocab.json"
cp --remove-destination ../starcoder2_base_vocab/vocab.json "$OUTDIR/starcoder2_vocab.json"
cp --remove-destination ../openai_gpt2_vocab/file/downloaded "$OUTDIR/gpt2-merges.txt"
cp --remove-destination ../qwen25coder_vocab/tokenizer.json "$OUTDIR/qwen25coder_vocab.json"
cp --remove-destination ../qwen3_vocab/tokenizer.json "$OUTDIR/qwen3_vocab.json"
cp --remove-destination ../qwen3_embedding_0.6b_vocab/tokenizer.json "$OUTDIR/qwen3_embedding_0.6b_vocab.json"
cp --remove-destination ../qwen3_embedding_vocab/tokenizer.json "$OUTDIR/qwen3_embedding_vocab.json"
cp --remove-destination base/tokenizers/tiktoken.so "$OUTDIR/"
