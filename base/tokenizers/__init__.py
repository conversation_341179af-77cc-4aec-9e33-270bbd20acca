from typing import Callable, Optional

from base.tokenizers import tokenizer
from base.tokenizers.deepseek_coder_v2_tokenizer import (
    DeepSeekCoderV2SpecialTokens,
    DeepSeekCoderV2Tokenizer,
)
from base.tokenizers.deepseek_tokenizer import (
    DeepSeekCoderBaseTokenizer,
    DeepSeekCoderSpecialTokens,
)
from base.tokenizers.llama3_tokenizer import (
    Llama3BaseTokenizer,
    Llama3InstructTokenizer,
    Llama3SpecialTokens,
)
from base.tokenizers.qwen25coder_tokenizer import (
    Qwen25CoderSpecialTokens,
    Qwen25CoderTokenizer,
)
from base.tokenizers.rogue_tokenizer import RogueSpecialTokens
from base.tokenizers.starcoder2_tokenizer import StarCoder2Tokenizer
from base.tokenizers.tiktoken_codegen_tokenizer import CodeGenSpecialTokens
from base.tokenizers.qwen3_tokenizer import (
    Qwen3SpecialTokens,
    Qwen3Tokenizer,
    Qwen3EmbeddingSpecialTokens,
    Qwen3EmbeddingTokenizer,
)
from base.tokenizers.tiktoken_starcoder_tokenizer import (
    StarCoderSpecialTokens,
    TiktokenStarCoderTokenizer,
)
from base.tokenizers.tokenizer import (
    FimSpecialTokens,
    NextEditGenSpecialTokens,
    RerankerSpecialTokens,
    RetrievalSpecialTokens,
    SpecialTokens,
    Tokenizer,
)


def create_tokenizer_by_name(name: str) -> Tokenizer:
    """Creates a tokenizer of the kind specified by name.

    If there is no kind of tokenizer with the given name, an exception is thrown.
    """
    return tokenizer.REGISTRY.get(name)()


def list_tokenizers():
    """Lists all available tokenizers."""
    return [name for name, _ in tokenizer.REGISTRY]
