exports_files(
    ["clean.sh"],
    visibility = ["//tools/generate_proto_typestubs:__pkg__"],
)

sh_binary(
    name = "clean",
    srcs = ["clean.sh"],
    deps = [
        ":install_lib",
    ],
)

sh_binary(
    name = "install",
    srcs = ["install.sh"],
    deps = [
        ":install_lib",
    ],
)

sh_library(
    name = "install_lib",
    data = [
        "//base/blob_names:install",
        "//base/datasets:install",
        "//base/diff_utils:install",
        "//base/error_details:install",
        "//base/fastforward:install",
        "//base/fastforward/deepseek_v2:install",
        "//base/static_analysis:install",
        "//base/tokenizers:install",
        "//third_party/tiktoken:install",
    ],
    visibility = ["//tools/generate_proto_typestubs:__pkg__"],
)
