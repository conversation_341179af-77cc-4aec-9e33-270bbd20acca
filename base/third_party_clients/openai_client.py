"""An OpenAI client, which is also compatible with xAI API."""

import json
import time
from typing import Any, Generator, Iterator, Literal

import httpx
import openai
import prometheus_client
import structlog
from openai import (
    NOT_GIVEN as OPENAI_NOT_GIVEN,
)
from openai import (
    APIConnectionError,
    APIStatusError,
    BadRequestError,
    InternalServerError,
    RateLimitError,
)
from openai.types.chat import ChatCompletionToolParam
from openai.types.shared_params import FunctionDefinition
from typing_extensions import override

import base.feature_flags
from base.prompt_format.common import (
    ChatRequestNodeType,
    ChatResultNodeType,
    Exchange,
    ImageFormatType,
    RequestMessage,
    ResponseMessage,
    StopReason,
    try_get_request_message_as_text,
)
from base.prompt_format_chat import get_token_counter_by_prompt_formatter_name
from base.third_party_clients.common import (
    InvalidArgumentRpcError,
    UnavailableRpcError,
)
from base.third_party_clients.third_party_model_client import (
    EndOfStream,
    ThirdPartyModelClient,
    ThirdPartyModelResponse,
    ToolChoice,
    ToolDefinition,
    ToolUseResponse,
    ToolUseStart,
)
from base.third_party_clients.token_counter.token_counter import TokenCounter

# Feature flag to enable resource exhausted error handling
OPENAI_RATE_LIMIT_IS_RESOURCE_EXHAUSTED = False

# Note that this metric can be inaccurate, because it depends on the client
# closing the generator stream as soon as it is done consuming results.
OPENAI_RESPONSE_LATENCY = prometheus_client.Histogram(
    "au_openai_response_latency",
    "Latency of OpenAI API responses in seconds",
    ["model"],
    buckets=[
        0.1,
        0.25,
        0.5,
        1.0,
        2.5,
        5.0,
        10.0,
        20.0,
        50.0,
        100.0,
        200.0,
        float("inf"),
    ],
)

OPENAI_FIRST_TOKEN_LATENCY = prometheus_client.Histogram(
    "au_openai_first_token_latency",
    "Latency of first token of OpenAI API responses in seconds",
    ["model"],
    buckets=[
        0.1,
        0.25,
        0.5,
        1.0,
        2.5,
        5.0,
        10.0,
        20.0,
        50.0,
        100.0,
        200.0,
        float("inf"),
    ],
)

# Prompt cache usage metrics
PROMPT_INPUT_TOKENS = prometheus_client.Histogram(
    "au_openai_prompt_input_tokens",
    labelnames=["model_caller"],
    documentation="Tokens in the prompt input for a given request",
    buckets=[
        0,
        100,
        200,
        500,
        1000,
        2000,
        5000,
        10000,
        20000,
        50000,
        100000,
        200000,
        500000,
        float("inf"),
    ],
)

FIRST_TOKEN_HIGH_LATENCY_THRESHOLD = 10  # seconds

MAX_RETRIES = 2  # Default is 2
# Default is httpx.Timeout(timeout=600.0, connect=5.0)
# We add a read timeout, but it's not very strict as it covers context processing and
# periods of time when model is generating tool input where nothing is streamed.
TIMEOUT = httpx.Timeout(timeout=280.0, connect=3.0, read=75.0)

_token_count_calls = prometheus_client.Counter(
    "au_openai_token_count_calls",
    "Number of token counting calls made to OpenAI client",
)

_output_text_chars_counter = prometheus_client.Counter(
    "au_openai_output_text_size",
    "Size of output text from OpenAI client",
    ["model"],
)

_output_tokens_histogram = prometheus_client.Histogram(
    "au_openai_output_tokens",
    "Number of output tokens from OpenAI client",
    ["model_caller"],
    buckets=[
        0,
        100,
        200,
        500,
        1000,
        2000,
        5000,
        10000,
        20000,
        50000,
        100000,
        float("inf"),
    ],
)

_error_counter = prometheus_client.Counter(
    "au_openai_error_count",
    "Number of errors from OpenAI client",
    ["model", "error_type"],
)

_bad_request_counter = prometheus_client.Counter(
    "au_openai_bad_request_types",
    "Number of bad request errors from OpenAI client",
    ["model_caller", "reason"],
)

_output_tool_use_blocks = prometheus_client.Counter(
    "au_openai_output_tool_use_blocks",
    "Number of tool use blocks from OpenAI client",
    ["model"],
)

_event_type_counter = prometheus_client.Counter(
    "au_openai_event_type",
    "Number of events from OpenAI client",
    ["model", "event_type"],
)

XAI_BASE_URL = "https://api.x.ai/v1"
XAI_RESEARCH_BASE_URL = "https://research-models.api.x.ai/research/swe"

OPENAI_MODEL_ATTRIBUTES = {
    "gpt-4o-2024-08-06": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
    "gpt-4o-2024-11-20": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
    "gpt-4.1-2025-04-14": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
    "gpt-4.1-mini-2025-04-14": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
    "gpt-4.1-nano-2025-04-14": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
    "o1-preview-2024-09-12": {
        "system_prompt": False,
        "streaming": True,
        "temperature": False,
        "function_calling": False,
    },
    "o1-2024-12-17": {
        "system_prompt": True,
        "streaming": False,
        "temperature": False,
        "function_calling": False,
    },
    "o1-mini-2024-09-12": {
        "system_prompt": False,
        "streaming": True,
        "temperature": False,
        "function_calling": False,
    },
    "o3-mini-2025-01-31": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
    "o3-2025-04-16": {
        "system_prompt": True,
        "streaming": False,
        "temperature": False,
        "function_calling": False,
    },
    "o4-mini-2025-04-16": {
        "system_prompt": True,
        "streaming": True,
        "temperature": False,
        "function_calling": False,
    },
    "chatgpt-4o-latest": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": False,
    },
    "gpt-4.5-preview-2025-02-27": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
}
OPENAI_RESPONSES_MODEL_ATTRIBUTES = {
    "o1-pro-2025-03-19": {
        "system_prompt": True,
        "streaming": False,
        "temperature": False,
        "function_calling": False,
    },
}
XAI_MODEL_ATTRIBUTES = {
    "grok-2-1212": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
    "grok-3-beta": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
    "grok-3-mini-beta": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
}
XAI_RESEARCH_MODEL_ATTRIBUTES = {
    "swe-v6-checkpoint-05-23": {
        "system_prompt": True,
        "streaming": True,
        "temperature": True,
        "function_calling": True,
    },
}


def get_tool_definition(tool_name: str) -> ChatCompletionToolParam:
    openai_tool_definitions: dict[str, ChatCompletionToolParam] = {
        "replace_text": ChatCompletionToolParam(
            type="function",
            function=FunctionDefinition(
                name="replace_text",
                description="Replace part of the file starting from line `start_line_number` (inclusive) to line `end_line_number` (inclusive) with the `replacement_text`. Always generate arguments in the following order: `old_text`, `start_line_number`, `end_line_number`, `replacement_text`.",
                parameters={
                    "type": "object",
                    "properties": {
                        "old_text": {
                            "type": "string",
                            "description": "The original text.",
                        },
                        "start_line_number": {
                            "type": "integer",
                            "description": "The line number where the original text starts, inclusive.",
                        },
                        "end_line_number": {
                            "type": "integer",
                            "description": "The line number where the original text ends, inclusive.",
                        },
                        "replacement_text": {
                            "type": "string",
                            "description": "The new text.",
                        },
                    },
                    "required": [
                        "old_text",
                        "start_line_number",
                        "end_line_number",
                        "replacement_text",
                    ],
                },
            ),
        ),
        "check_command_code_related": ChatCompletionToolParam(
            type="function",
            function=FunctionDefinition(
                name="check_command_code_related",
                description="Check if the given command executed any code related checks.",
                parameters={
                    "type": "object",
                    "properties": {
                        "result": {
                            "type": "boolean",
                            "description": "Boolean indicating whether the command executed any code related checks.",
                        },
                        "desc": {
                            "type": "string",
                            "description": "Short and concise one line explanation of the result.",
                        },
                    },
                    "required": ["result", "desc"],
                },
            ),
        ),
        "command_output_contain_errors": ChatCompletionToolParam(
            type="function",
            function=FunctionDefinition(
                name="command_output_contain_errors",
                description="Report if the given command output contains errors.",
                parameters={
                    "type": "object",
                    "properties": {
                        "result": {
                            "type": "boolean",
                            "description": "Boolean indicating whether the command output contains errors.",
                        },
                        "desc": {
                            "type": "string",
                            "description": "Short and concise one line explanation.",
                        },
                    },
                    "required": ["result", "desc"],
                },
            ),
        ),
        "glean_search": ChatCompletionToolParam(
            type="function",
            function=FunctionDefinition(
                name="glean_search",
                description="Search in company's internal documentation including Notion, Slack, and Jira using Glean. Use this tool only when necessary to find relevant information not available in the codebase. This tool returns a list of search queries to retrieve relevant documents from Glean, complementing codebase searches. Use sparingly for additional context beyond the codebase.",
                parameters={
                    "type": "object",
                    "properties": {
                        "queries": {
                            "type": "array",
                            "items": {
                                "type": "string",
                            },
                            "description": "List of 1-3 concise search queries.",
                            "minItems": 1,
                            "maxItems": 3,
                        },
                    },
                    "required": ["queries"],
                },
            ),
        ),
        "codebase-retrieval": ChatCompletionToolParam(
            type="function",
            function=FunctionDefinition(
                name="codebase-retrieval",
                description="Use this tool to request information from the codebase. It will return relevant snippets for the requested information. This tool is not longer available to use. DO NOT CALL IT.",
                parameters={
                    "type": "object",
                    "properties": {
                        "information_request": {
                            "type": "string",
                            "description": "A description of the information you need.",
                        },
                    },
                    "required": ["information_request"],
                },
            ),
        ),
        "dummy_tool": ChatCompletionToolParam(
            type="function",
            function=FunctionDefinition(
                name="dummy_tool",
                description="A placeholder tool. DO NOT CALL IT.",
                parameters={
                    "type": "object",
                    "properties": {
                        "input": {
                            "type": "string",
                            "description": "The input to the dummy tool.",
                        },
                    },
                    "required": ["input"],
                },
            ),
        ),
    }

    if tool_name not in openai_tool_definitions:
        raise NotImplementedError(f"Tool {tool_name} not implemented")
    return openai_tool_definitions[tool_name]


def to_stop_reason(finish_reason: str) -> StopReason:
    match finish_reason:
        case "stop":
            return StopReason.END_TURN
        case "length":
            return StopReason.MAX_TOKENS
        case "tool_calls":
            return StopReason.TOOL_USE_REQUESTED
        case _:
            return StopReason.REASON_UNSPECIFIED


_SAVE_FILE_PARTIAL_TOOL_USE = base.feature_flags.BoolFlag(
    "agents_save_file_partial_tool_use", False
)


def should_return_partial_tool_use_block(tool_use_block: ToolUseResponse) -> bool:
    """Check if a tool use block should be returned partially.

    This is similar to the function in anthropic_direct_client.py.
    """
    if not isinstance(tool_use_block.input, dict):
        return False

    # return partial tool use for str-replace-editor with str_replace command
    if (
        tool_use_block.tool_name == "str-replace-editor"
        and tool_use_block.input.get("command") == "str_replace"
    ):
        input_params = tool_use_block.input.keys()
        # only return partial tool use for flat schema
        return (
            "path" in input_params
            and any([param.startswith("old_str_") for param in input_params])
            and any([param.startswith("new_str_") for param in input_params])
        )

    # TODO(jeff): Remove feature flag once flipped on.
    global_context = base.feature_flags.get_global_context()
    if _SAVE_FILE_PARTIAL_TOOL_USE.get(global_context):
        # return partial tool use for save-file command
        if tool_use_block.tool_name == "save-file":
            input_params = tool_use_block.input.keys()
            # New clients know how to handle partial tool use with only `path`.
            return "path" in input_params

    return False


class OpenAIClient(ThirdPartyModelClient):
    """
    A class to interact with OpenAI for generating responses.
    """

    client_type = "openai_direct"

    def __init__(
        self,
        api_key: str,
        endpoint: Literal["openai", "xai", "xai_research"],
        model_name: str,
        temperature: float,
        max_output_tokens: int,
        prompt_cache_usage: str | None = None,
    ):
        """
        Initialize the OpenAI API with project details and model parameters.

        Args:
            api_key: The API key for the API.
            endpoint: The endpoint, one of "openai", "xai", "xai_research".
            model_name: The name of the model to use for generating responses.
            temperature: The temperature parameter for controlling the randomness of the
                responses.
            max_output_tokens: The maximum number of tokens to generate in the response.
        """
        self.model_name = model_name
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens
        self.supports_system_prompt = False
        self.supports_streaming = False
        self.supports_function_calling = False
        self.supports_temperature = False
        self.api_type = "chat"
        self.logger = structlog.get_logger().bind(
            client_type="openai", model_name=model_name
        )

        if endpoint == "openai":
            if model_name in OPENAI_RESPONSES_MODEL_ATTRIBUTES:
                raise ValueError(f"Model {model_name} is not supported yet.")
            self.base_url = None
            model_attributes = OPENAI_MODEL_ATTRIBUTES[model_name]
        elif endpoint == "xai":
            self.base_url = XAI_BASE_URL
            model_attributes = XAI_MODEL_ATTRIBUTES[model_name]
        elif endpoint == "xai_research":
            self.base_url = XAI_RESEARCH_BASE_URL
            model_attributes = XAI_RESEARCH_MODEL_ATTRIBUTES[model_name]

        self._token_counter = get_token_counter_by_prompt_formatter_name("binks-openai")
        self.supports_system_prompt = model_attributes["system_prompt"]
        self.supports_streaming = model_attributes["streaming"]
        self.supports_function_calling = model_attributes["function_calling"]
        self.supports_temperature = model_attributes["temperature"]

        if not self.supports_temperature:
            assert (
                self.temperature == 1
            ), f"{self.model_name} only supports temperature of 1."

        self.client = openai.OpenAI(
            base_url=self.base_url,
            api_key=api_key,
            max_retries=MAX_RETRIES,
            timeout=TIMEOUT,
        )

        self.logger = structlog.get_logger().bind(
            client_type=self.client_type, model_name=self.model_name
        )
        self.logger.info("OpenAIClient initialized")

        self.prompt_cache_usage = prompt_cache_usage
        self.prompt_caching_headers = {}

        if prompt_cache_usage:
            self.logger.warning(
                "OpenAIClient does not support prompt caching now.",
                prompt_cache_usage=prompt_cache_usage,
            )

    @override
    def generate_response_stream(
        self,
        model_caller: str,
        messages: list[tuple[str, str]] = [],  # Deprecated: use chat_history instead
        system_prompt: str | None = None,
        cur_message: RequestMessage = "",
        chat_history: list[Exchange] | None = None,
        tools: list[str] = [],
        tool_definitions: list[ToolDefinition] = [],
        tool_choice: ToolChoice | None = None,
        temperature: float | None = None,
        max_output_tokens: int | None = None,
        prefill: str | None = None,
        use_caching: bool = False,
        request_context: Any = None,
        yield_final_parameters: bool = False,
    ) -> Generator[ThirdPartyModelResponse, None, None]:
        """
        Generate a response based on the given message.

        Args:
            messages: List of (user message, assistant message) tuples. Deprecated in favor of `chat_history`.
            system_prompt: System prompt to guide model behavior.
            cur_message: The current user message to generate a response for.
            chat_history: Structured conversation history as a list of Exchange objects.
            tools: List of tool names available for the model to use.
            tool_definitions: List of tool definitions specifying tool interfaces.
            tool_choice: Specification for which tool the model should use.
            max_output_tokens: Maximum number of tokens in the generated response.
            temperature: Sampling temperature for response generation (0.0 to 1.0).
            prefill: Pre-filled text to start the response with.
            use_caching: Whether to cache and reuse responses.
            request_context: Optional request context containing information about the request, including session ID.
            yield_final_parameters: Whether to yield a response with the final parameters used for the request.

        Returns:
            Generator[ThirdPartyModelResponse]: Stream of response chunks.

        Raises:
            ResponseValidationError: If the response fails validation.
            Exception: If response generation fails.
        """
        self.logger.info(
            "OpenAIClient generating response", message_count=len(messages)
        )

        del request_context  # Not used

        if cur_message:
            try:
                input_tokens = self.count_tokens(
                    try_get_request_message_as_text(cur_message) or ""
                )
                PROMPT_INPUT_TOKENS.labels(
                    model_caller=model_caller,
                ).observe(input_tokens)
            except Exception as e:
                self.logger.warning("Failed to count input tokens", error=str(e))

        if use_caching and self.prompt_cache_usage:
            self.logger.info(
                "Using prompt caching",
                prompt_cache_usage=self.prompt_cache_usage,
            )
            # In the future, if OpenAI supports prompt caching, we would use it here

        if tool_choice:
            self.logger.error(
                "OpenAI client doesn't fully support tool_choice yet, ignoring"
            )

        if not self.supports_function_calling and (
            len(tools) > 0 or len(tool_definitions) > 0
        ):
            tool_names = tools + [tool_def.name for tool_def in tool_definitions]
            self.logger.warning(
                f"This model does not currently support tools, but got {tool_names}."
            )
            tools = []
            tool_definitions = []
        temperature = self.temperature if temperature is None else temperature
        if not self.supports_temperature:
            assert temperature == 1
        max_output_tokens = max_output_tokens or self.max_output_tokens

        self.logger.info(
            "OpenAIClient generating response", message_count=len(messages)
        )

        formatted_messages = []
        if system_prompt is not None:
            formatted_messages.append(
                {
                    "role": "system" if self.supports_system_prompt else "user",
                    "content": system_prompt,
                }
            )

        if chat_history is None:
            chat_history = [
                Exchange(request_message=message[0], response_text=message[1])
                for message in messages
            ]

        for message in chat_history:
            formatted_messages += format_request_message_as_nodes(
                message.request_message
            )
            formatted_messages.append(
                format_response_message_as_node(message.response_text)
            )

        formatted_messages += format_request_message_as_nodes(cur_message)

        if prefill:
            formatted_messages.append(
                {
                    "role": "assistant",
                    "content": prefill,
                }
            )
        all_tool_definitions = [
            get_tool_definition(tool_name) for tool_name in tools
        ] + [
            ChatCompletionToolParam(
                type="function",
                function=FunctionDefinition(
                    name=tool_def.name,
                    description=tool_def.description,
                    parameters=patch_mcp_input_schema_for_openai(
                        json.loads(tool_def.input_schema_json)
                    ),
                ),
            )
            for tool_def in tool_definitions
        ]
        all_tool_definitions = all_tool_definitions or OPENAI_NOT_GIVEN

        start_time = time.time()
        with OPENAI_RESPONSE_LATENCY.labels(
            model=self.model_name,
        ).time():
            if self.supports_streaming:
                self.logger.info(
                    "OpenAIClient streaming response",
                    message_count=len(formatted_messages),
                )
                first_message_time = None
                if yield_final_parameters:
                    yield ThirdPartyModelResponse(
                        text="",
                        final_parameters={
                            "model": self.model_name,
                            "messages": formatted_messages,
                            "temperature": temperature,
                            "max_tokens": max_output_tokens,
                            "tools": all_tool_definitions
                            if all_tool_definitions is not OPENAI_NOT_GIVEN
                            else None,
                            "stream": True,
                        },
                    )
                try:
                    with self.client.chat.completions.create(
                        model=self.model_name,
                        messages=formatted_messages,
                        temperature=temperature,
                        max_tokens=max_output_tokens,
                        tools=all_tool_definitions,
                        stream=True,
                    ) as response_iterator:
                        assert isinstance(response_iterator, Iterator)

                        tool_calls = {}
                        stop_reason = StopReason.REASON_UNSPECIFIED
                        current_mode = None
                        for chunk in response_iterator:
                            if not chunk.choices:
                                continue

                            if chunk.choices[0].delta.content:
                                chunk_contents = chunk.choices[0].delta.content
                                if current_mode and current_mode != "content":
                                    yield ThirdPartyModelResponse("</think>")
                                current_mode = "content"
                            elif (
                                hasattr(chunk.choices[0].delta, "reasoning_content")
                                and chunk.choices[0].delta.reasoning_content  # type: ignore
                            ):
                                chunk_contents = chunk.choices[
                                    0
                                ].delta.reasoning_content  # type: ignore
                                if current_mode and current_mode != "reasoning":
                                    yield ThirdPartyModelResponse("<think>")
                                current_mode = "reasoning"
                            else:
                                chunk_contents = ""

                            if chunk_contents:
                                # Track first token latency
                                if first_message_time is None:
                                    first_message_time = time.time()
                                    first_token_latency = (
                                        first_message_time - start_time
                                    )
                                    OPENAI_FIRST_TOKEN_LATENCY.labels(
                                        model=self.model_name,
                                    ).observe(first_token_latency)

                                    if (
                                        first_token_latency
                                        > FIRST_TOKEN_HIGH_LATENCY_THRESHOLD
                                    ):
                                        self.logger.warning(
                                            "High first token latency",
                                            first_token_latency=first_token_latency,
                                            model=self.model_name,
                                        )

                                # Track output text size
                                _output_text_chars_counter.labels(
                                    model=self.model_name,
                                ).inc(len(chunk_contents))

                                # Track event type
                                _event_type_counter.labels(
                                    model=self.model_name,
                                    event_type="text",
                                ).inc()

                                yield ThirdPartyModelResponse(chunk_contents)

                            for delta_tool_call in (
                                chunk.choices[0].delta.tool_calls or []
                            ):
                                if delta_tool_call.index not in tool_calls:
                                    tool_calls[delta_tool_call.index] = {
                                        "id": None,
                                        "name": None,
                                        "argument_string": "",
                                        "start_sent": False,
                                    }
                                tool_call = tool_calls[delta_tool_call.index]

                                if id_ := delta_tool_call.id:
                                    tool_call["id"] = id_
                                if function := delta_tool_call.function:
                                    if name := function.name:
                                        tool_call["name"] = name
                                    if arguments := function.arguments:
                                        tool_call["argument_string"] += arguments
                                if (
                                    not tool_call["start_sent"]
                                    and tool_call["name"]
                                    and tool_call["id"]
                                ):
                                    tool_call["start_sent"] = True
                                    # Track tool use blocks
                                    _output_tool_use_blocks.labels(
                                        model=self.model_name,
                                    ).inc()

                                    # Track event type
                                    _event_type_counter.labels(
                                        model=self.model_name,
                                        event_type="tool_use_start",
                                    ).inc()

                                    yield ThirdPartyModelResponse(
                                        text="",
                                        tool_use_start=ToolUseStart(
                                            tool_name=tool_call["name"],
                                            tool_use_id=tool_call["id"],
                                        ),
                                    )

                            # Deferring the yield until we're outside the loop (a) de-risks
                            # sending end of stream before processing all parts of all chunks
                            # and (b) gives us a default finish_reason in var outside the loop
                            if chunk.choices[0].finish_reason:
                                stop_reason = to_stop_reason(
                                    chunk.choices[0].finish_reason
                                )

                    # Process completed tool calls after stream ends
                    for tool_call_data in tool_calls.values():
                        # Ensure tool call was properly started and has necessary info
                        if tool_call_data.get("id") and tool_call_data.get("name"):
                            try:
                                # Ensure argument_string is not empty before parsing
                                argument_string = tool_call_data.get(
                                    "argument_string", ""
                                )
                                if not argument_string:
                                    # Handle cases where arguments might be empty (e.g., {})
                                    parsed_args = {}
                                else:
                                    parsed_args = json.loads(argument_string)

                                # Track tool use blocks
                                _output_tool_use_blocks.labels(
                                    model=self.model_name,
                                ).inc()

                                # Track event type
                                _event_type_counter.labels(
                                    model=self.model_name,
                                    event_type="tool_use",
                                ).inc()

                                # TODO(jeff): add is_partial flag, but it seems like
                                # the should_return_partial_tool_use_block check lower
                                # down doesn't do anything?
                                tool_use_response = ToolUseResponse(
                                    tool_name=tool_call_data["name"],
                                    input=parsed_args,
                                    tool_use_id=tool_call_data["id"],
                                )

                                # Check if we should return partial tool use
                                if should_return_partial_tool_use_block(
                                    tool_use_response
                                ):
                                    self.logger.info(
                                        "Returning partial tool use block",
                                        tool_name=tool_use_response.tool_name,
                                        tool_use_id=tool_use_response.tool_use_id,
                                    )

                                yield ThirdPartyModelResponse(
                                    text="",
                                    tool_use=tool_use_response,
                                )
                            except json.JSONDecodeError as e:
                                # Get the argument string again to ensure it's bound
                                arg_str = tool_call_data.get("argument_string", "")
                                self.logger.error(
                                    "Failed to parse tool arguments JSON",
                                    tool_name=tool_call_data["name"],
                                    tool_use_id=tool_call_data["id"],
                                    arguments=arg_str,
                                    error=str(e),
                                )
                                # Optionally yield an error response or raise? For now, just log.
                            except (
                                Exception
                            ) as e:  # Catch other potential errors during processing
                                self.logger.error(
                                    "Error processing tool call",
                                    tool_name=tool_call_data.get("name"),
                                    tool_use_id=tool_call_data.get("id"),
                                    error=str(e),
                                )

                        # OpenAI streaming doesn't provide token usage in the stream
                        # We'll estimate based on the content length
                        output_tokens = None

                        # In a future update, we could track token usage more accurately
                        # by counting tokens in the generated text

                        # Track event type for end of stream
                        _event_type_counter.labels(
                            model=self.model_name,
                            event_type="end_of_stream",
                        ).inc()

                        yield ThirdPartyModelResponse(
                            text="",
                            end_of_stream=EndOfStream(
                                stop_reason=stop_reason,
                                output_tokens=output_tokens,
                            ),
                        )
                except (
                    RateLimitError,
                    InternalServerError,
                    APIConnectionError,
                    BadRequestError,
                    APIStatusError,
                    Exception,
                ) as e:
                    self._handle_openai_error(e, is_streaming=True)

            if not self.supports_streaming:
                try:
                    if yield_final_parameters:
                        yield ThirdPartyModelResponse(
                            text="",
                            final_parameters={
                                "model": self.model_name,
                                "messages": formatted_messages,
                                "temperature": temperature,
                                "max_completion_tokens": max_output_tokens,
                                "tools": all_tool_definitions
                                if all_tool_definitions is not OPENAI_NOT_GIVEN
                                else None,
                                "stream": False,
                            },
                        )
                    response = self.client.chat.completions.create(
                        model=self.model_name,
                        messages=formatted_messages,
                        temperature=temperature,
                        max_completion_tokens=max_output_tokens,
                        tools=all_tool_definitions,
                        stream=False,
                    )
                    text = response.choices[0].message.content or ""
                    # TODO(jeff): add is_partial flag, but it seems like
                    # the should_return_partial_tool_use_block check lower down
                    # doesn't do anything?
                    tool_uses = [
                        ToolUseResponse(
                            tool_name=tool_call.function.name,
                            input=json.loads(tool_call.function.arguments),
                            tool_use_id=tool_call.id,
                        )
                        for tool_call in response.choices[0].message.tool_calls or []
                    ]
                    # Track event type for text
                    _event_type_counter.labels(
                        model=self.model_name,
                        event_type="text",
                    ).inc()

                    # Track output text size
                    _output_text_chars_counter.labels(
                        model=self.model_name,
                    ).inc(len(text))

                    yield ThirdPartyModelResponse(text)
                    for tool_use in tool_uses:
                        # Check if we should return partial tool use
                        if should_return_partial_tool_use_block(tool_use):
                            self.logger.info(
                                "Returning partial tool use block",
                                tool_name=tool_use.tool_name,
                                tool_use_id=tool_use.tool_use_id,
                            )
                        # Track tool use blocks
                        _output_tool_use_blocks.labels(
                            model=self.model_name,
                        ).inc()

                        # Track event type
                        _event_type_counter.labels(
                            model=self.model_name,
                            event_type="tool_use",
                        ).inc()

                        yield ThirdPartyModelResponse(text="", tool_use=tool_use)
                    # Track output tokens if available
                    output_tokens = None
                    if (
                        hasattr(response, "usage")
                        and response.usage
                        and hasattr(response.usage, "completion_tokens")
                    ):
                        output_tokens = response.usage.completion_tokens
                        if output_tokens:
                            _output_tokens_histogram.labels(
                                model_caller=model_caller,
                            ).observe(output_tokens)

                    _event_type_counter.labels(
                        model=self.model_name,
                        event_type="end_of_stream",
                    ).inc()

                    yield ThirdPartyModelResponse(
                        text="",
                        end_of_stream=EndOfStream(
                            stop_reason=to_stop_reason(
                                response.choices[0].finish_reason
                            ),
                            output_tokens=output_tokens,
                        ),
                    )
                except BadRequestError as e:
                    self.logger.error("OpenAI BadRequestError: %s", e)
                    _error_counter.labels(
                        model=self.model_name,
                        error_type="bad_request",
                    ).inc()
                    if hasattr(e, "code") and e.code == "invalid_prompt":
                        yield ThirdPartyModelResponse(e.message)
                    else:
                        raise InvalidArgumentRpcError(f"OpenAI bad request: {e}")
                except (
                    RateLimitError,
                    InternalServerError,
                    APIConnectionError,
                    APIStatusError,
                    Exception,
                ) as e:
                    self._handle_openai_error(e, is_streaming=False)

    @override
    def count_tokens(self, message: str) -> int:
        """
        Count the number of tokens in the given message.

        Args:
            message (str): The message to count the tokens for.

        Returns:
            int: An estimate of the number of tokens in the given message.

        Raises:
            ResponseValidationError: Raises an exception if the response is not valid.
            Exception: Raises an exception if the response is not valid.
        """
        _token_count_calls.inc()
        # Track token count in metrics
        token_count = self._token_counter.count_tokens(message)
        # Add a 10% buffer due to potential tokenizer inaccuracy, similar to Anthropic client
        return round(token_count * 1.1)

    @override
    def token_counter(self) -> TokenCounter:
        return self._token_counter

    def _handle_openai_error(self, error, is_streaming=False):
        """Centralized error handling for OpenAI API errors.

        Args:
            error: The exception raised by the OpenAI API
            is_streaming: Whether the error occurred during streaming

        Raises:
            UnavailableRpcError: For rate limits, server errors, connection issues, or API status errors
            InvalidArgumentRpcError: For bad requests
        """
        context = "stream" if is_streaming else "processing"

        if isinstance(error, RateLimitError):
            self.logger.warning(f"OpenAI RateLimitError: {error}")
            _error_counter.labels(
                model=self.model_name,
                error_type="rate_limit",
            ).inc()
            # TODO: Check if specific OpenAI RateLimitError maps to ResourceExhaustedRpcError
            raise UnavailableRpcError(f"Rate limit exceeded: {error}")

        elif isinstance(error, InternalServerError):
            self.logger.warning(f"OpenAI InternalServerError: {error}")
            _error_counter.labels(
                model=self.model_name,
                error_type="internal_server",
            ).inc()
            raise UnavailableRpcError(f"OpenAI internal server error: {error}")

        elif isinstance(error, APIConnectionError):
            self.logger.warning(f"OpenAI APIConnectionError: {error}")
            _error_counter.labels(
                model=self.model_name,
                error_type="connection",
            ).inc()
            raise UnavailableRpcError(f"OpenAI connection error: {error}")

        elif isinstance(error, APIStatusError):
            self.logger.warning(
                f"OpenAI APIStatusError: {error.status_code} {error.message}"
            )
            _error_counter.labels(
                model=self.model_name,
                error_type="api_status",
            ).inc()
            # TODO: Check if specific OpenAI APIStatusError maps to ResourceExhaustedRpcError
            raise UnavailableRpcError(
                f"OpenAI API status error {error.status_code}: {error.message}"
            )

        elif isinstance(error, BadRequestError):
            self.logger.error(f"OpenAI BadRequestError: {error}")
            _error_counter.labels(
                model=self.model_name,
                error_type="bad_request",
            ).inc()
            # Note: Special handling for invalid_prompt is done in the non-streaming path
            # TODO: Check if specific OpenAI BadRequestError maps to ResourceExhaustedRpcError (e.g., quota)
            raise InvalidArgumentRpcError(f"OpenAI bad request: {error}")

        else:  # Catch any other unexpected errors
            self.logger.exception(f"Unexpected error during OpenAI {context}")
            _error_counter.labels(
                model=self.model_name,
                error_type="unexpected",
            ).inc()
            raise UnavailableRpcError(f"Unexpected error: {error}")


def format_request_message_as_nodes(request_message: RequestMessage):
    message_text = try_get_request_message_as_text(request_message)
    if message_text is not None:
        return [
            {
                "role": "user",
                "content": message_text,
            }
        ]
    # This check might be redundant if RequestMessage is always list-like
    # but keeping it for safety, mirroring original function.
    if isinstance(request_message, str):
        raise ValueError("request_message should not be a plain string here.")

    # Assert that request_message is iterable
    # Note: In production, this would be google._upb._message.RepeatedCompositeContainer
    assert iter(request_message) is not None

    formatted_messages = []
    for node in request_message:
        if node.type == ChatRequestNodeType.TEXT:
            assert node.text_node is not None
            assert node.tool_result_node is None
            formatted_messages.append(
                {
                    "role": "user",
                    "content": node.text_node.content,
                }
            )
        elif node.type == ChatRequestNodeType.TOOL_RESULT:
            assert node.tool_result_node is not None
            assert node.text_node is None
            formatted_messages.append(
                {
                    "role": "tool",
                    "tool_call_id": node.tool_result_node.tool_use_id,
                    "content": node.tool_result_node.content,
                }
            )
        elif node.type == ChatRequestNodeType.IMAGE:
            assert node.image_node is not None
            if node.image_node.format == ImageFormatType.IMAGE_FORMAT_UNSPECIFIED:
                supported_formats = [
                    format_type.name
                    for format_type in ImageFormatType
                    if format_type != ImageFormatType.IMAGE_FORMAT_UNSPECIFIED
                ]
                raise ValueError(
                    f"Image format must be specified. Supported formats: {', '.join(supported_formats)}"
                )

            media_type = {
                ImageFormatType.PNG: "image/png",
                ImageFormatType.JPEG: "image/jpeg",
                ImageFormatType.GIF: "image/gif",
                ImageFormatType.WEBP: "image/webp",
            }.get(node.image_node.format)

            if media_type is None:
                raise ValueError(f"Unsupported image format: {node.image_node.format}")

            # API reference: https://platform.openai.com/docs/guides/vision#uploading-base64-encoded-images
            formatted_messages.append(
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:{media_type};base64,{node.image_node.image_data}"
                            },
                        }
                    ],
                }
            )
    return formatted_messages


def format_response_message_as_node(response_text: ResponseMessage):
    if isinstance(response_text, str):
        return {
            "role": "assistant",
            "content": response_text,
        }
    assert isinstance(response_text, list)

    content = ""
    tool_calls = []
    response_nodes = response_text
    for node in response_nodes:
        if node.type == ChatResultNodeType.RAW_RESPONSE:
            is_whitespace_only = node.content.strip() == ""
            if not is_whitespace_only or len(response_nodes) == 1:
                assert node.tool_use is None
                content += node.content
        elif node.type == ChatResultNodeType.TOOL_USE:
            assert node.tool_use is not None
            tool_calls.append(
                {
                    "type": "function",
                    "id": node.tool_use.tool_use_id,
                    "function": {
                        "name": node.tool_use.name,
                        "arguments": json.dumps(node.tool_use.input),
                    },
                }
            )
    return {
        "role": "assistant",
        "content": content,
        "tool_calls": tool_calls or None,
    }


def patch_mcp_input_schema_for_openai(input_schema: dict[str, Any]):
    if input_schema["type"] == "array" and "items" not in input_schema:
        input_schema["items"] = {"type": "string"}
    if input_schema["type"] == "object":
        for property_ in input_schema["properties"].values():
            patch_mcp_input_schema_for_openai(property_)
    return input_schema
