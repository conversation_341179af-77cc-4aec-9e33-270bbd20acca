load("@python_pip//:requirements.bzl", "requirement")
load("//tools/bzl:python.bzl", "py_library", "pytest_test")

py_library(
    name = "third_party_model_client",
    srcs = ["third_party_model_client.py"],
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/prompt_format:common",
        "//base/third_party_clients/token_counter",
    ],
)

py_library(
    name = "anthropic_tool_response_handler",
    srcs = ["anthropic_tool_response_handler.py"],
    deps = [
        ":third_party_model_client",
        requirement("anthropic"),
        requirement("structlog"),
        requirement("prometheus_client"),
    ],
)

pytest_test(
    name = "anthropic_direct_client_test",
    srcs = ["anthropic_direct_client_test.py"],
    deps = [
        ":clients",
        "//base/prompt_format:common",
    ],
)

pytest_test(
    name = "anthropic_vertexai_client_test",
    srcs = ["anthropic_vertexai_client_test.py"],
    deps = [
        ":clients",
    ],
)

pytest_test(
    name = "google_genai_client_test",
    srcs = ["google_genai_client_test.py"],
    deps = [
        ":clients",
    ],
)

pytest_test(
    name = "openai_client_test",
    srcs = ["openai_client_test.py"],
    deps = [
        ":clients",
    ],
)

pytest_test(
    name = "third_party_model_client_test",
    srcs = ["third_party_model_client_test.py"],
    deps = [
        ":third_party_model_client",
    ],
)

py_library(
    name = "clients",
    srcs = [
        "anthropic_direct_client.py",
        "anthropic_vertexai_client.py",
        "common.py",
        "fireworks_client.py",
        "google_genai_client.py",
        "openai_client.py",
        "vertexai_client.py",
    ],
    visibility = [
        "//base/stream_processor:__subpackages__",
        "//infra/svc/slack_feedback_summarizer:__subpackages__",
        "//models:__subpackages__",
        "//services:__subpackages__",
    ],
    deps = [
        ":anthropic_tool_response_handler",
        ":third_party_model_client",
        "//base/error_details:error_details_py_proto",
        "//base/feature_flags:feature_flags_py",
        "//base/prompt_format_chat",
        "//base/third_party_clients/utils:google_genai_schema_adapter_utils",
        "//base/tokenizers",
        requirement("anthropic"),
        requirement("fireworks-ai"),
        requirement("google-auth"),
        requirement("google-cloud-aiplatform"),
        requirement("google-genai"),
        requirement("Pillow"),  # Added for image processing to calculate image tokens
        requirement("openai"),
        requirement("prometheus_client"),
        requirement("sentencepiece"),  # Implicit dependency from `google-cloud-aiplatform[tokenization]`
        requirement("structlog"),
    ],
)
