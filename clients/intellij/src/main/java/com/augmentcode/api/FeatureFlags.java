package com.augmentcode.api;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;

public class FeatureFlags {
  // Default max size for files to be uploaded. The backend is expected to
  // override this through a feature flag.
  public static final int DEFAULT_MAX_UPLOAD_SIZE_BYTES = 128 * 1024;
  public static final int DEFAULT_SMALL_SYNC_THRESHOLD = 15;
  public static final int DEFAULT_BIG_SYNC_THRESHOLD = 1000;
  public static final int DEFAULT_USER_GUIDELINES_LENGTH_LIMIT = 2000;
  public static final int DEFAULT_WORKSPACE_GUIDELINES_LENGTH_LIMIT = 2000;

  public boolean enableIntellijChat;
  public boolean enableWorkspaceManagerUi;
  public String intellijChatMinVersion;
  public boolean bypassLanguageFilter;
  public String intellijForceCompletionMinVersion;
  public String additionalChatModels;
  public int maxUploadSizeBytes = DEFAULT_MAX_UPLOAD_SIZE_BYTES;
  public boolean enableExternalSourcesInChat;
  public String intellijShareMinVersion;
  public boolean enableAgentAutoMode;
  public int smallSyncThreshold = DEFAULT_SMALL_SYNC_THRESHOLD;
  public int bigSyncThreshold = DEFAULT_BIG_SYNC_THRESHOLD;
  public boolean intellijShowSummary;
  public String intellijNewThreadsMenuMinVersion;
  public boolean enableNewThreadsList;
  public String intellijCompletionsHistoryMinVersion;
  public String intellijSmartPasteMinVersion;
  public String smartPastePrecomputeMode;
  public String intellijChatWithToolsMinVersion;
  public String intellijDesignSystemRichTextEditorMinVersion;
  public String intellijEnableChatMermaidDiagramsMinVersion;
  public String intellijAgentModeMinVersion;
  public String intellijBackgroundAgentsMinVersion;
  public String intellijAskForSyncPermissionMinVersion;
  public String intellijChatMultimodalMinVersion;
  public String memoriesParams;
  public boolean enableGuidelines;
  public boolean intellijEnableUserGuidelines;
  public boolean intellijUserGuidelinesInSettings;
  public boolean intellijEnableWorkspaceGuidelines;
  public String intellijPreferenceCollectionAllowedMinVersion;
  public boolean intellijEnableHomespunGitignore;
  public boolean intellijPromptEnhancerEnabled;
  public boolean intellijEdtFreezeDetectionEnabled;
  public boolean intellijEnableSentry;
  public double intellijWebviewErrorSamplingRate = 0.0;
  public double intellijPluginErrorSamplingRate = 0.0;
  public double intellijWebviewTraceSamplingRate = 0.0;
  public double intellijPluginTraceSamplingRate = 0.0;
  public boolean intellijEnableWebviewPerformanceMonitoring;
  public String eloModelConfiguration;
  public boolean intellijIndexingV3Enabled;

  // Sidecar feature flags
  public String agentEditTool;
  public int agentEditToolMinViewSize;
  public String agentEditToolSchemaType;
  public boolean agentEditToolEnableFuzzyMatching;
  public String agentEditToolFuzzyMatchSuccessMessage;
  public int agentEditToolFuzzyMatchMaxDiff;
  public double agentEditToolFuzzyMatchMaxDiffRatio;
  public int agentEditToolFuzzyMatchMinAllMatchStreakBetweenDiffs;
  public boolean agentEditToolInstructionsReminder;
  public boolean agentEditToolShowResultSnippet;
  public int agentEditToolMaxLines;
  public boolean agentSaveFileToolInstructionsReminder;
  public boolean enableChatWithTools;
  public boolean enableAgentMode;
  public boolean grepSearchToolEnable;
  public int grepSearchToolTimelimitSec;
  public int grepSearchToolOutputCharsLimit;
  public int grepSearchToolNumContextLines;
  public String historySummaryMinVersion;
  public int historySummaryMaxChars;
  public int historySummaryLowerChars;

  public int userGuidelinesLengthLimit = DEFAULT_USER_GUIDELINES_LENGTH_LIMIT;
  public int workspaceGuidelinesLengthLimit = DEFAULT_WORKSPACE_GUIDELINES_LENGTH_LIMIT;

  private static final Gson GSON = new Gson();

  public Map<String, String> additionalChatModelsMap() {
    if (additionalChatModels == null || additionalChatModels.isEmpty()) {
      return Collections.emptyMap();
    }

    Map<String, String> nullableResult;
    try {
      nullableResult = GSON.fromJson(
        additionalChatModels,
        new TypeToken<Map<String, String>>(){}.getType()
      );
      if (nullableResult == null) {
        return Collections.emptyMap();
      }
    } catch (JsonSyntaxException e) {
      return Collections.emptyMap();
    }

    return nullableResult.entrySet().stream()
      .collect(Collectors.toMap(
        Map.Entry::getKey,
        e -> e.getValue() == null ? "null" : e.getValue(),
        (v1, v2) -> v2,  // merge function in case of duplicate keys
        LinkedHashMap::new  // preserve order
      ));
  }
}
