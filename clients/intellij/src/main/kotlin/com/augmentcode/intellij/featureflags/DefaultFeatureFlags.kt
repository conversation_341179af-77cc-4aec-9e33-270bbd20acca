package com.augmentcode.intellij.featureflags

import com.augmentcode.api.EloModelConfiguration

val DefaultFeatureFlags =
  FeatureFlags(
    chatMultimodalEnabled = false,
    enableCompletionsHistory = false,
    agentModeEnabled = false,
    guidelinesEnabled = false,
    userGuidelinesEnabled = false,
    promptEnhancerEnabled = false,
    enableChatMermaidDiagrams = false,
    enableDesignSystemRichTextEditor = false,
    smartPastePrecomputeMode = "visible-hover",
    enableSmartPaste = false,
    useNewThreadsMenu = false,
    enableNewThreadsList = false,
    enableExternalSourcesInChat = false,
    shareServiceEnabled = false,
    enableHomespunGitignore = false,
    memoriesParams = "{}",
    userGuidelinesLengthLimit = com.augmentcode.api.FeatureFlags.DEFAULT_USER_GUIDELINES_LENGTH_LIMIT,
    workspaceGuidelinesLengthLimit = com.augmentcode.api.FeatureFlags.DEFAULT_WORKSPACE_GUIDELINES_LENGTH_LIMIT,
    userGuidelinesInSettingsEnabled = false,
    chatWithToolsEnabled = false,
    sentryEnabled = false,
    webviewErrorSamplingRate = 0.0,
    pluginErrorSamplingRate = 0.0,
    webviewTraceSamplingRate = 0.0,
    pluginTraceSamplingRate = 0.0,
    maxUploadSizeBytes = com.augmentcode.api.FeatureFlags.DEFAULT_MAX_UPLOAD_SIZE_BYTES,
    bypassLanguageFilter = false,
    additionalChatModels = emptyMap(),
    enableAgentAutoMode = false,
    preferenceCollectionAllowed = false,
    eloModelConfiguration =
      EloModelConfiguration().apply {
        highPriorityModels = emptyList()
        regularBattleModels = emptyList()
        highPriorityThreshold = 0.5
      },
    enableEdtFreezeDetection = false,
    indexingV3Enabled = false,
    enableWebviewPerformanceMonitoring = false,
  )
