package com.augmentcode.intellij.actions

import com.augmentcode.intellij.index.AugmentLocalIndex
import com.augmentcode.intellij.syncing.AugmentRemoteSyncingManager
import com.augmentcode.intellij.workspacemanagement.coordination.WorkspaceCoordinatorService
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.project.DumbAware
import kotlinx.coroutines.runBlocking

/**
 * An action that triggers a rebuild of the Augment local index.
 * This can be useful for debugging or when the index gets out of sync.
 */
class ReindexAction : AnActionWithCtx(), DumbAware {
  override fun actionPerformed(e: AnActionEvent) {
    val project = e.project ?: return

    runBlocking {
      AugmentRemoteSyncingManager.getInstance(project).reset()
    }

    AugmentLocalIndex.requestRebuild()

    // This function will kick off the v3 indexing pipeline if enabled.
    WorkspaceCoordinatorService.getInstance(project).syncWorkspace()
  }
}
