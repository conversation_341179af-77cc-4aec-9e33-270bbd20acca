[versions]
annotations = "24.1.0"
codeownersEnforcerRules = "1.5.1"
gson = "2.10.1"
# versions used for 2024.3.3 from https://www.jetbrains.com/legal/third-party-software
ktor = "2.3.12"
kotlin = "1.9.22"
kotlinx = "1.8.0"
mockk = "1.13.12"
caffeine = "3.1.8"
changelog = "2.2.0"
gradleIntelliJPlugin = "2.2.1"
qodana = "0.1.13"
kover = "0.9.1"
protoPlugin = "0.9.4"
retryPlugin = "1.6.2"
# same as https://github.com/JetBrains/intellij-community/blob/master/platform/execution-process-mediator/common/build.gradle.kts
grpcVersion = "1.57.2"
grpcKotlinVersion = "1.4.0"
protobufVersion = "3.24.4"
coroutinesTestVersion = "1.9.0"
# Sentry SDK version
sentryVersion = "8.12.0"
sentryGradlePlugin = "5.5.0"

[libraries]
annotations = { group = "org.jetbrains", name = "annotations", version.ref = "annotations" }
caffeine = { group = "com.github.ben-manes.caffeine", name = "caffeine", version.ref = "caffeine" }
codeownersEnforcerRules = { group = "nl.basjes.maven.enforcer.codeowners", name="codeowners-enforcer-rules", version.ref = "codeownersEnforcerRules" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }
ktorCore = { group = "io.ktor", name = "ktor-client-core", version.ref = "ktor" }
ktorMock = { group = "io.ktor", name = "ktor-client-mock", version.ref = "ktor" }
ktorCIO = { group = "io.ktor", name = "ktor-client-cio", version.ref = "ktor" }
mockk = { group = "io.mockk", name = "mockk", version.ref = "mockk" }
protoCompiler = { group = "com.google.protobuf", name = "protoc", version.ref  = "protobufVersion" }
protoKotlin = { group = "com.google.protobuf", name = "protobuf-kotlin", version.ref  = "protobufVersion" }
protoJavaUtil = { group = "com.google.protobuf", name = "protobuf-java-util", version.ref  = "protobufVersion" }
grpc = { group = "io.grpc", name = "grpc-all", version.ref  = "grpcVersion" }
grpcKotlin = { group = "io.grpc", name = "grpc-kotlin-stub", version.ref  = "grpcKotlinVersion" }
protocGenJava = { group = "io.grpc", name = "protoc-gen-grpc-java", version.ref  = "grpcVersion" }
protocGenKotlin = { group = "io.grpc", name = "protoc-gen-grpc-kotlin", version.ref  = "grpcKotlinVersion" }
coroutinesTest = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-test", version.ref = "coroutinesTestVersion" }
# Sentry dependencies
sentry = { group = "io.sentry", name = "sentry", version.ref = "sentryVersion" }
sentryKotlin = { group = "io.sentry", name = "sentry-kotlin-extensions", version.ref = "sentryVersion" }

[plugins]
changelog = { id = "org.jetbrains.changelog", version.ref = "changelog" }
gradleIntelliJPlugin = { id = "org.jetbrains.intellij.platform", version.ref = "gradleIntelliJPlugin" }
kotlin = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kover = { id = "org.jetbrains.kotlinx.kover", version.ref = "kover" }
qodana = { id = "org.jetbrains.qodana", version.ref = "qodana" }
proto = { id = "com.google.protobuf", version.ref = "protoPlugin" }
retry = { id = "org.gradle.test-retry", version.ref = "retryPlugin" }
sentryGradle = { id = "io.sentry.jvm.gradle", version.ref = "sentryGradlePlugin" }
