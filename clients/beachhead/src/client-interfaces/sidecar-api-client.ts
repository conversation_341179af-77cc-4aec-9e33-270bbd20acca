import {
    Chat<PERSON><PERSON>,
    ChatRequestNode,
    Exchange,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { IAPIClient } from "@augment-internal/sidecar-libs/src/client-interfaces/api-client";
import {
    AgentCodebaseRetrievalOptions,
    AgentCodebaseRetrievalResult,
    AgentRequestEvent,
    AgentSessionEvent,
    ChatResult,
    RemoteAgentSessionEvent,
    ToolUseRequestEvent,
} from "@augment-internal/sidecar-libs/src/client-interfaces/api-client-types";
import { RemoteToolId, ToolDefinition } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { APIServer } from "../augment-api";
import { BeachheadWorkspaceManager } from "../workspace-manager";

/**
 * This class is a light wrapper around our APIServer to meet the IAPIClient
 * interface. This allows components in the sidecar (such as the tools host) to
 * access the augment APIs.
 */
export class SidecarAPIClient implements IAPIClient {
    constructor(
        private _apiServer: APIServer,
        private _workspaceManager: BeachheadWorkspaceManager
    ) {}

    async chatStream(
        message: string,
        requestId: string,
        chatHistory: Exchange[],
        toolDefinitions: ToolDefinition[],
        requestNodes: ChatRequestNode[],
        mode: ChatMode,
        modelId: string | undefined
    ): Promise<AsyncIterable<ChatResult>> {
        const chatResultIter = await this._apiServer.chatStream(
            requestId, // requestId
            message, // message
            chatHistory, // chatHistory
            { checkpointId: undefined, addedBlobs: [], deletedBlobs: [] }, // blobs
            [], // userGuidedBlobs
            [], // externalSourceIds
            modelId ?? undefined, // model (if `undefined`, backend picks model based on `mode`)
            { workingDirectory: [], commits: [] }, // vcsChange
            [], // recentChanges
            undefined, // contextCodeExchangeRequestId
            undefined, // selectedCode
            undefined, // prefix
            undefined, // suffix
            undefined, // pathName
            undefined, // language
            undefined, // sessionId
            undefined, // disableAutoExternalSources
            undefined, // userGuidelines
            undefined, // workspaceGuidelines
            toolDefinitions, // toolDefinitions
            requestNodes, // nodes
            mode, // mode
            undefined, // agentMemories
            undefined // personaType
        );

        return chatResultIter;
    }

    public agentCodebaseRetrieval(
        toolRequestId: string,
        informationRequest: string,
        chatHistory: Exchange[],
        maxOutputLength: number,
        options?: AgentCodebaseRetrievalOptions,
        signal?: AbortSignal
    ): Promise<AgentCodebaseRetrievalResult> {
        const blobs = this._workspaceManager.getCheckpoint() ?? {
            checkpointId: undefined,
            addedBlobs: [],
            deletedBlobs: [],
        };
        return this._apiServer.agentCodebaseRetrieval(
            toolRequestId,
            informationRequest,
            blobs,
            chatHistory,
            maxOutputLength,
            options,
            signal
        );
    }

    // delegate tool use event reporter to apiserver
    logToolUseRequestEvent(events: ToolUseRequestEvent[]): Promise<void> {
        return this._apiServer.logToolUseRequestEvent(events);
    }

    logAgentRequestEvent(events: AgentRequestEvent[]): Promise<void> {
        return this._apiServer.logAgentRequestEvent(events);
    }

    logAgentSessionEvent(events: AgentSessionEvent[]): Promise<void> {
        return this._apiServer.logAgentSessionEvent(events);
    }

    logRemoteAgentSessionEvent(events: RemoteAgentSessionEvent[]): Promise<void> {
        return this._apiServer.logRemoteAgentSessionEvent(events);
    }

    checkToolSafety(toolId: RemoteToolId, toolInputJson: string): Promise<boolean> {
        return this._apiServer.checkToolSafety(toolId, toolInputJson);
    }
}
