import {
    ChatRequestN<PERSON>,
    ChatRequestNodeType,
    ChatResultNodeType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ToolUseResponse } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import { AgentState } from "../../agent_loop/state";
import { ChatResult } from "../../augment-api";
import { FileChangeType, RemoteAgentStatus } from "../../remote-agent-manager/types";

const message: ChatRequestNode[] = [
    {
        id: 1,
        type: ChatRequestNodeType.TEXT,
        text_node: {
            content: "Hello",
        },
    },
];

const chunks: ChatResult[] = [
    { text: "Hello " },
    { text: "there" },
    {
        text: "",
        nodes: [
            {
                id: 1,
                type: ChatResultNodeType.RAW_RESPONSE,
                content: "Hello there",
            },
        ],
    },
    {
        text: "",
        nodes: [
            {
                id: 2,
                type: ChatResultNodeType.TOOL_USE,
                tool_use: {
                    tool_use_id: "tool-1",
                    tool_name: "test-tool",
                    input_json: '{"param": "value"}',
                },
                content: "",
            },
        ],
    },
];

const toolResult: ToolUseResponse = {
    text: "Tool result",
    isError: false,
    requestId: "tool-req-1",
};

describe("AgentState", () => {
    let state: AgentState;

    beforeEach(() => {
        state = new AgentState();
    });

    // Helper function to setup a state with a user message, request, and response
    function setupState(
        message: ChatRequestNode[],
        requestId: string,
        chunks: ChatResult[],
        finishResponse: boolean
    ) {
        state.sendUserChat(message);
        state.beginRequest(requestId);
        for (const chunk of chunks) {
            state.pushResponseChunk(chunk);
        }
        if (finishResponse) {
            state.finishResponse();
        }
    }

    describe("initialization", () => {
        it("should initialize with default values", () => {
            expect(state.conversationId).not.toBe("");
            expect(state.chatHistoryForDisplay).toEqual([]);
            expect(state.status).toBe(RemoteAgentStatus.agentStarting);
            expect(state.requestNodes).toEqual([]);
            expect(state.responseNodes).toEqual([]);
            expect(state.responseText).toBe("");
            expect(state.currentOpenExchange).toBeUndefined();
        });
    });

    describe("sendUserChat", () => {
        it("should set request nodes and update status", () => {
            state.sendUserChat(message);

            expect(state.requestNodes).toEqual(message);
            expect(state.status).toBe(RemoteAgentStatus.agentRunning);
        });
    });

    describe("beginRequest", () => {
        it("should set requestId and initialize currentOpenExchange", () => {
            setupState(message, "req-123", [], false);

            expect(state.requestId).toBe("req-123");
            expect(state.currentOpenExchange).toBeDefined();
            expect(state.currentOpenExchange?.request_nodes).toEqual(message);
            expect(state.status).toBe(RemoteAgentStatus.agentRunning);
        });
    });

    describe("pushResponseChunk", () => {
        it("should append text and nodes to the response", () => {
            setupState(message, "req-123", chunks.slice(0, 1), false);

            expect(state.responseText).toBe("Hello ");
            expect(state.responseNodes).toEqual([]);
            expect(state.currentOpenExchange).toEqual({
                request_message: "Hello",
                response_text: "Hello ",
                request_id: "req-123",
                request_nodes: message,
                response_nodes: state.responseNodes,
            });
        });

        it("should handle multiple chunks", () => {
            setupState(message, "req-123", chunks, false);

            expect(state.responseText).toBe("Hello there");
            expect(state.responseNodes).toHaveLength(2);
            expect(state.currentOpenExchange).toEqual({
                request_message: "Hello",
                response_text: "Hello there",
                request_id: "req-123",
                request_nodes: message,
                response_nodes: [...(chunks[2].nodes || []), ...(chunks[3].nodes || [])],
            });
        });
    });

    describe("chatHistory", () => {
        it("chatHistoryForDisplay completed exchange includes structured and unstructured", () => {
            setupState(message, "req-123", chunks, true);

            expect(state.chatHistoryForDisplay).toHaveLength(1);
            const exchange = state.chatHistoryForDisplay[0].exchange;
            expect(exchange.request_nodes).toHaveLength(1);
            expect(exchange.response_nodes).toHaveLength(2);
            expect(exchange.request_message).toBe("Hello");
            expect(exchange.response_text).toBe("Hello there");
        });

        it("chatHistoryForAPI completed exchange includes structured but not unstructured", () => {
            setupState(message, "req-123", chunks, true);

            expect(state.chatHistoryForAPI).toHaveLength(1);
            const exchange = state.chatHistoryForAPI[0];
            expect(exchange.request_nodes).toHaveLength(1);
            expect(exchange.response_nodes).toHaveLength(2);
            expect(exchange.request_message).toBe("");
            expect(exchange.response_text).toBe("");
        });

        it("currentOpenExchange includes structured and unstructured", () => {
            setupState(message, "req-123", chunks, false);

            const exchange = state.currentOpenExchange;
            expect(exchange?.request_nodes).toHaveLength(1);
            expect(exchange?.response_nodes).toHaveLength(2);
            expect(exchange?.request_message).toBe("Hello");
            expect(exchange?.response_text).toBe("Hello there");
        });

        it("MAIN_TEXT_FINISHED node replaces response text", () => {
            const mainTextFinishedChunks = [
                { text: "This should be replaced" },
                {
                    text: "",
                    nodes: [
                        {
                            id: 1,
                            type: ChatResultNodeType.RAW_RESPONSE,
                            content: "Hello there",
                        },
                    ],
                },
                {
                    text: "",
                    nodes: [
                        {
                            id: 2,
                            type: ChatResultNodeType.MAIN_TEXT_FINISHED,
                            content: "This is the real text",
                        },
                    ],
                },
            ];
            setupState(message, "req-123", mainTextFinishedChunks, true);

            expect(state.chatHistoryForDisplay).toHaveLength(1);
            const exchange = state.chatHistoryForDisplay[0].exchange;
            expect(exchange.response_nodes).toHaveLength(1); // This is 1 because only the RAW_RESPONSE node should be included
            expect(exchange.response_text).toBe("This is the real text");
        });
    });

    describe("finishResponse", () => {
        it("should add the current exchange to history and reset state", () => {
            setupState(message, "req-123", chunks, true);

            expect(state.chatHistoryForDisplay).toHaveLength(1);

            // Check that the exchange has the expected properties
            const exchangeState = state.chatHistoryForDisplay[0];
            expect(exchangeState.exchange).toEqual({
                request_message: "Hello",
                response_text: "Hello there",
                request_id: "req-123",
                request_nodes: message,
                response_nodes: [...(chunks[2].nodes || []), ...(chunks[3].nodes || [])],
            });

            // Check that sequenceId and finishedAt are present
            expect(exchangeState.sequenceId).toBe(1);
            expect(typeof exchangeState.finishedAt).toBe("string");
            // Verify it's a valid ISO date string
            expect(() => new Date(exchangeState.finishedAt)).not.toThrow();

            expect(state.toolCalls).toEqual(chunks[3].nodes);
            expect(state.currentOpenExchange).toBeUndefined();
            expect(state.requestNodes).toEqual([]);
            expect(state.responseNodes).toEqual([]);
            expect(state.responseText).toBe("");
            expect(state.status).toBe(RemoteAgentStatus.agentRunning);
        });

        it("should increment sequenceId for each exchange", () => {
            // First exchange
            setupState(message, "req-123", chunks.slice(0, 1), true);

            // Second exchange
            setupState(message, "req-456", chunks.slice(1, 2), true);

            expect(state.chatHistoryForDisplay).toHaveLength(2);
            expect(state.chatHistoryForDisplay[0].sequenceId).toBe(1);
            expect(state.chatHistoryForDisplay[1].sequenceId).toBe(2);
        });
    });

    describe("pushToolCallResult", () => {
        it("should add tool result to request nodes", () => {
            setupState(message, "req-123", chunks, true);

            state.pushToolCallResult(0, toolResult);

            expect(state.requestNodes).toHaveLength(1);
            expect(state.requestNodes[0].tool_result_node).toEqual({
                tool_use_id: "tool-1",
                content: "Tool result",
                is_error: false,
            });
        });
    });

    describe("pushChangedFiles", () => {
        it("should add changed files to the current exchange", () => {
            setupState(message, "req-123", chunks, false);
            const numTurns = 50;
            const maxChangedFiles = 100;
            for (let i = 0; i < numTurns; i++) {
                const changedFiles = [];
                const numChangedFiles = Math.floor(Math.random() * maxChangedFiles) + 1;
                for (let j = 0; j < numChangedFiles; j++) {
                    changedFiles.push({
                        old_path: "",
                        new_path: `file${i}-${j}.txt`,
                        old_contents: "",
                        new_contents: "New file contents",
                        change_type: FileChangeType.added,
                    });
                }
                state.pushChangedFiles(changedFiles, 2_000_000, 2000);
                state.finishResponse();
                const savedChangedFiles =
                    state.chatHistoryForDisplay[state.chatHistoryForDisplay.length - 1]
                        .changedFiles;
                expect(savedChangedFiles).toHaveLength(numChangedFiles);
                for (let j = 0; j < numChangedFiles; j++) {
                    expect(savedChangedFiles[j].old_path).toBe("");
                    expect(savedChangedFiles[j].new_path).toBe(`file${i}-${j}.txt`);
                    expect(savedChangedFiles[j].old_contents).toBe("");
                    expect(savedChangedFiles[j].new_contents).toBe("New file contents");
                    expect(savedChangedFiles[j].change_type).toBe(FileChangeType.added);
                }
            }
        });

        it("should trim large changed files", () => {
            setupState(message, "req-123", chunks, false);
            const changedFiles = [];
            const numChangedFiles = 10;
            const numNormalFiles = 4;
            for (let j = 0; j < numNormalFiles; j++) {
                changedFiles.push({
                    old_path: "",
                    new_path: `file${j}.txt`,
                    old_contents: "",
                    new_contents: "New file contents",
                    change_type: FileChangeType.added,
                });
            }

            for (let j = numNormalFiles; j < numChangedFiles; j++) {
                changedFiles.push({
                    old_path: "",
                    new_path: `file${j}.txt`,
                    old_contents: "",
                    new_contents: "1234567890".repeat(300_000),
                    change_type: FileChangeType.added,
                });
            }
            state.pushChangedFiles(changedFiles, 2_000_000, 2000);
            state.finishResponse();
            const savedChangedFiles =
                state.chatHistoryForDisplay[state.chatHistoryForDisplay.length - 1].changedFiles;
            expect(savedChangedFiles).toHaveLength(0);
            const skippedPaths =
                state.chatHistoryForDisplay[state.chatHistoryForDisplay.length - 1]
                    .changedFilesSkipped;
            expect(skippedPaths).toHaveLength(10);
            expect(skippedPaths[0]).toBe("file0.txt");
            expect(skippedPaths[9]).toBe("file9.txt");
        });
    });

    describe("fromJson", () => {
        describe("Type validation", () => {
            it("should throw an error for invalid conversation ID type", () => {
                const state = new AgentState();
                const serialized = JSON.stringify(state);
                const invalidJson = serialized.replace(
                    `"_conversationId":"${state.conversationId}"`,
                    `"_conversationId":123`
                );

                expect(() => AgentState.fromJson(invalidJson)).toThrow(
                    "Invalid _conversationId type in state data"
                );
            });

            it("should throw an error for invalid request nodes type", () => {
                const state = new AgentState();
                const serialized = JSON.stringify(state);
                const invalidJson = serialized.replace(
                    `"_requestNodes":[]`,
                    `"_requestNodes":"not an array"`
                );

                expect(() => AgentState.fromJson(invalidJson)).toThrow(
                    "Invalid _requestNodes type in state data"
                );
            });

            it("should throw an error for invalid status value", () => {
                const state = new AgentState();
                const serialized = JSON.stringify(state);
                const invalidJson = serialized.replace(
                    `"_status":${RemoteAgentStatus.agentStarting}`,
                    `"_status":999`
                );

                expect(() => AgentState.fromJson(invalidJson)).toThrow(
                    "Invalid _status value: 999"
                );
            });

            it("should throw an error for invalid status type", () => {
                const state = new AgentState();
                const serialized = JSON.stringify(state);
                const invalidJson = serialized.replace(
                    `"_status":${RemoteAgentStatus.agentStarting}`,
                    `"_status":"not a number"`
                );

                expect(() => AgentState.fromJson(invalidJson)).toThrow(
                    "Invalid _status type in state data"
                );
            });

            it("should throw an error for invalid JSON", () => {
                const invalidJson = "{invalid json";

                expect(() => AgentState.fromJson(invalidJson)).toThrow(
                    "Failed to parse agent state JSON"
                );
            });
        });

        describe("Structure change detection", () => {
            it("should detect changes to the AgentState structure", () => {
                // This test is designed to fail if the structure of AgentState changes
                // It verifies that all expected properties exist and have the correct types

                // Create a state and ensure all properties are set (not undefined)
                const state = new AgentState();
                // Set requestId explicitly to ensure it's included in serialization
                Object.defineProperty(state, "_requestId", {
                    value: "test-request-id",
                    writable: true,
                    enumerable: true,
                });

                const serialized = JSON.stringify(state);
                const parsed = JSON.parse(serialized);

                // Check that all expected private properties exist
                expect(parsed).toHaveProperty("_conversationId");
                expect(parsed).toHaveProperty("_requestNodes");
                expect(parsed).toHaveProperty("_requestId");
                expect(parsed).toHaveProperty("_responseChunks");
                expect(parsed).toHaveProperty("_toolCalls");
                // _changeHistory property has been removed in favor of storing changedFiles directly in each exchange
                expect(parsed).toHaveProperty("_chatHistory");
                expect(parsed).toHaveProperty("_status");
                expect(parsed).toHaveProperty("_lastProcessedServiceUpdateSequenceId");
                expect(parsed).toHaveProperty("_lastUploadedChatExchangeSequenceId");

                // Check types of key properties
                expect(typeof parsed._conversationId).toBe("string");
                expect(Array.isArray(parsed._requestNodes)).toBe(true);
                expect(typeof parsed._requestId).toBe("string");
                expect(Array.isArray(parsed._responseChunks)).toBe(true);
                expect(Array.isArray(parsed._toolCalls)).toBe(true);
                // _changeHistory property has been removed in favor of storing changedFiles directly in each exchange
                expect(Array.isArray(parsed._chatHistory)).toBe(true);
                expect(typeof parsed._status).toBe("number");

                // If any new properties are added to AgentState, this test will need to be updated
                // This ensures we catch structural changes that might affect serialization
                const expectedKeys = [
                    "_conversationId",
                    "_requestNodes",
                    "_requestId",
                    "_responseChunks",
                    "_toolCalls",
                    // "_changeHistory", // Removed in favor of storing changedFiles directly in each exchange
                    "_chatHistory",
                    "_status",
                    "_userGuidelines",
                    "_workspaceGuidelines",
                    "_agentMemories",
                    "_lastProcessedServiceUpdateSequenceId",
                    "_lastUploadedChatExchangeSequenceId",
                    "_changedFiles", // Added to store changed files for the current exchange
                    "_modelId",
                    "_logger",
                    "_changedFilesSkipped",
                    "_changedFilesSkippedCount",
                ];

                const actualKeys = Object.keys(parsed);

                // Check if there are any unexpected keys (new properties added to AgentState)
                const unexpectedKeys = actualKeys.filter((key) => !expectedKeys.includes(key));
                if (unexpectedKeys.length > 0) {
                    throw new Error(
                        `Found unexpected properties in AgentState: ${unexpectedKeys.join(", ")}. Update the tests to include these properties.`
                    );
                }

                // Check if any expected keys are missing (properties removed from AgentState)
                const missingKeys = expectedKeys.filter((key) => !actualKeys.includes(key));
                if (missingKeys.length > 0) {
                    throw new Error(
                        `Missing expected properties in AgentState: ${missingKeys.join(", ")}. Update the tests to reflect the new structure.`
                    );
                }
            });
        });
    });
});
