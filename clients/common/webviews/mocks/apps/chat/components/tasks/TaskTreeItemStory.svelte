<script lang="ts">
  import TaskTree from "$common-webviews/src/apps/chat/components/tasks/task-tree/TaskTree.svelte";
  import {
    TaskState,
    TaskUpdatedBy,
  } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import type { HydratedTask } from "@augment-internal/sidecar-libs/src/agent/task/task-types";
  import ColumnLayout from "$common-webviews/mocks/storybook/ColumnLayout.svelte";
  import { get, writable } from "svelte/store";
  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "$common-webviews/src/apps/chat/models/task-store";
  import { setContext } from "svelte";
  import { preprocessTaskTreeVisibility } from "$common-webviews/src/apps/chat/components/tasks/utils/task-visibility-utils";

  // Create sample tasks
  const pendingTask: HydratedTask = {
    uuid: "task-1",
    name: "Implement editable task name",
    description: "Add double-click to edit functionality for task names",
    state: TaskState.IN_PROGRESS,
    subTasks: [],
    lastUpdated: Date.now(),
    lastUpdatedBy: TaskUpdatedBy.USER,
  };

  const completedTask: HydratedTask = {
    uuid: "task-2",
    name: "Create task component",
    description: "Design and implement the task component",
    state: TaskState.COMPLETE,
    subTasks: [],
    lastUpdated: Date.now(),
    lastUpdatedBy: TaskUpdatedBy.USER,
  };

  const cancelledTask: HydratedTask = {
    uuid: "task-3",
    name: "Implement drag and drop",
    description: "Add drag and drop functionality for tasks",
    state: TaskState.CANCELLED,
    subTasks: [],
    lastUpdated: Date.now(),
    lastUpdatedBy: TaskUpdatedBy.USER,
  };

  const parentTask: HydratedTask = {
    uuid: "task-parent",
    name: "Task management system",
    description: "Implement the task management system",
    state: TaskState.IN_PROGRESS,
    subTasks: ["task-child-1", "task-child-2"],
    lastUpdated: Date.now(),
    lastUpdatedBy: TaskUpdatedBy.USER,
    subTasksData: [
      {
        uuid: "task-child-1",
        name: "Task list component",
        description: "Implement the task list component",
        state: TaskState.IN_PROGRESS,
        subTasks: [],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      },
      {
        uuid: "task-child-2",
        name: "Task detail component",
        description: "Implement the task detail component",
        state: TaskState.COMPLETE,
        subTasks: ["task-child-2-1"],
        subTasksData: [
          {
            uuid: "task-child-2-1",
            name: "Task detail component",
            description: "Implement the task detail component",
            state: TaskState.COMPLETE,
            subTasks: [],
            lastUpdated: Date.now(),
            lastUpdatedBy: TaskUpdatedBy.USER,
          },
        ],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      },
    ],
  };

  // Create writable stores for tasks to track updates
  const pendingTaskStore = writable(pendingTask);
  const completedTaskStore = writable(completedTask);
  const cancelledTaskStore = writable(cancelledTask);
  const parentTaskStore = writable(parentTask);

  function handleUpdateTask(
    uuid: string,
    updates: Partial<HydratedTask>,
    updatedBy: TaskUpdatedBy,
  ): Promise<void> {
    console.log("Update task:", uuid, updates, updatedBy);

    if (uuid === "task-1") {
      pendingTaskStore.update((t) => ({ ...t, ...updates, lastUpdatedBy: updatedBy }));
    } else if (uuid === "task-2") {
      completedTaskStore.update((t) => ({ ...t, ...updates, lastUpdatedBy: updatedBy }));
    } else if (uuid === "task-3") {
      cancelledTaskStore.update((t) => ({ ...t, ...updates, lastUpdatedBy: updatedBy }));
    } else if (uuid === "task-parent") {
      parentTaskStore.update((t) => ({ ...t, ...updates, lastUpdatedBy: updatedBy }));
    } else if (uuid === "task-child-1" || uuid === "task-child-2") {
      // Update child tasks
      parentTaskStore.update((t) => {
        if (!t.subTasksData) return t;

        const updatedSubTasks = t.subTasksData.map((st) => {
          if (st.uuid === uuid) {
            return { ...st, ...updates, lastUpdatedBy: updatedBy };
          }
          return st;
        });

        return { ...t, subTasksData: updatedSubTasks };
      });
    }

    return Promise.resolve();
  }

  function handleCreateSubtask(parentTaskUuid: string): Promise<string> {
    console.log("Create subtask for:", parentTaskUuid);

    const newSubtask: HydratedTask = {
      uuid: `task-child-${Date.now()}`,
      name: "New subtask",
      description: "New subtask description",
      state: TaskState.IN_PROGRESS,
      subTasks: [],
      lastUpdated: Date.now(),
      lastUpdatedBy: TaskUpdatedBy.USER,
    };

    if (parentTaskUuid === "task-1") {
      pendingTaskStore.update((t) => ({
        ...t,
        subTasks: [...(t.subTasks || []), newSubtask.uuid],
        subTasksData: [...(t.subTasksData || []), newSubtask],
      }));
    } else if (parentTaskUuid === "task-2") {
      completedTaskStore.update((t) => ({
        ...t,
        subTasks: [...(t.subTasks || []), newSubtask.uuid],
        subTasksData: [...(t.subTasksData || []), newSubtask],
      }));
    } else if (parentTaskUuid === "task-parent") {
      parentTaskStore.update((t) => ({
        ...t,
        subTasks: [...t.subTasks, newSubtask.uuid],
        subTasksData: [...(t.subTasksData || []), newSubtask],
      }));
    } else if (parentTaskUuid.startsWith("task-child-")) {
      // Add subtask to a child task
      parentTaskStore.update((t) => {
        if (!t.subTasksData) return t;

        const updatedSubTasks = t.subTasksData.map((st) => {
          if (st.uuid === parentTaskUuid) {
            return {
              ...st,
              subTasks: [...(st.subTasks || []), newSubtask.uuid],
              subTasksData: [...(st.subTasksData || []), newSubtask],
            };
          }
          return st;
        });

        return { ...t, subTasksData: updatedSubTasks };
      });
    }

    return Promise.resolve(newSubtask.uuid);
  }

  function getHydratedTask(uuid: string): Promise<HydratedTask> {
    if (uuid === "task-1") {
      return Promise.resolve(pendingTask);
    } else if (uuid === "task-2") {
      return Promise.resolve(completedTask);
    } else if (uuid === "task-3") {
      return Promise.resolve(cancelledTask);
    } else if (uuid === "task-parent") {
      return Promise.resolve(parentTask);
    } else if (uuid.startsWith("task-child-")) {
      return Promise.resolve(get(parentTaskStore)?.subTasksData?.find((t) => t.uuid === uuid)!);
    } else {
      return Promise.reject(new Error(`Task not found: ${uuid}`));
    }
  }

  function refreshTasks(): Promise<void> {
    console.log("Refresh tasks");
    return Promise.resolve();
  }

  function updateTaskListStatuses(uuid: string): Promise<void> {
    console.log("Update task list statuses:", uuid);
    return Promise.resolve();
  }

  const taskStore: ICurrentConversationTaskStore = {
    rootTaskUuid: writable("task-parent"),
    rootTask: writable(parentTask),
    canShowTaskList: writable(true),
    uuidToTask: writable(
      new Map([
        ["task-1", pendingTask],
        ["task-2", completedTask],
        ["task-3", cancelledTask],
        ["task-parent", parentTask],
        ...(() => {
          const entries: [string, HydratedTask][] = [];
          const stack = [parentTask];
          while (stack.length > 0) {
            const task = stack.pop()!;
            entries.push([task.uuid, task]);
            if (task.subTasksData) {
              stack.push(...task.subTasksData);
            }
          }
          return entries;
        })(),
      ]),
    ),
    createTask: handleCreateSubtask,
    updateTask: handleUpdateTask,
    getHydratedTask,
    cloneHydratedTask: () => Promise.resolve(undefined),
    refreshTasks,
    updateTaskListStatuses,
    syncTaskListWithConversation: () => Promise.resolve(),
    deleteTask: () => Promise.resolve(),
    getParentTask: () => undefined,
    addNewTaskAfter: () => Promise.resolve(undefined),
    saveHydratedTask: () => Promise.resolve(),
    runHydratedTask: () => Promise.resolve(),
    dispose: () => {},
    handleMessageFromExtension: () => false,
    runAllTasks: () => Promise.resolve(),
    isImportingExporting: writable(false),
    exportTask: () => Promise.resolve(),
    exportTasksToMarkdown: () => Promise.resolve(),
    importTasksFromMarkdown: () => Promise.resolve(),
    onTaskAdded: () => () => {},
  };

  setContext(CurrentConversationTaskStore.key, taskStore);

  // Convert HydratedTask to VisibleHydratedTask for display (show all tasks)
  $: visiblePendingTask = preprocessTaskTreeVisibility($pendingTaskStore, new Set());
  $: visibleCompletedTask = preprocessTaskTreeVisibility($completedTaskStore, new Set());
  $: visibleCancelledTask = preprocessTaskTreeVisibility($cancelledTaskStore, new Set());
  $: visibleParentTask = preprocessTaskTreeVisibility($parentTaskStore, new Set());
</script>

<ColumnLayout>
  <h3>Task States</h3>
  <div
    style="width: 100%; max-width: 800px; background: var(--ds-surface-1); padding: 16px; border-radius: 8px;"
  >
    <TaskTree task={visiblePendingTask} />
  </div>

  <div
    style="width: 100%; max-width: 800px; background: var(--ds-surface-1); padding: 16px; border-radius: 8px;"
  >
    <TaskTree task={visibleCompletedTask} />
  </div>

  <div
    style="width: 100%; max-width: 800px; background: var(--ds-surface-1); padding: 16px; border-radius: 8px;"
  >
    <TaskTree task={visibleCancelledTask} />
  </div>

  <h3>Task with Subtasks</h3>
  <div
    style="width: 100%; max-width: 800px; background: var(--ds-surface-1); padding: 16px; border-radius: 8px;"
  >
    <TaskTree task={visibleParentTask} />
  </div>
</ColumnLayout>
