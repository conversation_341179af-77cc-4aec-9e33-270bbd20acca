<script lang="ts">
  import CardButton from "$common-webviews/src/apps/chat/components/buttons/CardButton/CardButton.svelte";
  import DropdownMenu from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import ChevronRight from "$common-webviews/src/design-system/icons/chevron-right.svelte";
  import GithubIcon from "$common-webviews/src/design-system/icons/github.svelte";
  import { createEventDispatcher, getContext, onDestroy, onMount } from "svelte";
  import { GitReferenceModel } from "../../models/git-reference-model";
  import CopyButton from "$common-webviews/src/common/components/CopyButton.svelte";

  // Create event dispatcher
  const dispatch = createEventDispatcher<{
    authStateChange: { isAuthenticated: boolean };
  }>();

  const gitRefModel = getContext<GitReferenceModel>(GitReferenceModel.key);

  let isGithubAuthenticated = false;
  let isGithubAuthenticating = false;
  let isGithubRevoking = false;
  let githubAuthTimeout: ReturnType<typeof setTimeout> | null = null;
  let pollInterval: ReturnType<typeof setInterval> | null = null;
  let requestClose: () => void = () => {};

  let errorMessage: string | undefined = undefined;
  let authUrl: string | undefined = undefined;

  async function checkGithubAuthentication() {
    try {
      const newAuthState = await gitRefModel.isGithubAuthenticated();
      // Only dispatch if the state has changed
      if (newAuthState !== isGithubAuthenticated) {
        isGithubAuthenticated = newAuthState;
        dispatch("authStateChange", { isAuthenticated: isGithubAuthenticated });
      } else {
        isGithubAuthenticated = newAuthState;
      }
    } catch (error) {
      console.error("Failed to check GitHub authentication status:", error);
      isGithubAuthenticated = false;
      dispatch("authStateChange", { isAuthenticated: false });
    }
  }

  async function handleGithubAuthenticate() {
    errorMessage = undefined;
    authUrl = undefined;

    if (isGithubAuthenticating) {
      isGithubAuthenticating = false;
      if (githubAuthTimeout) {
        clearTimeout(githubAuthTimeout);
        githubAuthTimeout = null;
      }
      return;
    }

    isGithubAuthenticating = true;

    try {
      const { success, message, url } = await gitRefModel.authenticateGithub();

      if (!success) {
        // If we weren't able to open the browser, we shouldn't show the loading state
        authUrl = url;
        throw new Error(message);
      }

      // Poll while the user is authenticating on the browser
      pollInterval = setInterval(async () => {
        try {
          const authenticated = await gitRefModel.isGithubAuthenticated();
          if (authenticated) {
            isGithubAuthenticated = true;
            isGithubAuthenticating = false;
            // Dispatch event when authentication succeeds
            dispatch("authStateChange", { isAuthenticated: true });
            pollInterval && clearInterval(pollInterval);
            if (githubAuthTimeout) {
              clearTimeout(githubAuthTimeout);
              githubAuthTimeout = null;
            }
          }
        } catch (error) {
          // Keep polling on error as the user may still be in the process of authenticating
          console.error("Failed to check GitHub authentication status:", error);
        }
      }, 5000);

      // Hopefully the user will have authenticated within 60 seconds
      githubAuthTimeout = setTimeout(() => {
        pollInterval && clearInterval(pollInterval);
        isGithubAuthenticating = false;
        githubAuthTimeout = null;
      }, 60000);
    } catch (error) {
      console.error("Failed to authenticate with GitHub:", error);
      errorMessage = `Error: ${error instanceof Error ? error.message : String(error)}`;
      isGithubAuthenticating = false;
    }
  }

  async function handleRevokeGithubAccess() {
    if (isGithubRevoking) {
      return;
    }
    isGithubRevoking = true;
    try {
      const result = await gitRefModel.revokeGithubAccess();
      if (result.success) {
        isGithubAuthenticated = false;
        // Dispatch event when access is revoked
        dispatch("authStateChange", { isAuthenticated: false });
      } else {
        console.error("Failed to revoke GitHub access:", result.message);
      }
    } catch (error) {
      console.error("Error revoking GitHub access:", error);
    } finally {
      isGithubRevoking = false;
      requestClose();
    }
  }

  onMount(async () => {
    await checkGithubAuthentication();
  });

  onDestroy(() => {
    if (githubAuthTimeout) {
      clearTimeout(githubAuthTimeout);
      githubAuthTimeout = null;
    }
    if (pollInterval) {
      clearInterval(pollInterval);
      pollInterval = null;
    }
  });
</script>

<div class="github-auth-card">
  <div class="github-auth-button">
    {#if !isGithubAuthenticated}
      <CardButton
        type="button"
        title={isGithubAuthenticating ? "Cancel" : "Connect to GitHub"}
        onClick={handleGithubAuthenticate}
      >
        <GithubIcon slot="iconLeft" />
        <div slot="iconRight">
          {#if isGithubAuthenticating}
            <SpinnerAugment size={1} useCurrentColor={true} />
          {:else}
            <ChevronRight />
          {/if}
        </div>
      </CardButton>
      {#if errorMessage}
        <div class="github-auth-error">
          <TextAugment size={1}>An error occurred while authenticating with GitHub.</TextAugment>
          {#if authUrl}
            <TextAugment size={1}>
              Visit or copy the following URL in your browser to authenticate manually:
            </TextAugment>
            <div class="github-auth-error-url-container">
              <TextAugment size={1} class="github-auth-error-url">
                <a href={authUrl} target="_blank">
                  {authUrl}
                </a>
              </TextAugment>
              <CopyButton text={authUrl} tooltipNested={false} />
            </div>
          {/if}
          <TextAugment size={1}>
            {errorMessage}
          </TextAugment>
        </div>
      {/if}
    {:else}
      <CardButton type="dropdown" title="Connected to your GitHub account">
        <GithubIcon slot="iconLeft" />
        <div slot="dropdown-content">
          <DropdownMenu.Item
            color="error"
            onSelect={() => {
              handleRevokeGithubAccess();
              requestClose();
            }}
          >
            {#if isGithubRevoking}
              <SpinnerAugment slot="iconLeft" useCurrentColor size={1} />
              <TextAugment size={1} weight="medium">Revoking...</TextAugment>
            {:else}
              <TextAugment size={1} weight="medium">Revoke Access</TextAugment>
            {/if}
          </DropdownMenu.Item>
        </div>
      </CardButton>
    {/if}
  </div>
</div>

<style>
  .github-auth-card {
    border: 1px solid var(--ds-color-border-subtle);
    border-radius: var(--ds-radius-2);
    background-color: var(--ds-color-surface-1);
    height: fit-content;
  }

  .github-auth-error {
    margin-top: var(--ds-spacing-3);
  }

  .github-auth-error :global(.c-text) {
    display: block;
    margin: var(--ds-spacing-1) 0;
  }

  .github-auth-error :global(.c-text.github-auth-error-url) {
    user-select: text;
    word-break: break-all;
  }

  .github-auth-error-url-container {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }
</style>
