<script lang="ts">
  import { onDestroy, onMount } from "svelte";
  import type * as Monaco<PERSON><PERSON><PERSON> from "monaco-editor";

  import { DiffViewModel } from "./models/diff-view-model";
  import ChunkActionPanel from "./components/ChunkActionPanel.svelte";
  import TopActionPanel from "./components/TopActionPanel.svelte";
  import InstructionOverlay from "./components/InstructionOverlay.svelte";
  import {
    CATEGORY_TO_MONACO_THEME,
    getMonacoTheme,
  } from "$common-webviews/src/common/utils/monaco-theme";
  import {
    type AugmentUserThemeDetails,
    themeStore,
  } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import { UserThemeCategory } from "$common-webviews/src/common/hosts/user-themes/augment-theme-attributes";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";

  let diffViewModel: DiffViewModel | undefined;
  let editorContainer: HTMLElement | undefined;

  $: disableApply = diffViewModel?.disableApply;
  $: disableResolution = diffViewModel?.disableResolution;

  function getThemeName(themeDetails: AugmentUserThemeDetails | undefined): string {
    const fallbackCategory = UserThemeCategory.dark;
    const origCategory = themeDetails?.category || fallbackCategory;
    const monacoTheme =
      getMonacoTheme(origCategory, themeDetails?.intensity) ??
      CATEGORY_TO_MONACO_THEME.get(fallbackCategory);
    return monacoTheme;
  }

  $: {
    const themeDetails = $themeStore;
    if (diffViewModel) {
      diffViewModel?.updateTheme(getThemeName(themeDetails));
    }
  }

  let monaco: typeof MonacoEditor | undefined;
  $: if (monaco && editorContainer && !diffViewModel) {
    diffViewModel = new DiffViewModel(editorContainer, getThemeName($themeStore), monaco);
  }

  onMount(async () => {
    monaco = await window.augmentDeps.monaco;
    if (!monaco) {
      console.error("Monaco not loaded. Diff view cannot be initialized.");
      return;
    }
  });

  onDestroy(() => {
    diffViewModel?.dispose();
  });

  $: leaves = $diffViewModel?.leaves;

  // Track the focused state of the webview to notify the extension
  let isFocused = false;
  $: diffViewModel?.updateIsWebviewFocused(isFocused);
</script>

<svelte:window
  on:message={diffViewModel?.handleMessageFromExtension}
  on:focus={() => (isFocused = true)}
  on:blur={() => (isFocused = false)}
/>
<MonacoProvider.Root>
  <div class="diff-view-container">
    {#if $diffViewModel}
      <div class="sticky-top">
        <TopActionPanel diffViewModel={$diffViewModel} />
      </div>
      <InstructionOverlay diffViewModel={$diffViewModel} />
    {/if}

    <div class="editor-container">
      <div class="editor" bind:this={editorContainer} />
      {#if $diffViewModel && leaves?.length && !$disableResolution}
        <!-- Render the action panel for each leaf -->
        {#each leaves as leaf, idx}
          <!-- Only show the action panel if there is a diff for this range -->
          {#if leaf.unitOfCodeWork.modifiedCode !== leaf.unitOfCodeWork.originalCode}
            <ChunkActionPanel
              isFocused={$diffViewModel?.currFocusedChunkIdx === idx}
              onAccept={() => $diffViewModel?.acceptChunk(leaf)}
              onReject={() => $diffViewModel?.rejectChunk(leaf)}
              diffViewModel={$diffViewModel}
              {leaf}
              align="right"
              disableApply={$disableApply}
            />
          {/if}
        {/each}
      {/if}
    </div>
  </div>
</MonacoProvider.Root>

<style>
  .diff-view-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
  }

  .sticky-top {
    position: sticky;
    top: 0;
    z-index: var(--z-diff-view-top-actions);
  }

  .editor-container {
    position: relative;
    flex-grow: 1;
    overflow-y: auto;
  }

  .editor {
    height: 100%;
    width: 100%;
  }

  .diff-view-container :global(.monaco-editor-background),
  .diff-view-container :global(.monaco-editor),
  .diff-view-container :global(.monaco-editor .margin),
  .diff-view-container :global(.sticky-widget) {
    /* Override monaco's background color with the block container's color */
    background-color: var(--user-theme-panel-background);
  }
</style>
