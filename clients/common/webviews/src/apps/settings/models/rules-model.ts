import { writable, type Writable } from "svelte/store";
import type { HostInterface } from "$common-webviews/src/common/hosts/host-types";
import type {
  MessageBroker,
  MessageConsumer,
} from "$common-webviews/src/common/utils/message-broker";
import {
  WebViewMessageType,
  type WebViewMessage,
} from "$vscode/src/webview-providers/webview-messages";
import type { Rule } from "@augment-internal/sidecar-libs/src/chat/chat-types";
import { ExtensionClient } from "../../chat/extension-client";
import { ChatFlagsModel } from "../../chat/models/chat-flags-model";
import {
  AgentSessionEventName,
  RulesImportedType,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import {
  AUGMENT_GUIDELINES_FILE,
  RulesParser,
} from "@augment-internal/sidecar-libs/src/utils/rules-parser";

/**
 * Model for managing rules
 */
export class RulesModel implements MessageConsumer {
  private _rulesFiles: Writable<Rule[]> = writable([]);
  private _loading: Writable<boolean> = writable(true);
  public _extensionClient: ExtensionClient;

  constructor(
    private readonly _host: HostInterface,
    private readonly _msgBroker: MessageBroker,
  ) {
    this.requestRules();
    const flagsModel = new ChatFlagsModel();
    this._extensionClient = new ExtensionClient(_host, _msgBroker, flagsModel);
  }

  /**
   * Handle messages from the extension
   */
  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    if (e.data && e.data.type === WebViewMessageType.getRulesListResponse) {
      this._rulesFiles.set(e.data.data);
      this._loading.set(false);
      return true;
    }
    if (e.data && e.data.type === WebViewMessageType.createRuleResponse) {
      this._extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.rulesImported,
        conversationId: "",
        eventData: {
          rulesImportedData: {
            type: RulesImportedType.manuallyCreated,
            numFiles: 1,
          },
        },
      });
    }
    if (e.data && e.data.type === WebViewMessageType.autoImportRulesResponse && e.data.data) {
      this._extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.rulesImported,
        conversationId: "",
        eventData: {
          rulesImportedData: {
            type: RulesImportedType.auto,
            numFiles: e.data.data.importedRulesCount,
            source: e.data.data.source,
          },
        },
      });
    }
    if (e.data && e.data.type === WebViewMessageType.triggerImportDialogResponse && e.data.data) {
      this.requestRules();
      const ruleImportType =
        e.data.data.directoryOrFile === "directory"
          ? RulesImportedType.selectedDirectory
          : RulesImportedType.selectedFile;
      this._extensionClient.reportAgentSessionEvent({
        eventName: AgentSessionEventName.rulesImported,
        conversationId: "",
        eventData: {
          rulesImportedData: {
            type: ruleImportType,
            numFiles: e.data.data.importedRulesCount,
          },
        },
      });
      return true;
    }
    return false;
  }

  /**
   * Request the list of rules from the extension
   */
  requestRules() {
    this._loading.set(true);
    this._host.postMessage({
      type: WebViewMessageType.getRulesListRequest,
    });
  }

  /**
   * Create a new rule
   * The extension will prompt for a name
   */
  createRule() {
    this._host.postMessage({
      type: WebViewMessageType.createRule,
    });
  }

  /**
   * Open a rule in the editor
   */
  openRule(path: string) {
    // Check if this is a guidelines file
    if (path === AUGMENT_GUIDELINES_FILE) {
      // For guidelines files, use the openGuidelines message type
      // We need to get the workspace folder, but for now we'll pass empty string
      // which will open the guidelines file in the current workspace
      this._host.postMessage({
        type: WebViewMessageType.openGuidelines,
        data: "", // Empty string means current workspace
      });
    } else {
      // For regular rules files, use the openRule message type
      this._host.postMessage({
        type: WebViewMessageType.openRule,
        data: { path },
      });
    }
  }

  updateRuleType(path: string, content: string, alwaysApply: boolean) {
    const newText = RulesParser.updateAlwaysApplyFrontmatterKey(content, alwaysApply);
    this._extensionClient.saveFile({
      repoRoot: "",
      pathName: path,
      content: newText,
    });
  }

  deleteRule(path: string) {
    this._host.postMessage({
      type: WebViewMessageType.deleteRule,
      data: { path },
    });
  }

  autoImportRules() {
    this._host.postMessage({
      type: WebViewMessageType.autoImportRules,
    });
  }

  selectFileToImport() {
    this._host.postMessage({
      type: WebViewMessageType.triggerImportDialog,
    });
  }

  importRule(filename: string) {
    this._host.postMessage({
      type: WebViewMessageType.importFileRequest,
      data: { filename, autoImport: true },
    });
  }

  importDirectoryContents(directoryPath: string) {
    this._host.postMessage({
      type: WebViewMessageType.importDirectoryRequest,
      data: { directoryPath },
    });
  }

  /**
   * Get the rules files store
   */
  getRulesFiles() {
    return this._rulesFiles;
  }

  /**
   * Get the loading state store
   */
  getLoading() {
    return this._loading;
  }
}
