<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import RegularFileIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file.svg?component";
  import RegularArrowUpRightFromSquareIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/arrow-up-right-from-square.svg?component";
  import RegularPlusIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/plus.svg?component";
  import Download from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/download.svg?component";
  import Trash from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/trash.svg?component";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { host } from "$common-webviews/src/common/hosts/host";
  import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
  import { RulesModel } from "../models/rules-model";

  import { onMount } from "svelte";
  import { type Readable } from "svelte/store";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import UserGuidelinesCategory from "./UserGuidelinesCategory.svelte";
  import SettingsCard from "../components/SettingsCard.svelte";
  import { RuleType } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import RulesDropdown from "../../rules/RulesDropdown.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/chevron-down.svg?component";
  import {
    AUGMENT_DIRECTORY_ROOT,
    AUGMENT_GUIDELINES_FILE,
    AUGMENT_RULES_FOLDER,
  } from "@augment-internal/sidecar-libs/src/utils/rules-parser";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";

  export let userGuidelines = "";
  export let userGuidelinesLengthLimit: number | undefined = undefined;
  export let updateUserGuideline: (value: string) => boolean = () => false;

  const msgBroker = new MessageBroker(host);
  const rulesModel = new RulesModel(host, msgBroker);

  msgBroker.registerConsumer(rulesModel);

  const rulesFiles = rulesModel.getRulesFiles();

  // Helper function to check if a file is a guidelines file
  function isGuidelinesFile(filePath: string): boolean {
    return filePath === AUGMENT_GUIDELINES_FILE;
  }

  // Import options
  const importOptions = [
    {
      label: "Auto import existing rules in the workspace",
      id: "auto_import",
      description: "Choose existing rules in your workspace to auto import to Augment.",
    },
    {
      label: "Select file(s) or directory to import",
      id: "select_file_or_directory",
      description: "Select an existing directory or list of markdown files to import to Augment.",
    },
  ] as const;
  type ImportOption = (typeof importOptions)[number];
  let importFocusedIndex: Readable<number | undefined> | undefined = undefined;
  let requestImportDropdownClose: () => void = () => {};

  async function handleImportSelect(option: ImportOption): Promise<void> {
    try {
      if (option.id === "select_file_or_directory") {
        rulesModel.selectFileToImport();
      } else if (option.id === "auto_import") {
        rulesModel.autoImportRules();
      }
    } catch (e) {
      console.error("Failed to handle import select:", e);
    }

    if (requestImportDropdownClose) requestImportDropdownClose();
  }

  onMount(() => {
    rulesModel.requestRules();
  });
</script>

<svelte:window on:message={msgBroker.onMessageFromExtension} />

<div class="c-rules-category">
  <!-- Project Rules Section -->
  <div class="c-rules-section">
    <TextAugment class="c-section-header" size={3} color="primary">Rules</TextAugment>
    <!-- Rules description field -->
    <div>
      Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) <a
        href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"
        target="_blank"
      >
        <TextAugment size={1} weight="regular">Learn more</TextAugment>
      </a>
    </div>
    <div class="c-rules-list">
      {#if $rulesFiles.length === 0}
        <div class="c-rules-list-empty">
          <TextAugment size={1} color="neutral">No rules files found</TextAugment>
        </div>
      {:else}
        {#each $rulesFiles as file (file.path)}
          <SettingsCard isClickable on:click={() => rulesModel.openRule(file.path)}>
            <div class="c-rule-item-info" slot="header-left">
              <RegularFileIcon />
              <div class="c-rule-item-path">
                <TextAugment size={1} color="neutral">{file.path}</TextAugment>
              </div>
            </div>
            <div class="server-actions" slot="header-right">
              <div class="status-controls">
                {#if isGuidelinesFile(file.path)}
                  <!-- Guidelines files are always "Always" and cannot be changed -->
                  <TextTooltipAugment content="Workspace guidelines are always applied">
                    <ButtonAugment color="accent" size={1} disabled>Always</ButtonAugment>
                  </TextTooltipAugment>
                {:else}
                  <!-- Regular rules files with dropdown -->
                  <RulesDropdown
                    onSave={(alwaysApply) => {
                      rulesModel.updateRuleType(
                        `${AUGMENT_DIRECTORY_ROOT}/${AUGMENT_RULES_FOLDER}/${file.path}`,
                        file.content,
                        alwaysApply,
                      );
                    }}
                    alwaysApply={file.type === RuleType.ALWAYS_ATTACHED}
                  />
                {/if}
                <ButtonAugment
                  size={1}
                  variant="ghost-block"
                  color="neutral"
                  class="c-rule-item-button"
                  on:click={(e) => {
                    e.stopPropagation();
                    rulesModel.openRule(file.path);
                  }}
                >
                  <RegularArrowUpRightFromSquareIcon slot="iconRight" />
                </ButtonAugment>
                <ButtonAugment
                  size={1}
                  variant="ghost-block"
                  color="neutral"
                  class="c-rule-item-button"
                  on:click={(e) => {
                    e.stopPropagation();
                    rulesModel.deleteRule(file.path);
                  }}
                >
                  <Trash slot="iconRight" />
                </ButtonAugment>
              </div>
            </div>
          </SettingsCard>
        {/each}
      {/if}
    </div>
    <div class="c-rules-actions-container">
      <ButtonAugment
        size={1}
        variant="soft"
        color="neutral"
        class="c-rules-action-button"
        on:click={() => rulesModel.createRule()}
      >
        <div class="c-rules-actions-button-content">
          <RegularPlusIcon />
          <span>Create new rule file</span>
        </div>
      </ButtonAugment>

      <DropdownMenuAugment.Root
        bind:requestClose={requestImportDropdownClose}
        bind:focusedIndex={importFocusedIndex}
      >
        <DropdownMenuAugment.Trigger>
          <ButtonAugment size={1} variant="soft" color="neutral" class="c-rules-action-button">
            <div class="c-rules-actions-button-content">
              <Download />
              <span>Import rules</span>
              <ChevronDown />
            </div>
          </ButtonAugment>
        </DropdownMenuAugment.Trigger>
        <DropdownMenuAugment.Content align="start" side="bottom">
          {#each importOptions as option (option.id)}
            <DropdownMenuAugment.Item onSelect={() => handleImportSelect(option)}>
              {option.label}
            </DropdownMenuAugment.Item>
          {/each}
          {#if $importFocusedIndex !== undefined}
            <DropdownMenuAugment.Separator />
            <DropdownMenuAugment.Label>
              {$importFocusedIndex !== undefined
                ? importOptions[$importFocusedIndex].description
                : importOptions[0]}
            </DropdownMenuAugment.Label>
          {/if}
        </DropdownMenuAugment.Content>
      </DropdownMenuAugment.Root>
    </div>
  </div>
  <!-- User Guidelines Section -->
  <div class="c-user-guidelines-section">
    <TextAugment class="c-section-header" size={3} color="primary">User Guidelines</TextAugment>
    <!-- User Guidelines description field -->
    <div>
      User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. <a
        href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"
        target="_blank"
      >
        <TextAugment size={1} weight="regular">Learn more</TextAugment>
      </a>
    </div>
    <UserGuidelinesCategory {userGuidelines} {userGuidelinesLengthLimit} {updateUserGuideline} />
  </div>
</div>

<style>
  .c-rules-category {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-5);
    position: relative;
  }

  .c-user-guidelines-section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-rules-section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-rules-list {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-1);
    padding: var(--ds-spacing-2) 0;
  }

  .c-rules-list-empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: var(--ds-spacing-3);
    opacity: 0.7;
    gap: var(--ds-spacing-4);
  }

  .c-rules-list :global(.c-button--content) {
    justify-content: space-between;
    width: 100%;
  }

  .c-rule-item-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-rule-item-path {
    font-size: 0.85em;
    opacity: 0.7;
  }

  .c-rules-actions-button-content {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-rules-actions-container {
    display: flex;
    gap: var(--ds-spacing-2); /* Add gap between buttons */
    align-items: center;
  }

  .status-controls {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
  }
  .status-controls :global(.c-button--content) {
    padding: var(--base-btn-padding-vertical) var(--ds-spacing-1);
  }
  .status-controls :global(svg) {
    --button-icon-size: 14px;
  }
</style>
