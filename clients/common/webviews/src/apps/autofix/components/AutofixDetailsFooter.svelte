<script lang="ts">
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import { createEventDispatcher } from "svelte";

  const dispatch = createEventDispatcher<{ applyAndRetestClick: void }>();
</script>

<div class="autofix-details-footer">
  <span>
    Here are the suggestions for how to fix this failure. You must review each change before
    continuing.
  </span>

  <ButtonAugment on:click={() => dispatch("applyAndRetestClick")}>Apply & Retest</ButtonAugment>
</div>

<style>
  .autofix-details-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--p-2);
    background-color: var(--user-theme-sidebar-background);
    border-top: 1px solid var(--vscode-panel-border);
  }
</style>
