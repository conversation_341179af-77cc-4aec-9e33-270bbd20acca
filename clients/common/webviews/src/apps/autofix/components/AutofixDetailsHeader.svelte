<script lang="ts">
  import type { IConversationAutofixExtraData } from "$vscode/src/autofix/autofix-state";
  import { AutofixDetailsHeaderTab } from "../models/types";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";

  export let autofixData: IConversationAutofixExtraData;
  export let currentTab: AutofixDetailsHeaderTab = AutofixDetailsHeaderTab.testLog;
  export let currentIterationId: string | undefined = undefined;

  $: iterations = autofixData.autofixIterations || [];
  $: currentIteration = iterations.find((it) => it.id === currentIterationId);
  $: isSolutionEnabled = currentIteration?.suggestedSolutions !== undefined;

  $: {
    if (iterations.length > 0 && !currentIterationId) {
      currentIterationId = iterations[iterations.length - 1].id;
    }
  }

  function handleTabChange(tab: AutofixDetailsHeaderTab) {
    currentTab = tab;
  }

  function handleIterationChange(iterationId: string | undefined) {
    currentTab = AutofixDetailsHeaderTab.testLog;
    currentIterationId = iterationId;
  }
</script>

<div class="autofix-details-header">
  <div class="tab-buttons">
    <ButtonAugment
      on:click={() => handleTabChange(AutofixDetailsHeaderTab.testLog)}
      variant={currentTab === AutofixDetailsHeaderTab.testLog ? "classic" : "solid"}
    >
      Test log
    </ButtonAugment>
    <ButtonAugment
      on:click={() => handleTabChange(AutofixDetailsHeaderTab.solution)}
      variant={currentTab === AutofixDetailsHeaderTab.solution ? "classic" : "solid"}
      disabled={!isSolutionEnabled}
    >
      Solution
    </ButtonAugment>
  </div>
  <div class="iteration-selector-container">
    <select
      class="iteration-selector"
      bind:value={currentIterationId}
      on:change={() => handleIterationChange(currentIterationId)}
    >
      {#each iterations as iteration}
        <option value={iteration.id}>
          Test Attempt {iterations.indexOf(iteration) + 1}
        </option>
      {/each}
    </select>
  </div>
</div>

<style>
  .autofix-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ds-spacing-3);
    background-color: var(--user-theme-sidebar-background);
    border-bottom: 1px solid var(--vscode-panel-border);
  }

  .tab-buttons {
    display: flex;
    gap: var(--ds-spacing-3);
  }

  .iteration-selector-container {
    min-width: 150px;
  }

  .iteration-selector {
    background-color: rgba(221, 234, 248, 0.05);
    border: none;
    border-radius: var(--ds-radius-2);
    padding: var(--ds-spacing-1) var(--ds-spacing-3);
    font-size: var(--vscode-font-size);
    color: var(--vscode-foreground);
    width: 100%;
  }
</style>
