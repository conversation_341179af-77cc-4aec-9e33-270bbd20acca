import { writable, derived, readonly, get } from "svelte/store";
import type { ChatModel } from "./chat-model";
import { ExchangeStatus } from "../types/chat-message";
import type { FileDetails, WebViewMessage } from "$vscode/src/webview-providers/webview-messages";
import { getSlashCommand } from "$common-webviews/src/apps/chat/types/slash-command-list";
import type { IMentionable } from "$common-webviews/src/common/components/inputs/types";
import type { IContextInfo } from "./types";
import { ChatMetricName } from "$vscode/src/metrics/types";
import { WebViewMessageType } from "$vscode/src/webview-providers/webview-messages";
import { type MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import { type AgentConversationModel } from "./agent-conversation-model";

class CommandSubscription {
  private _model: SlashCommandModel;
  private _fn: (command: string | undefined) => void;

  constructor(model: SlashCommandModel, fn: (command: string | undefined) => void) {
    this._model = model;
    this._fn = fn;
  }

  dispose = (): void => {
    this._model.removeAfterCommandListener(this._fn);
  };
}

export class SlashCommandModel implements MessageConsumer {
  constructor(
    private _chatModel: ChatModel,
    private _agentConversationModel: AgentConversationModel,
  ) {}

  private _activeCommand = writable<string | undefined>(undefined);
  private _isActiveCommandRunnable = writable(false);
  private _afterCommandListeners: Array<(command: string | undefined) => void> = [];
  private _inputText = "";
  private _selections: (IMentionable & {
    selection: FileDetails;
  } & IContextInfo)[] = [];

  public readonly hasActiveCommand = derived(
    this._activeCommand,
    ($activeCommand) => $activeCommand !== undefined,
  );

  public readonly isActiveCommandRunnable = readonly(this._isActiveCommandRunnable);
  public readonly activeCommand = readonly(this._activeCommand);
  public readonly activeCommandData = derived(this._activeCommand, ($activeCommand) => {
    return getSlashCommand($activeCommand);
  });

  get inputText(): string {
    return this._inputText;
  }

  get selections(): (IMentionable & {
    selection: FileDetails;
  } & IContextInfo)[] {
    return this._selections;
  }

  setActiveCommand(command: string | undefined) {
    this._activeCommand.set(command);
    this.updateRunnable();
  }

  setInputText(inputText: string) {
    this._inputText = inputText;
    this.updateRunnable();
  }

  setSelections(selections: (IMentionable & { selection: FileDetails } & IContextInfo)[]) {
    this._selections = selections;
    this.updateRunnable();
  }

  async runActiveCommand() {
    const command = getSlashCommand(get(this._activeCommand));
    if (!command) {
      return;
    }

    const prompt = await command.promptFactory(this._inputText, this._chatModel);
    this._activeCommand.set(undefined);

    // Save the existing conversation. We'll start a new thread.
    await this._chatModel.setCurrentConversation();

    if (command.id === "generate-commit-message") {
      this._chatModel.currentConversationModel?.generateCommitMessage();
    } else {
      // Send the message
      this._chatModel.currentConversationModel?.sendExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: prompt,
        rich_text_json_repr: undefined,
        status: ExchangeStatus.draft,
        model_id: this._chatModel.currentConversationModel?.selectedModelId ?? undefined,
        /* eslint-enable @typescript-eslint/naming-convention */
      });
    }

    // Sending a session metric
    this._chatModel.host.postMessage({
      type: WebViewMessageType.usedSlashAction,
    });
    // Sending aggregate metrics
    this.reportCommandMetric(command.id);

    this._afterCommandListeners.forEach((listener) => listener(prompt));
  }

  reportCommandMetric(command: string | undefined) {
    switch (command) {
      case "fix":
        this._chatModel.extensionClient.reportWebviewClientEvent(ChatMetricName.chatUseActionFix);
        break;
      case "explain":
        this._chatModel.extensionClient.reportWebviewClientEvent(
          ChatMetricName.chatUseActionExplain,
        );
        break;
      case "write-test":
        this._chatModel.extensionClient.reportWebviewClientEvent(
          ChatMetricName.chatUseActionWriteTest,
        );
        break;
      case "find":
        this._chatModel.extensionClient.reportWebviewClientEvent(ChatMetricName.chatUseActionFind);
        break;
    }
  }

  cancelActiveCommand() {
    this._activeCommand.set(undefined);
    this._afterCommandListeners.forEach((listener) => listener(undefined));
  }

  afterCommand(fn: (command: string | undefined) => void): CommandSubscription {
    this._afterCommandListeners.push(fn);
    return new CommandSubscription(this, fn);
  }

  removeAfterCommandListener(fn: (command: string | undefined) => void) {
    this._afterCommandListeners = this._afterCommandListeners.filter((listener) => listener !== fn);
  }

  updateRunnable() {
    let isRunnable = false;
    const actionId = get(this._activeCommand);
    const command = getSlashCommand(actionId);

    if (actionId !== undefined && command !== undefined) {
      isRunnable = true;
      if (command?.requiresInput) {
        isRunnable = isRunnable && this._inputText !== "";
      }
      if (command?.requiresEditorSelection) {
        isRunnable = isRunnable && this._selections.length > 0;
      }
    }

    this._isActiveCommandRunnable.set(isRunnable);
  }

  handleMessageFromExtension(e: MessageEvent<WebViewMessage>): boolean {
    const msg = e.data;
    switch (msg.type) {
      case WebViewMessageType.runSlashCommand: {
        // For the "fix" command, create a new agent thread; for others, switch to chat mode
        if (msg.data === "fix") {
          // Create a new Agent thread with the last used execution mode
          void this._createNewAgentThreadForFix();
        } else {
          // For other slash commands, switch to chat mode if currently in agent mode
          if (get(this._agentConversationModel.isCurrConversationAgentic)) {
            this._agentConversationModel.setToChat();
          }
        }
        this.setActiveCommand(msg.data);
        this.runActiveCommand();
        return true;
      }
      default:
        return false;
    }
  }

  /**
   * Creates a new Agent thread for the fix command, preserving the last used execution mode
   */
  private async _createNewAgentThreadForFix(): Promise<void> {
    // Get the ChatModeModel from the ChatModel
    const chatModeModel = (this._chatModel as any)._chatModeModel;
    if (chatModeModel) {
      // Get the current agent execution mode (manual/auto)
      const currentMode = get(this._chatModel.agentExecutionMode);
      // Create a new local agent thread with the preserved execution mode
      await chatModeModel.createNewLocalAgentThread(currentMode);
    } else {
      // Fallback: if ChatModeModel is not available, just switch to agent mode
      if (!get(this._agentConversationModel.isCurrConversationAgentic)) {
        void this._agentConversationModel.setToAgentic();
      }
    }
  }
}
