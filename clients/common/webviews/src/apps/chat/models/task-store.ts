/**
 * @file task-store.ts
 * This file contains the implementation of the CurrentConversationTaskStore class.
 * This store only manages the top-level task.
 */

import { writable, type Readable, readonly, type Writable, get, derived } from "svelte/store";
import { type IExtensionClient } from "../extension-client";
import { type MessageConsumer } from "$common-webviews/src/common/utils/message-broker";
import {
  type HydratedTask,
  type SerializedTask,
  TaskUpdatedBy,
  TaskState,
} from "@augment-internal/sidecar-libs/src/agent/task/task-types";
import {
  findTaskInTree,
  getMarkdownRepresentation,
  parseMarkdownToTaskTree,
  SAMPLE_TASK_MARKDOWN,
  deepCloneTask,
} from "@augment-internal/sidecar-libs/src/agent/task/task-utils";
import type { IDisposable } from "monaco-editor";
import type { ConversationModel } from "./conversation-model";
import type { ChatModel } from "./chat-model";
import type { AgentConversationModel } from "./agent-conversation-model";
import { type JSONContent } from "@tiptap/core";
import { ExchangeStatus } from "../types/chat-message";

/**
 * Interface for the CurrentConversationTaskStore class.
 * Used for mocking + store injection
 */
export interface ICurrentConversationTaskStore {
  rootTaskUuid: Readable<string | undefined>;
  rootTask: Readable<HydratedTask | undefined>;
  isImportingExporting: Readable<boolean>;
  canShowTaskList: Readable<boolean>;
  uuidToTask: Readable<Map<string, HydratedTask>>;
  onTaskAdded: (listener: (event: Event) => void) => () => void;
  createTask(name: string, description: string, parentTaskUuid?: string): Promise<string>;
  updateTask(
    uuid: string,
    updates: Partial<SerializedTask>,
    updatedBy: TaskUpdatedBy,
  ): Promise<void>;
  getHydratedTask(uuid: string): Promise<HydratedTask | undefined>;
  cloneHydratedTask(task: HydratedTask): Promise<HydratedTask | undefined>;
  refreshTasks(): Promise<void>;
  updateTaskListStatuses(uuid: string): Promise<void>;
  syncTaskListWithConversation(uuid: string): Promise<void>;
  deleteTask(uuid: string): Promise<void>;
  getParentTask(uuid: string): HydratedTask | undefined;
  addNewTaskAfter(afterUuid: string, newTask: HydratedTask): Promise<HydratedTask | undefined>;
  saveHydratedTask(task: HydratedTask): Promise<void>;
  runHydratedTask(task: HydratedTask): Promise<void>;
  dispose(): void;
  handleMessageFromExtension(event: MessageEvent<any>): boolean;
  runAllTasks(): Promise<void>;
  exportTask(
    task: HydratedTask,
    options?: {
      fileName?: string;
      baseName?: string;
      format?: "markdown";
      shallow?: boolean;
    },
  ): Promise<void>;
  exportTasksToMarkdown(): Promise<void>;
  importTasksFromMarkdown(): Promise<void>;
}

class TaskAddedEvent extends Event {
  public static type = "task-added";

  constructor(public readonly task: HydratedTask) {
    super(TaskAddedEvent.type);
  }
}

/**
 * Implementation of the CurrentConversationTaskStore interface.
 * Provides reactive stores and methods for managing the top-level task.
 */
export class CurrentConversationTaskStore
  implements ICurrentConversationTaskStore, IDisposable, MessageConsumer
{
  public static key = "currentConversationTaskStore"; // for svelte context
  private _eventTarget = new EventTarget();

  /**
   * Creates a display-only version of the task store.
   *
   * Core concept: Provide task data for UI rendering without allowing modifications.
   *
   * This factory:
   * - Builds a fast lookup map of all tasks by UUID
   * - Implements the full interface but with no-op mutation methods
   * - Preserves type compatibility with the regular store
   *
   * @param rootTask - The root task to display
   * @returns A read-only task store
   */
  public static createReadOnlyStore(
    rootTask: HydratedTask | undefined,
  ): ICurrentConversationTaskStore {
    const rootTaskWritable = writable<HydratedTask | undefined>(rootTask);
    const uuidToTaskMap = new Map<string, HydratedTask>();

    // If we have a root task, populate the uuidToTask map
    if (rootTask) {
      // DFS and collect all tasks
      const stack: HydratedTask[] = [rootTask];
      while (stack.length > 0) {
        const currentTask = stack.pop()!;
        uuidToTaskMap.set(currentTask.uuid, currentTask);
        if (currentTask.subTasksData) {
          stack.push(...currentTask.subTasksData);
        }
      }
    }

    return {
      rootTaskUuid: writable(rootTask?.uuid),
      rootTask: rootTaskWritable,
      canShowTaskList: writable(true),
      uuidToTask: writable(uuidToTaskMap),
      createTask: () => Promise.resolve(""),
      updateTask: () => Promise.resolve(),
      getHydratedTask: () => Promise.resolve(undefined),
      cloneHydratedTask: () => Promise.resolve(undefined),
      refreshTasks: () => Promise.resolve(),
      updateTaskListStatuses: () => Promise.resolve(),
      syncTaskListWithConversation: () => Promise.resolve(),
      deleteTask: () => Promise.resolve(),
      getParentTask: () => undefined,
      addNewTaskAfter: () => Promise.resolve(undefined),
      saveHydratedTask: () => Promise.resolve(),
      runHydratedTask: () => Promise.resolve(),
      dispose: () => {},
      handleMessageFromExtension: () => false,
      runAllTasks: () => Promise.resolve(),
      exportTask: () => Promise.resolve(),
      exportTasksToMarkdown: () => Promise.resolve(),
      importTasksFromMarkdown: () => Promise.resolve(),
      isImportingExporting: writable(false),
      onTaskAdded: () => () => {},
    };
  }

  private _disposables: IDisposable[] = [];

  /**
   * The root task UUID for the current conversation.
   * This is derived from the conversation model's rootTaskUuid or id.
   */
  public rootTaskUuid: Readable<string | undefined>;

  /**
   * The root task for the current conversation.
   */
  private _rootTask: Writable<HydratedTask | undefined> = writable(undefined);

  /**
   * The root task for the current conversation (readonly).
   */
  public rootTask: Readable<HydratedTask | undefined> = readonly(this._rootTask);

  /**
   * Whether the task store is currently importing or exporting tasks.
   */
  private _isImportingExporting: Writable<boolean> = writable(false);
  public isImportingExporting: Readable<boolean> = readonly(this._isImportingExporting);

  /**
   * Whether the task list can be shown for the current conversation.
   */
  public canShowTaskList: Readable<boolean>;

  /**
   * How often to refresh the task list in milliseconds.
   */
  // eslint-disable-next-line @typescript-eslint/naming-convention
  public static readonly REFRESH_INTERVAL_MS = 2000;
  private _refreshInterval: NodeJS.Timeout | undefined;

  /**
   * A map of task UUIDs to their backlinks. Bootstrapped from the uuitToTask store.
   */
  private _backlinks: Writable<Record<string, string>> = writable({});

  /**
   * A map of task UUIDs to their hydrated tasks.
   */
  public uuidToTask: Readable<Map<string, HydratedTask>> = derived(this._rootTask, ($rootTask) => {
    const uuidToTask = new Map<string, HydratedTask>();
    if (!$rootTask) {
      return uuidToTask;
    }

    const backlinks: Record<string, string> = {};

    // DFS and collect all tasks
    const stack: HydratedTask[] = $rootTask ? [$rootTask] : [];
    while (stack.length > 0) {
      const currentTask = stack.pop()!;
      uuidToTask.set(currentTask.uuid, currentTask);
      if (currentTask.subTasksData) {
        stack.push(...currentTask.subTasksData);
        for (const subTaskUuid of currentTask.subTasks) {
          backlinks[subTaskUuid] = currentTask.uuid;
        }
      }
    }

    this._backlinks.set(backlinks);
    return uuidToTask;
  });

  constructor(
    private readonly _chatModel: ChatModel,
    private readonly _extensionClient: IExtensionClient,
    private readonly _conversationModel: ConversationModel,
    private readonly _agentConversationModel: AgentConversationModel,
  ) {
    // Create a derived store for the root task UUID
    this.rootTaskUuid = derived(
      this._conversationModel,
      (conversation) => conversation.rootTaskUuid,
    );

    // Set up a subscription to the rootTaskUuid to refresh tasks when it changes
    this._disposables.push({
      dispose: this.rootTaskUuid.subscribe(async (uuid) => {
        if (uuid) {
          // Notify the sidecar about the current root task UUID
          this._extensionClient.setCurrentRootTaskUuid(uuid);
          await this.refreshTasks();
        }
      }),
    });

    // Listen for new conversations to initialize the root task if needed
    this._disposables.push({
      dispose: this._conversationModel.onNewConversation(async () => {
        await this._maybeInitializeConversationRootTask();
      }),
    });

    this.canShowTaskList = derived(
      [this._chatModel.flags, this._agentConversationModel.isCurrConversationAgentic],
      ([$flags, $isCurrConversationAgentic]) => {
        return $flags.enableTaskList && $isCurrConversationAgentic;
      },
    );

    this._disposables.push({
      dispose: this.canShowTaskList.subscribe((canShow) => {
        if (canShow) {
          this._startRefreshInterval();
        } else {
          this._stopRefreshInterval();
        }
      }),
    });

    // Initialize with the current conversation
    this._maybeInitializeConversationRootTask();
  }

  /**
   * Disposes of the task store.
   */
  dispose(): void {
    for (const disposable of this._disposables) {
      disposable.dispose();
    }
    this._disposables = [];
  }

  /**
   * Handles messages from the extension.
   * @param _event - The message event (unused)
   * @returns True if the message was handled, false otherwise
   */
  handleMessageFromExtension(_event: MessageEvent<any>): boolean {
    // No messages to handle for task import/export since we're using browser APIs
    return false;
  }

  /**
   * Creates a task.
   * @param name - The name of the task
   * @param description - The description of the task
   * @param parentTaskUuid - The UUID of the parent task, if any
   * @returns A promise that resolves to the UUID of the created task
   */
  async createTask(name: string, description: string, parentTaskUuid?: string): Promise<string> {
    // Create the task using updateTask
    const uuid = await this._extensionClient.createTask(name, description, parentTaskUuid);

    // Refresh tasks to update the UI
    await this.refreshTasks();

    const task = get(this.rootTask);
    if (task) {
      this._eventTarget.dispatchEvent(new TaskAddedEvent(task));
    }
    return uuid;
  }

  /**
   * Updates a task.
   * @param uuid - The UUID of the task to update
   * @param updates - The updates to apply to the task
   * @param updatedBy - Who is updating the task
   * @returns A promise that resolves when the update is complete
   */
  async updateTask(
    uuid: string,
    updates: Partial<SerializedTask>,
    updatedBy: TaskUpdatedBy,
  ): Promise<void> {
    await this._extensionClient.updateTask(uuid, updates, updatedBy);
    await this.refreshTasks();
  }

  /**
   * Gets an hydrated task.
   * @param uuid - The UUID of the task to get
   * @returns A promise that resolves to the hydrated task, or undefined if not found
   */
  async getHydratedTask(uuid: string): Promise<HydratedTask | undefined> {
    return this._extensionClient.getHydratedTask(uuid);
  }

  /**
   * Refreshes the task for the current conversation.
   * If there's a root task UUID, it gets the hydrated task for that UUID.
   * @returns A promise that resolves when the refresh is complete
   */
  async refreshTasks(): Promise<void> {
    const rootTaskUuid = get(this.rootTaskUuid);

    if (rootTaskUuid) {
      // Get the hydrated task for the root task UUID
      const rootTask = await this._extensionClient.getHydratedTask(rootTaskUuid);

      this._rootTask.set(rootTask);
    } else {
      // If there's no root task UUID, clear the root task
      this._rootTask.set(undefined);
    }
  }

  public async syncTaskListWithConversation(uuid: string): Promise<void> {
    await this._updateTaskList(
      uuid,
      "Update the task list to reflect the current state of the conversation. " +
        "Add any new tasks that have been created, update the status of existing tasks, and remove any tasks that are no longer relevant. " +
        "The updated task list should reflect the current state of the conversation. " +
        "If the tasks are not detailed enough, please add new tasks to fill in the steps you think are necessary, " +
        "provide more details by adding more information to the description, or add sub-tasks",
      [
        TaskListRules.preferAddingTasks,
        TaskListRules.preferUpdatingTaskDetails,
        TaskListRules.preferUpdatingTaskStatus,
        TaskListRules.preferRemovingTasks,
      ],
    );
  }

  public async updateTaskListStatuses(uuid: string): Promise<void> {
    await this._updateTaskList(
      uuid,
      "Update the status of each task in the list to reflect the current state of the conversation.",
      [
        TaskListRules.allowUpdatingTaskStatus,
        TaskListRules.preventAddingTasks,
        TaskListRules.preventRemovingTasks,
        TaskListRules.preventUpdatingTaskDetails,
      ],
    );
  }

  /**
   * Creates a root task for the conversation if one does not already exist.
   * @returns A promise that resolves to the UUID of the root task
   */
  private async _maybeInitializeConversationRootTask(): Promise<string> {
    // If the conversation has a task UUID, do nothing
    const rootTaskUuid = get(this.rootTaskUuid);
    if (rootTaskUuid) {
      return rootTaskUuid;
    }

    const conversation = get(this._conversationModel);
    const curConversationId = conversation.id;

    // Create a root task for the conversation
    const taskName = `Conversation: ${conversation.name || "New Chat"}`;
    const taskDescription = `Root task for conversation ${curConversationId}`;

    // Create the root task with no parent
    const taskUuid = await this._extensionClient.createTask(taskName, taskDescription);

    // Set the root task UUID in the conversation model
    // The derived store will automatically update
    this._conversationModel.rootTaskUuid = taskUuid;

    // Notify the sidecar about the new root task UUID
    this._extensionClient.setCurrentRootTaskUuid(taskUuid);

    return taskUuid;
  }

  /**
   * Enhances a task list by sending a silent exchange to the model.
   * The model will read the markdown representation of the task list and output an updated version.
   * @param currentTaskListMarkdown - The current task list in markdown format
   * @returns A promise that resolves to the enhanced task list in markdown format
   */
  public async _updateTaskList(
    uuid: string,
    prompt: string = "",
    rules: TaskListRules[] = [],
  ): Promise<void> {
    try {
      const rootTask = get(this._rootTask);
      if (!rootTask) {
        return;
      }

      const taskToEnhance = findTaskInTree(rootTask, uuid);
      if (!taskToEnhance) {
        return;
      }

      const currentTaskListMarkdown = getMarkdownRepresentation(taskToEnhance);
      const instructions =
        prompt +
        "\n" +
        "Follow these rules when updating the task list:\n" +
        rules?.join("\n") +
        "\n" +
        // Generic rules below
        "Maintain the hierarchical structure, given by the `Example task list structure`, with proper indentation. " +
        'If a task is new, give it a UUID of "NEW_UUID". ' +
        "Always structure each task with [ ] for not started, " +
        "[/] for in progress, [x] for completed, and [-] for cancelled. \n" +
        "Example task list structure: \n" +
        SAMPLE_TASK_MARKDOWN +
        "\n\n" +
        "Current working task list - This is ACTUAL working task list to use, read from, and modify:\n" +
        currentTaskListMarkdown +
        "\n\n" +
        "Only output the updated markdown without any additional explanation. " +
        "Do not include any sentences before or after the markdown, ONLY the markdown itself. " +
        "Do not use a tool call, just return the markdown in plaintext, without tools, or anything else. " +
        "Just plaintext markdown.";

      // Send a silent exchange by prompting the model to enhance the task list
      const { responseText: enhancedTaskList } = await this._conversationModel.sendSilentExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: instructions,
        disableSelectedCodeDetails: true,
        /* eslint-enable @typescript-eslint/naming-convention */
      });

      // Log the task lists for debugging
      /* eslint-disable no-console */
      console.log("Updating task list for conversation", get(this._conversationModel).id);
      console.log({
        instructions,
        currentTaskListMarkdown,
        enhancedTaskList,
      });
      /* eslint-enable no-console */

      // Parse the markdown into a task tree
      const newTaskTree = parseMarkdownToTaskTree(enhancedTaskList);

      // Preserve the original UUID
      newTaskTree.uuid = taskToEnhance.uuid;

      // Use the updateHydratedTask method to update the entire tree at once
      const { created, updated, deleted } = await this._extensionClient.updateHydratedTask(
        newTaskTree,
        TaskUpdatedBy.AGENT,
      );

      // Log the results
      /* eslint-disable no-console */
      console.log("Task tree update results:", { created, updated, deleted });
      /* eslint-enable no-console */
    } finally {
      await this.refreshTasks();
    }
  }

  public getParentTask(uuid: string): HydratedTask | undefined {
    const backlinks = get(this._backlinks);
    const parentUuid = backlinks[uuid];

    if (!parentUuid) {
      return undefined;
    }

    const uuidToTask = get(this.uuidToTask);
    return uuidToTask.get(parentUuid);
  }

  public async addNewTaskAfter(
    afterUuid: string,
    newTask: HydratedTask,
  ): Promise<HydratedTask | undefined> {
    // Get the parent task of the target task
    const parentTask = this.getParentTask(afterUuid);
    if (!parentTask) {
      // If no parent found, we can't add a sibling task
      return undefined;
    }

    // Find the position of the target task in the parent's subtasks
    const targetIndex = parentTask.subTasks.indexOf(afterUuid);
    if (targetIndex === -1) {
      // Target task not found in parent's subtasks
      return undefined;
    }

    // Clone the new task to ensure we don't modify the original
    const clonedTask = await this.cloneHydratedTask(newTask);
    if (!clonedTask) {
      return undefined;
    }

    // Insert the cloned task after the target task
    // Remove 0 elements at targetIndex + 1 and insert clonedTask.uuid
    parentTask.subTasks.splice(targetIndex + 1, 0, clonedTask.uuid);

    // Update the parent task in the store
    await this.saveHydratedTask(parentTask);
    return clonedTask;
  }

  public async deleteTask(uuid: string): Promise<void> {
    const backlinks = get(this._backlinks);
    const parentTaskUuid = backlinks[uuid];
    if (!parentTaskUuid) {
      return;
    }

    const parentTask = await this.getHydratedTask(parentTaskUuid);
    if (!parentTask) {
      return;
    }

    // Remove the task from the parent's subtasks
    parentTask.subTasks = parentTask.subTasks.filter((taskUuid) => taskUuid !== uuid);
    parentTask.subTasksData = parentTask.subTasksData?.filter((task) => task.uuid !== uuid);

    // Update the parent task
    await this.updateTask(parentTask.uuid, { subTasks: parentTask.subTasks }, TaskUpdatedBy.USER);
  }

  public async saveHydratedTask(task: HydratedTask): Promise<void> {
    await this._extensionClient.updateHydratedTask(task, TaskUpdatedBy.USER);
    await this.refreshTasks();
  }

  public async cloneHydratedTask(task: HydratedTask): Promise<HydratedTask | undefined> {
    // Clone the task with new UUIDs
    const clonedTask = deepCloneTask(task);

    // Create a new root task for the conversation
    const newTaskUuid = await this.createTask(clonedTask.name, clonedTask.description);
    if (!newTaskUuid) {
      return undefined;
    }
    clonedTask.uuid = newTaskUuid;

    // Create the hierarchy
    await this._extensionClient.updateHydratedTask(clonedTask, TaskUpdatedBy.USER);
    return await this.getHydratedTask(newTaskUuid);
  }

  public async runAllTasks(): Promise<void> {
    const rootTask = get(this._rootTask);
    if (!rootTask) {
      return;
    }

    this.runHydratedTask(rootTask, {
      message: "Run all tasks for the task list: ",
    });
  }

  /**
   * Runs a hydrated task by sending it to the agent for processing.
   * This will interrupt any current agent activity and shift focus to the specified task.
   * @param task - The hydrated task to run
   * @returns A promise that resolves when the task has been sent to the agent
   */
  public async runHydratedTask(
    task: HydratedTask,
    options?: {
      message?: string;
      newConversation?: boolean;
    },
  ): Promise<void> {
    // Interrupt the agent
    const currConversationId = this._chatModel.currentConversationId;
    await this._agentConversationModel.interruptAgent();

    // If we're not in the conversation we started on, stop doing any work
    if (currConversationId !== this._chatModel.currentConversationId) {
      return;
    }

    // See if we should create a new conversation
    if (options?.newConversation) {
      // Try to clone the current task. If we can't, then early return.
      const newTask = await this.cloneHydratedTask(task);
      if (!newTask) {
        return;
      }

      // Create a new conversation with a root task UUID that we define
      const newRootTaskUuid = await this.createTask(task.name, task.description);
      await this.saveHydratedTask({
        uuid: newRootTaskUuid,
        name: task.name,
        description: task.description,
        state: TaskState.NOT_STARTED,
        subTasks: [newTask.uuid],
        subTasksData: [newTask],
        lastUpdated: Date.now(),
        lastUpdatedBy: TaskUpdatedBy.USER,
      });

      // If the conversation has changed, this is now a no-op
      if (currConversationId !== this._chatModel.currentConversationId) {
        return;
      }
      await this._chatModel.setCurrentConversation(undefined, true, {
        newTaskUuid: newRootTaskUuid,
      });
    }

    const jsonRepr: JSONContent = {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [
            { type: "text", text: options?.message ?? "Please shift focus to work on task: " },
            {
              type: "mention",
              attrs: {
                id: `UUID:${task.uuid} NAME:${task.name} DESCRIPTION:${task.description}`,
                label: task.name,
                data: {
                  ...task,
                  id: `UUID:${task.uuid} NAME:${task.name} DESCRIPTION:${task.description}`,
                  label: task.name,
                },
              },
            },
          ],
        },
      ],
    };

    await this._chatModel.currentConversationModel.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "",
      rich_text_json_repr: jsonRepr,
      structured_request_nodes:
        this._chatModel.currentConversationModel.createStructuredRequestNodes(jsonRepr),
      status: ExchangeStatus.draft,
      model_id: this._chatModel.currentConversationModel.selectedModelId ?? undefined,
      /* eslint-enable @typescript-eslint/naming-convention */
    });
    await this.refreshTasks();
  }

  private _startRefreshInterval(): void {
    this._refreshInterval = setInterval(async () => {
      await this.refreshTasks();
    }, CurrentConversationTaskStore.REFRESH_INTERVAL_MS);
  }

  private _stopRefreshInterval(): void {
    if (this._refreshInterval) {
      clearInterval(this._refreshInterval);
      this._refreshInterval = undefined;
    }
  }

  /**
   * Generic export method that takes a hydrated task and exports it to a file.
   * @param task - The hydrated task to export
   * @param options - Export options including file name, format, etc.
   * @returns A promise that resolves when the export is complete
   */
  public async exportTask(
    task: HydratedTask,
    options?: {
      fileName?: string;
      baseName?: string;
      format?: "markdown";
      shallow?: boolean;
    },
  ): Promise<void> {
    try {
      this._isImportingExporting.set(true);

      // Generate content based on format (currently only markdown supported)
      const format = options?.format || "markdown";
      let content: string;
      let fileExtension: string;

      switch (format) {
        case "markdown":
          content = getMarkdownRepresentation(task, options?.shallow);
          fileExtension = "md";
          break;
        default:
          throw new Error(`Unsupported export format: ${format}`);
      }

      // Generate file name
      let fileName: string;
      if (options?.fileName) {
        fileName = options.fileName;
      } else {
        const baseName = options?.baseName || task.name || "Task";
        const sanitizedBaseName = baseName.replace(/[/:*?"<>|\s]/g, "_");
        const timestamp = new Date().toISOString().replace(/[:.]/g, "-").slice(0, 19); // Format: YYYY-MM-DDTHH-MM-SS
        fileName = `${sanitizedBaseName}_${timestamp}.${fileExtension}`;
      }

      // Use chatCreateFile to create the file
      this._extensionClient.createFile(content, fileName);
    } catch (error) {
      console.error("Error exporting task:", error);
    } finally {
      this._isImportingExporting.set(false);
    }
  }

  /**
   * Exports the current task tree to a markdown file.
   * This will create a new file with the markdown representation of the task tree.
   * @returns A promise that resolves when the export is complete
   */
  public async exportTasksToMarkdown(): Promise<void> {
    const rootTask = get(this._rootTask);
    if (!rootTask) {
      return;
    }

    // Get conversation name for base name
    const conversation = get(this._conversationModel);
    const baseName = conversation.name || "Tasks";

    // Use the generic export method
    await this.exportTask(rootTask, {
      baseName,
      format: "markdown",
    });
  }

  /**
   * Imports tasks from a markdown file.
   * This will open a file open dialog using a hidden input element
   * and parse the markdown file into a task tree.
   * @returns A promise that resolves when the import is complete
   */
  public async importTasksFromMarkdown(): Promise<void> {
    try {
      this._isImportingExporting.set(true);

      // Create a hidden file input element
      const input = document.createElement("input");
      input.type = "file";
      input.accept = ".md,text/markdown";
      input.style.display = "none";

      // Create a promise that resolves when the file is selected
      const filePromise = new Promise<File | null>((resolve) => {
        input.onchange = () => {
          resolve(input.files ? input.files[0] : null);
        };

        // Also handle the case where user cancels the dialog
        input.oncancel = () => {
          resolve(null);
        };
      });

      // Add to DOM and trigger the file picker
      document.body.appendChild(input);
      input.click();

      // Wait for the file to be selected
      const file = await filePromise;

      // Clean up the input element
      document.body.removeChild(input);

      if (file) {
        // Read the file content
        const fileContent = await file.text();

        // Process the file content
        await this._processImportedFileContent(fileContent);
      }
    } catch (error) {
      console.error("Error importing tasks from markdown:", error);
    } finally {
      this._isImportingExporting.set(false);
    }
  }

  /**
   * Processes the content of an imported markdown file.
   * This method appends/merges the imported tasks to the existing task tree
   * and assigns new UUIDs to all imported tasks to avoid conflicts.
   * @param fileContent - The content of the loaded file
   */
  private async _processImportedFileContent(fileContent: string): Promise<void> {
    try {
      if (!fileContent.trim()) {
        console.error("Empty file content");
        return;
      }

      // Parse the markdown into a task tree
      const importedTaskTree = parseMarkdownToTaskTree(fileContent);
      if (!importedTaskTree) {
        console.error("Failed to parse task tree from markdown");
        return;
      }

      // Get the current root task
      const rootTask = get(this._rootTask);
      if (!rootTask) {
        console.error("No root task found");
        return;
      }

      // Clone the imported task tree with new UUIDs to avoid conflicts
      // We skip the root task of the imported tree and only import its subtasks
      const importedSubTasks = importedTaskTree.subTasksData || [];
      const clonedImportedSubTasks = importedSubTasks.map((subTask) =>
        deepCloneTask(subTask, { keepUuid: false }),
      );

      // Merge the cloned imported subtasks with existing subtasks
      const existingSubTasks = rootTask.subTasksData || [];
      const existingSubTaskUuids = rootTask.subTasks || [];

      const mergedSubTasksData = [...existingSubTasks, ...clonedImportedSubTasks];
      const mergedSubTaskUuids = [
        ...existingSubTaskUuids,
        ...clonedImportedSubTasks.map((task) => task.uuid),
      ];

      // Create the updated root task with merged subtasks
      const updatedRootTask: HydratedTask = {
        ...rootTask,
        subTasks: mergedSubTaskUuids,
        subTasksData: mergedSubTasksData,
      };

      // Update the task tree in the store
      await this._extensionClient.updateHydratedTask(updatedRootTask, TaskUpdatedBy.USER);

      // Refresh the tasks to update the UI
      await this.refreshTasks();
    } catch (error) {
      console.error("Error processing imported file content:", error);
    }
  }

  public onTaskAdded(listener: (event: TaskAddedEvent) => void): () => void {
    // Wrap the listener to cast the event type
    const cb = (event: Event) => listener(event as TaskAddedEvent);
    this._eventTarget.addEventListener(TaskAddedEvent.type, cb);
    return () => {
      this._eventTarget.removeEventListener(TaskAddedEvent.type, cb);
    };
  }
}

/**
 * Removes all tasks with cancelled status from a task tree while preserving the hierarchical structure.
 *
 * @param hydratedTask - A hydrated task object (which may contain nested subtasks)
 * @returns A new hydrated task object with all cancelled tasks removed from the entire tree, or null if the root task is cancelled
 */
export function clearCancelledTasks(hydratedTask: HydratedTask): HydratedTask | null {
  // If the root task itself is cancelled, return null
  if (hydratedTask.state === TaskState.CANCELLED) {
    return null;
  }

  // Create a new task object (immutable operation)
  const clearedTask: HydratedTask = {
    ...hydratedTask,
    subTasks: [],
    subTasksData: [],
  };

  // Process subtasks if they exist
  if (hydratedTask.subTasksData && hydratedTask.subTasksData.length > 0) {
    const clearedSubTasks: HydratedTask[] = [];
    const clearedSubTaskUuids: string[] = [];

    for (const subTask of hydratedTask.subTasksData) {
      // Recursively process each subtask
      const clearedSubTask = clearCancelledTasks(subTask);

      // Only include the subtask if it's not cancelled (clearCancelledTasks returns non-null)
      if (clearedSubTask !== null) {
        clearedSubTasks.push(clearedSubTask);
        clearedSubTaskUuids.push(clearedSubTask.uuid);
      }
    }

    // Update the cleared task with the filtered subtasks
    clearedTask.subTasksData = clearedSubTasks;
    clearedTask.subTasks = clearedSubTaskUuids;
  }

  return clearedTask;
}

// Local prompt options, do NOT export or use elsewhere
enum TaskListRules {
  preferAddingTasks = "You should add new tasks if any tasks are missing.",
  allowAddingTasks = "You may add new tasks if necessary.",
  preventAddingTasks = "Do not add any new tasks.",
  preferRemovingTasks = "You should remove any tasks that are no longer relevant by not including them in the task list.",
  allowRemovingTasks = "You may remove tasks if necessary by not including them in the task list.",
  preventRemovingTasks = "Do not remove any tasks.",
  preferUpdatingTaskStatus = "You should update the status of tasks if their status is outdated.",
  allowUpdatingTaskStatus = "You may update the status of tasks if necessary.",
  preventUpdatingTaskStatus = "Do not update the status of any tasks.",
  preferUpdatingTaskDetails = "You should update the details of tasks if their details are outdated.",
  allowUpdatingTaskDetails = "You may update the details of tasks if necessary.",
  preventUpdatingTaskDetails = "Do not update the details of any tasks.",
}
