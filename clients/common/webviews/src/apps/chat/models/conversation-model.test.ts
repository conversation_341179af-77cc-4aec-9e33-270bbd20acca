import {
  ChatRequestNodeType,
  ChatResultNodeType,
  PersonaType,
  type ChatRequestIdeState,
  type ChatResultNode,
  type ImageFormatType,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import type { JSONContent } from "@tiptap/core";
import { beforeEach, describe, expect, test, vi } from "vitest";

import { host } from "$common-webviews/src/common/hosts/__mocks__/host";
import { MessageBroker } from "$common-webviews/src/common/utils/message-broker";
import {
  WebViewMessageType,
  type AsyncWebViewMessage,
  type ChatModelReply,
  type WebViewMessage,
  type WorkspaceFileChunk,
} from "$vscode/src/webview-providers/webview-messages";
import { formatChatModelReplyStream } from "../extension-client";
import {
  ChatItemType,
  ExchangeStatus,
  isChatItemExchangeWithStatus,
  isChatItemHistorySummary,
  SeenState,
  type ExchangeWithStatus,
  type HistorySummaryMessage,
} from "../types/chat-message";
import { ChatModel } from "./chat-model";
import { SpecialContextInputModel } from "./context-model";
import { ConversationModel, formatHistory } from "./conversation-model";
import type { IConversation } from "./types";

async function tick() {
  await new Promise((resolve) => setTimeout(resolve, 0));
}

/**
 * The test kit for testing the ConversationModel.
 *
 * It wraps a chatModel with a fake host and some additional utilities that
 * allow us to control and yield responses. Note that we can only have a single
 * stream at a time with this test kit -- whenever a new message comes in, all further `yield`
 * calls will be instead yielded to the current message.
 *
 * Some useful utilities include:
 * - `sendMsg` - sends a message, performs all async wrapping, etc.
 * - `yieldMsg` - yields a message to the current stream created by the last `sendMsg` call.
 * - `echo` - a helper function that sends a message, yields an echo response, and ends the stream
 */
class ConversationModelTestKit {
  private _chatModel: ChatModel;
  private _contextModel: SpecialContextInputModel;
  private _currStreamIdx: number = 0;
  private _currAsyncReqId: string = "";
  private _currMessageReqId: number = 0;

  constructor() {
    host.postMessage.mockImplementation(this.fakeHostRcvMsg);
    this._contextModel = new SpecialContextInputModel();
    this._chatModel = new ChatModel(new MessageBroker(host), host, this._contextModel);
  }

  get context(): SpecialContextInputModel {
    return this._contextModel;
  }

  get conversation(): ConversationModel {
    return this._chatModel.currentConversationModel;
  }

  get chat(): ChatModel {
    return this._chatModel;
  }

  echo = async (...msgs: string[]): Promise<void> => {
    for (const msg of msgs) {
      await this.sendMsg(msg);
      await this.yieldMsg("echo: " + msg);
      await this.yieldMsg(undefined);
    }
  };

  // When a host (the test) "sends" a message from the extension, it really just does
  // a dispatch event straight to the window
  fakeHostSendMsg = vi.fn(async (data: any) => {
    window.dispatchEvent(new MessageEvent("message", { data }));
    await tick();
  });

  // When a host (the test) "receives" a message from the extension, we handle the
  // message types. If the message is an async message, we start a new message context
  // by updating the current async request ID and message request ID, and resets the
  // stream index to 0.
  fakeHostRcvMsg = vi.fn(async (msg: WebViewMessage) => {
    if (msg.type !== WebViewMessageType.asyncWrapper || !msg.baseMsg) {
      throw new Error("Unexpected message type");
    }

    // When user first sends message, receive an empty message with request ID
    this._currStreamIdx = 0;
    this._currAsyncReqId = msg.requestId;
    this._currMessageReqId++;
    switch (msg.baseMsg.type) {
      case WebViewMessageType.chatUserMessage: {
        return this.fakeHostSendMsg({
          type: WebViewMessageType.asyncWrapper,
          requestId: this._currAsyncReqId.toString(),
          error: null,
          baseMsg: {
            type: WebViewMessageType.chatModelReply,
            data: {
              text: "",
              requestId: this._currMessageReqId.toString(),
              streaming: true,
            },
          },
          streamCtx: {
            streamMsgIdx: 0,
            streamNextRequestId: `${this._currAsyncReqId}-0`,
          },
        });
      }
      case WebViewMessageType.openConfirmationModal: {
        return this.fakeHostSendMsg({
          type: WebViewMessageType.asyncWrapper,
          requestId: this._currAsyncReqId.toString(),
          error: null,
          baseMsg: {
            type: WebViewMessageType.confirmationModalResponse,
            data: {
              ok: true,
            },
          },
        });
      }
      case WebViewMessageType.resolveWorkspaceFileChunkRequest: {
        const chunk = msg.baseMsg.data;
        return this.fakeHostSendMsg({
          type: WebViewMessageType.asyncWrapper,
          requestId: this._currAsyncReqId.toString(),
          error: null,
          baseMsg: {
            type: WebViewMessageType.resolveWorkspaceFileChunkResponse,
            data: {
              repoRoot: "example-repo-root",
              pathName: `example/path/name/file-for-${chunk.blobName}`,
              fullRange: {
                startLineNumber: 0,
                startColumn: 0,
                endLineNumber: 1,
                endColumn: 10,
              },
            },
          },
        });
      }
      case WebViewMessageType.getChatRequestIdeStateRequest: {
        return this.fakeHostSendMsg({
          type: WebViewMessageType.asyncWrapper,
          requestId: this._currAsyncReqId.toString(),
          error: null,
          baseMsg: {
            type: WebViewMessageType.getChatRequestIdeStateResponse,
            data: mockIdeState,
          },
        });
      }
      default:
        throw new Error(`Unexpected async message type: ${msg.baseMsg.type}`);
    }
  });

  sendMsg = async (msg: string): Promise<void> => {
    this.conversation.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: msg,
      /* eslint-enable @typescript-eslint/naming-convention */
      status: ExchangeStatus.draft,
    });
    await tick();
  };

  yieldNode = async (node: ChatResultNode): Promise<void> => {
    this._currStreamIdx++;

    const currMsg: AsyncWebViewMessage<WebViewMessage> = {
      type: WebViewMessageType.asyncWrapper,
      requestId: `${this._currAsyncReqId}-${this._currStreamIdx - 1}`,
      error: null,
      baseMsg: {
        type: WebViewMessageType.chatModelReply,
        data: {
          text: "",
          requestId: this._currMessageReqId.toString(),
          nodes: [node],
          workspaceFileChunks: [],
        },
      },
      streamCtx: {
        streamMsgIdx: this._currStreamIdx,
        streamNextRequestId: `${this._currAsyncReqId}-${this._currStreamIdx}`,
      },
    };
    return this.fakeHostSendMsg(currMsg);
  };

  yieldMsg = async (msg: string | undefined, chunks: WorkspaceFileChunk[] = []): Promise<void> => {
    this._currStreamIdx++;

    const currMsg: AsyncWebViewMessage<WebViewMessage> = {
      type: WebViewMessageType.asyncWrapper,
      requestId: `${this._currAsyncReqId}-${this._currStreamIdx - 1}`,
      error: null,
      baseMsg: {
        type: WebViewMessageType.chatModelReply,
        data: {
          text: msg || "",
          requestId: this._currMessageReqId.toString(),
          workspaceFileChunks: chunks,
        },
      },
    };
    if (msg === "error") {
      currMsg.baseMsg = null;
      currMsg.error = "This is an error. Oh no!";
      currMsg.streamCtx = {
        streamMsgIdx: this._currStreamIdx,
        streamNextRequestId: `${this._currAsyncReqId}-${this._currStreamIdx}`,
        isStreamComplete: true,
      };
    } else if (msg !== undefined) {
      currMsg.streamCtx = {
        streamMsgIdx: this._currStreamIdx,
        streamNextRequestId: `${this._currAsyncReqId}-${this._currStreamIdx}`,
      };
    } else {
      currMsg.streamCtx = {
        streamMsgIdx: this._currStreamIdx,
        streamNextRequestId: `${this._currAsyncReqId}-${this._currStreamIdx}`,
        isStreamComplete: true,
      };
    }
    return this.fakeHostSendMsg(currMsg);
  };
}

describe("ConversationModel", async () => {
  test("should update the last exchange stream", async () => {
    // MOCKING
    const testKit = new ConversationModelTestKit();
    const conversation = testKit.conversation;

    await testKit.sendMsg("Hello");

    let turn = conversation.chatHistory[0] as ExchangeWithStatus;
    expect(turn.response_text).toBe("");

    await testKit.yieldMsg("Init response");
    turn = conversation.chatHistory[0] as ExchangeWithStatus;
    expect(turn.response_text).toBe("Init response");

    await testKit.yieldMsg("Second response");
    turn = conversation.chatHistory[0] as ExchangeWithStatus;
    expect(turn.response_text).toBe("Init responseSecond response");

    await testKit.yieldMsg(undefined);
    turn = conversation.chatHistory[0] as ExchangeWithStatus;
    expect(turn.status).toBe(ExchangeStatus.success);
  });

  describe("last exchange", async () => {
    test("should be null when there is no exchange", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;
      expect(conversation.lastExchange).toBe(null);
    });

    test("should be the last exchange", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.sendMsg("Hello");
      expect(conversation.lastExchange).toStrictEqual(
        expect.objectContaining({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "Hello",
          status: ExchangeStatus.sent,
          request_id: conversation.lastExchange?.request_id,
          seen_state: SeenState.unseen,
          /* eslint-enable @typescript-eslint/naming-convention */
        }),
      );
    });

    test("should be updated by stream", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const exchange: ExchangeWithStatus = {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Hello",
        /* eslint-enable @typescript-eslint/naming-convention */
        status: ExchangeStatus.draft,
      };
      conversation.sendExchange(exchange);
      await tick();

      await testKit.yieldMsg("Init response");
      expect(conversation.lastExchange?.chatItemType).toBe(undefined);
      expect(conversation.lastExchange?.response_text).toBe("Init response");
      await testKit.yieldMsg("Second response");
      expect(conversation.lastExchange?.response_text).toBe("Init responseSecond response");
      await testKit.yieldMsg(undefined);
      expect(conversation.lastExchange?.status).toBe(ExchangeStatus.success);
    });
  });

  test("should be replaced by MAIN_TEXT_FINISHED node", async () => {
    const testKit = new ConversationModelTestKit();
    const conversation = testKit.conversation;

    const exchange: ExchangeWithStatus = {
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Hello",
      /* eslint-enable @typescript-eslint/naming-convention */
      status: ExchangeStatus.draft,
    };
    conversation.sendExchange(exchange);
    await tick();

    await testKit.yieldMsg("Init response");
    expect(conversation.lastExchange?.chatItemType).toBe(undefined);
    expect(conversation.lastExchange?.response_text).toBe("Init response");
    await testKit.yieldMsg("Second response");
    expect(conversation.lastExchange?.response_text).toBe("Init responseSecond response");
    await testKit.yieldNode({
      id: 0,
      type: ChatResultNodeType.MAIN_TEXT_FINISHED,
      content: "Replaced response",
    });
    expect(conversation.lastExchange?.response_text).toBe("Replaced response");
    await testKit.yieldMsg(undefined);
    expect(conversation.lastExchange?.status).toBe(ExchangeStatus.success);
  });

  describe("history", () => {
    test("should be empty when there is no exchange", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;
      expect(conversation.chatHistory).toEqual([]);
    });

    test("has correct length", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.echo("Hello");
      expect(conversation.chatHistory.length).toBe(1);
      await testKit.echo("World");
      expect(conversation.chatHistory.length).toBe(2);
    });

    test("clear history should work", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.echo("Hello");
      expect(conversation.chatHistory.length).toBe(1);
      conversation.clearHistory();
      expect(conversation.chatHistory).toEqual([]);
    });

    test("clear history from should work", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.echo("Hello", "World", "Bye");
      expect(conversation.chatHistory.length).toBe(3);

      expect(conversation.chatHistory[1].request_id).toBeDefined();
      await conversation.clearHistoryFrom(conversation.chatHistory[1].request_id as string);
      expect(conversation.chatHistory.length).toBe(1);
    });

    test("historyTo computed correct without inclusive", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.echo("Hello", "World");
      expect(conversation.chatHistory.length).toBe(2);

      const lastRequestId = conversation.chatHistory[1].request_id as string;
      expect(conversation.historyTo(lastRequestId)).toEqual([conversation.chatHistory[0]]);
    });

    test("historyTo computed correct with inclusive", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.echo("Hello", "World");
      expect(conversation.chatHistory.length).toBe(2);
      const lastRequestId = conversation.chatHistory[1].request_id as string;
      expect(conversation.historyTo(lastRequestId, true)).toEqual(conversation.chatHistory);
    });

    test("historyFrom computed correct", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.echo("Hello", "World");
      expect(conversation.chatHistory.length).toBe(2);

      const firstRequestId = conversation.chatHistory[0].request_id as string;
      expect(conversation.historyFrom(firstRequestId)).toEqual(conversation.chatHistory);
    });

    test("historyFrom computed correct with inclusive", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.echo("Hello", "World");
      expect(conversation.chatHistory.length).toBe(2);

      const firstRequestId = conversation.chatHistory[0].request_id as string;
      expect(conversation.historyFrom(firstRequestId, true)).toEqual(conversation.chatHistory);
    });
  });

  describe("getters", async () => {
    describe("hasDraft", () => {
      test("should return true when there is text content", () => {
        const testKit = new ConversationModelTestKit();
        const conversation = testKit.conversation;

        conversation.saveDraftExchange("Hello");
        expect(conversation.hasDraft).toBe(true);
      });

      test("should return false when there is no content", () => {
        const testKit = new ConversationModelTestKit();
        const conversation = testKit.conversation;

        conversation.saveDraftExchange("");
        expect(conversation.hasDraft).toBe(false);
      });

      test("should return true when there is an image but no text", () => {
        const testKit = new ConversationModelTestKit();
        const conversation = testKit.conversation;

        // Create a JSON content with an image node
        const jsonWithImage: JSONContent = {
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "image",
                  attrs: {
                    src: "test-image.jpg",
                    title: "Test Image",
                    isLoading: false,
                  },
                },
              ],
            },
          ],
        };

        conversation.saveDraftExchange("", jsonWithImage);
        expect(conversation.hasDraft).toBe(true);
      });

      test("should return false when there is a loading image", () => {
        const testKit = new ConversationModelTestKit();
        const conversation = testKit.conversation;

        // Create a JSON content with a loading image node
        const jsonWithLoadingImage: JSONContent = {
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "image",
                  attrs: {
                    src: "test-image.jpg",
                    title: "Test Image",
                    isLoading: true,
                  },
                },
              ],
            },
          ],
        };

        conversation.saveDraftExchange("", jsonWithLoadingImage);
        // We still consider this as having content, even if it's loading
        expect(conversation.hasDraft).toBe(true);
      });
    });

    test("canSendDraft", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      conversation.saveDraftExchange("Hello");
      expect(conversation.canSendDraft).toBe(true);
      conversation.saveDraftExchange("");
      expect(conversation.canSendDraft).toBe(false);
    });

    test("canCancelMessage", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.sendMsg("Hello");
      await testKit.yieldMsg("Init response");
      expect(conversation.canCancelMessage).toBe(true);
      await testKit.yieldMsg(undefined);
      expect(conversation.canCancelMessage).toBe(false);
    });

    test("canClearHistory", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      conversation.saveDraftExchange("Hello");
      expect(conversation.canClearHistory).toBe(false);
      conversation.clearDraftExchange();
      expect(conversation.canClearHistory).toBe(false);

      await testKit.sendMsg("Hello");
      expect(conversation.canClearHistory).toBe(false);
      await testKit.yieldMsg("Init response");
      await testKit.yieldMsg(undefined);
      expect(conversation.canClearHistory).toBe(true);
    });

    test("successfulMessages", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.echo("Hello");
      expect(conversation.successfulMessages.length).toBe(1);
    });

    test("successfulMessages includes history summary messages", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      // Add a regular exchange
      await testKit.echo("Hello");
      expect(conversation.successfulMessages.length).toBe(1);

      // Add a history summary message
      const historySummaryMessage: HistorySummaryMessage = {
        /* eslint-disable @typescript-eslint/naming-convention */
        chatItemType: ChatItemType.historySummary,
        summaryVersion: 2,
        request_id: "summary-request-id",
        request_message: "Summarize the conversation",
        response_text: "This is a summary of the conversation",
        structured_output_nodes: [
          {
            id: 0,
            type: ChatResultNodeType.RAW_RESPONSE,
            content: "This is a summary of the conversation",
          },
        ],
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      conversation.addChatItem(historySummaryMessage);
      expect(conversation.successfulMessages.length).toBe(2);
      expect(conversation.successfulMessages[1]).toBe(historySummaryMessage);
    });

    test("requestId updated on success", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      // Send message
      conversation.sendExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Hello",
        /* eslint-enable @typescript-eslint/naming-convention */
        status: ExchangeStatus.draft,
      });
      // This test wants to check the intermediate state of the last exchange before
      // we receive any results from sendUserMessage. To get the asynchrony to work
      // right, we need to introduce a tick to keep the conversation in a temporary
      // state.
      const originalSendUserMessage = conversation["sendUserMessage"].bind(conversation);
      const mockedSendUserMessage = async function* (
        requestId: string,
        exchange: ExchangeWithStatus,
        silent: boolean,
      ) {
        await tick();
        yield* originalSendUserMessage(requestId, exchange, silent);
      };
      conversation["sendUserMessage"] = mockedSendUserMessage;

      await tick();
      const requestId = conversation.lastExchange?.request_id;
      expect(requestId).toBeDefined();
      await tick();

      // Get back initial response
      await testKit.yieldMsg("Init response");
      expect(conversation.lastExchange?.request_id).toBeDefined();
      expect(conversation.lastExchange?.request_id).not.toBe(requestId);
      const newRequestId = conversation.lastExchange?.request_id;

      // Finish stream
      await testKit.yieldMsg(undefined);
      expect(conversation.lastExchange?.request_id).not.toBe(requestId);
      expect(conversation.lastExchange?.request_id).toBe(newRequestId);
    });

    test("requestId updated on failure", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      // Send message
      conversation.sendExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Hello",
        /* eslint-enable @typescript-eslint/naming-convention */
        status: ExchangeStatus.draft,
      });
      // This test wants to check the intermediate state of the last exchange before
      // we receive any results from sendUserMessage. To get the asynchrony to work
      // right, we need to introduce a tick to keep the conversation in a temporary
      // state.
      const originalSendUserMessage = conversation["sendUserMessage"].bind(conversation);
      const mockedSendUserMessage = async function* (
        requestId: string,
        exchange: ExchangeWithStatus,
        silent: boolean,
      ) {
        await tick();
        yield* originalSendUserMessage(requestId, exchange, silent);
      };
      conversation["sendUserMessage"] = mockedSendUserMessage;

      await tick();
      const requestId = conversation.lastExchange?.request_id;
      expect(requestId).toBeDefined();
      await tick();

      // Get back initial response
      await testKit.yieldMsg("Init response");
      expect(conversation.lastExchange?.request_id).toBeDefined();
      expect(conversation.lastExchange?.request_id).not.toBe(requestId);
      const newRequestId = conversation.lastExchange?.request_id;

      // Finish stream
      await testKit.yieldMsg("error");
      expect(conversation.lastExchange?.request_id).not.toBe(requestId);
      expect(conversation.lastExchange?.request_id).toBe(newRequestId);
    });

    test("awaitingReply", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      await testKit.sendMsg("Hello");
      expect(conversation.awaitingReply).toBe(true);
      await testKit.yieldMsg("Init response");
      await testKit.yieldMsg(undefined);
      expect(conversation.awaitingReply).toBe(false);
    });
  });

  describe("static methods", () => {
    const DATE_NOW = Date.now();
    let emptyConvo: IConversation;
    let namedConvo: IConversation;
    let namedConvoWithHistory: IConversation;
    let convoWithHistory: IConversation;
    let convoWithHistoryAndDraft: IConversation;
    let convoWithDraft: IConversation;

    beforeEach(() => {
      vi.setSystemTime(DATE_NOW);

      /* eslint-disable @typescript-eslint/naming-convention */
      emptyConvo = {
        id: "1234",
        name: undefined,
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        chatHistory: [],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
        extraData: {},
      };

      namedConvo = {
        id: "1234",
        name: "Named Conversation",
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        chatHistory: [],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
        extraData: {},
      };

      namedConvoWithHistory = {
        id: "1234",
        name: "Named Conversation",
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        chatHistory: [
          {
            request_message: "Hello",
            response_text: "World",
            status: ExchangeStatus.success,
          },
        ],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
        extraData: {},
      };

      convoWithHistory = {
        id: "1234",
        name: undefined,
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        chatHistory: [
          {
            request_message: "Hello",
            response_text: "World",
            status: ExchangeStatus.success,
          },
        ],
        feedbackStates: {},
        draftExchange: undefined,
        requestIds: [],
        extraData: {},
      };

      convoWithHistoryAndDraft = {
        id: "1234",
        name: undefined,
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        chatHistory: [
          {
            request_message: "Hello",
            response_text: "World",
            status: ExchangeStatus.success,
          },
        ],
        feedbackStates: {},
        draftExchange: {
          request_message: "Draft",
          status: ExchangeStatus.draft,
        },
        requestIds: [],
        extraData: {},
      };

      convoWithDraft = {
        id: "1234",
        name: undefined,
        createdAtIso: new Date().toISOString(),
        lastInteractedAtIso: new Date().toISOString(),
        chatHistory: [],
        feedbackStates: {},
        draftExchange: {
          request_message: "Draft",
          status: ExchangeStatus.draft,
        },
        requestIds: [],
        extraData: {},
      };
      /* eslint-enable @typescript-eslint/naming-convention */
    });

    test("getDisplayName", () => {
      expect(ConversationModel.getDisplayName(emptyConvo)).toBe(`New Chat`);
      expect(ConversationModel.getDisplayName(namedConvo)).toBe("Named Conversation");
      expect(ConversationModel.getDisplayName(namedConvoWithHistory)).toBe("Named Conversation");
      expect(ConversationModel.getDisplayName(convoWithHistory)).toBe("Hello");
      expect(ConversationModel.getDisplayName(convoWithHistoryAndDraft)).toBe("Hello");
      expect(ConversationModel.getDisplayName(convoWithDraft)).toBe(`New Chat`);
    });

    test("isEmpty", () => {
      expect(ConversationModel.isEmpty(emptyConvo)).toBe(true);
      expect(ConversationModel.isEmpty(namedConvo)).toBe(true);
      expect(ConversationModel.isEmpty(namedConvoWithHistory)).toBe(false);
      expect(ConversationModel.isEmpty(convoWithHistory)).toBe(false);
      expect(ConversationModel.isEmpty(convoWithHistoryAndDraft)).toBe(false);
      expect(ConversationModel.isEmpty(convoWithDraft)).toBe(false);
    });

    test("isNamed", () => {
      expect(ConversationModel.isNamed(emptyConvo)).toBe(false);
      expect(ConversationModel.isNamed(namedConvo)).toBe(true);
      expect(ConversationModel.isNamed(namedConvoWithHistory)).toBe(true);
      expect(ConversationModel.isNamed(convoWithHistory)).toBe(false);
      expect(ConversationModel.isNamed(convoWithHistoryAndDraft)).toBe(false);
      expect(ConversationModel.isNamed(convoWithDraft)).toBe(false);
    });

    test("createdAt", () => {
      expect(ConversationModel.createdAt(emptyConvo).getTime()).toBe(DATE_NOW);
    });

    test("isValid", () => {
      expect(ConversationModel.isValid(emptyConvo)).toBe(false);
      expect(ConversationModel.isValid(namedConvo)).toBe(true);
      expect(ConversationModel.isValid(namedConvoWithHistory)).toBe(true);
      expect(ConversationModel.isValid(convoWithHistory)).toBe(true);
      expect(ConversationModel.isValid(convoWithHistoryAndDraft)).toBe(true);
      expect(ConversationModel.isValid(convoWithDraft)).toBe(true);
    });

    test("create", () => {
      expect(ConversationModel.create()).toEqual({
        id: expect.any(String),
        name: undefined,
        createdAtIso: expect.any(String),
        draftActiveContextIds: undefined,
        lastInteractedAtIso: expect.any(String),
        lastUrl: undefined,
        personaType: PersonaType.DEFAULT,
        chatHistory: [],
        feedbackStates: {},
        draftExchange: undefined,
        selectedModelId: undefined,
        requestIds: [],
        isPinned: false,
        isShareable: false,
        extraData: {},
        toolUseStates: {},
      });
    });
  });

  describe("resend conversation", () => {
    test("should resend conversation", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const exchange: ExchangeWithStatus = {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Hello",
        rich_text_json_repr: [{ type: "paragraph", content: [{ text: "Hello" }] }],
        /* eslint-enable @typescript-eslint/naming-convention */
        status: ExchangeStatus.draft,
      };
      conversation.sendExchange(exchange);
      await tick();

      await testKit.yieldMsg("Init response");
      await testKit.yieldMsg("error");
      let turn = conversation.chatHistory[0] as ExchangeWithStatus;
      expect(turn.status).toBe(ExchangeStatus.failed);

      // Resend conversation
      conversation.resendTurn(conversation.chatHistory[0] as ExchangeWithStatus);
      await tick();

      // Verify that the conversation is resent
      turn = conversation.chatHistory[0] as ExchangeWithStatus;
      expect(turn.status).toBe(ExchangeStatus.sent);
      expect(turn.response_text).toBe("");
      expect(turn.request_message).toBe("Hello");
      expect(turn.rich_text_json_repr).toBe(exchange.rich_text_json_repr);

      await testKit.yieldMsg("Init response");
      turn = conversation.chatHistory[0] as ExchangeWithStatus;
      expect(turn.response_text).toBe("Init response");
      await testKit.yieldMsg(undefined);
      turn = conversation.chatHistory[0] as ExchangeWithStatus;
      expect(turn.status).toBe(ExchangeStatus.success);
    });
  });

  describe("recoverExchange", () => {
    test("should recover response_text and output_nodes while retaining AGENT_MEMORY", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      /* eslint-disable @typescript-eslint/naming-convention */
      const targetResponseText: string = "Assistant response";
      const targetStructuredOutputNodes: ChatResultNode[] = [
        {
          id: 1,
          type: ChatResultNodeType.TOOL_USE,
          content: "",
          tool_use: {
            tool_name: "test-tool",
            tool_use_id: "test-tool-use-id",
            input_json: "{ param: 'value' }",
          },
        },
        {
          id: 2,
          type: ChatResultNodeType.RAW_RESPONSE,
          content: targetResponseText,
        },
        {
          id: 3,
          type: ChatResultNodeType.MAIN_TEXT_FINISHED,
          content: "",
        },
        {
          id: 4,
          type: ChatResultNodeType.SUGGESTED_QUESTIONS,
          content: "Question 1\nQuestion 2",
        },
      ];
      let underlyingStream: Partial<ExchangeWithStatus>[] = [
        {
          response_text: "Assis",
          request_id: "test-request-id",
        },
        {
          response_text: "tant",
          request_id: "test-request-id",
        },
        {
          response_text: " resp",
          request_id: "test-request-id",
        },
        {
          response_text: "onse",
          request_id: "test-request-id",
        },
      ];
      underlyingStream = underlyingStream.concat(
        targetStructuredOutputNodes.map((node) => ({
          request_id: "test-request-id",
          structured_output_nodes: [node],
        })),
      );
      /* eslint-enable @typescript-eslint/naming-convention */

      // Partial exchange to be recovered
      const recoverableExchange: ExchangeWithStatus = {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Hello",
        request_id: "test-request-id",
        response_text: "Assist",
        status: ExchangeStatus.sent,
        structured_output_nodes: [
          {
            id: 1,
            type: ChatResultNodeType.RAW_RESPONSE,
            content: "Raw response content",
          },
          {
            id: 12,
            type: ChatResultNodeType.AGENT_MEMORY,
            content: "Agent memory content",
          },
        ],
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      // Add the exchange to the conversation
      conversation.addChatItem(recoverableExchange);

      // Mock the getChatStream method to return the underlying stream
      const originalGetChatStream = conversation["getChatStream"].bind(conversation);
      conversation["getChatStream"] = async function* () {
        yield* underlyingStream;
      };

      // Recover the exchange
      await conversation.recoverExchange(recoverableExchange);
      conversation["getChatStream"] = originalGetChatStream;

      // Get the updated exchange
      const recovered = conversation.exchangeWithRequestId("test-request-id");

      // Verify that only AGENT_MEMORY nodes are preserved
      expect(recovered?.response_text).toBe(targetResponseText);
      expect(recovered?.structured_output_nodes).toBeDefined();
      expect(recovered?.structured_output_nodes?.length).toBe(
        targetStructuredOutputNodes.length + 1,
      );
      expect(recovered?.structured_output_nodes?.[0].id).toBe(12);
      expect(recovered?.structured_output_nodes?.[0].type).toBe(ChatResultNodeType.AGENT_MEMORY);
      expect(recovered?.structured_output_nodes?.[0].content).toBe("Agent memory content");
      expect(recovered?.structured_output_nodes?.slice(1)).toEqual(targetStructuredOutputNodes);
    });
  });

  describe("utils", () => {
    describe("formatChatModelReplyStream", () => {
      test("does not override request ID on success", async () => {
        async function* stream(): AsyncGenerator<ChatModelReply> {
          yield {
            type: WebViewMessageType.chatModelReply,
            data: { text: "Hello", requestId: "1234", workspaceFileChunks: [] },
          };
          yield {
            type: WebViewMessageType.chatModelReply,
            data: { text: "World", requestId: "1234", workspaceFileChunks: [] },
          };
          yield {
            type: WebViewMessageType.chatModelReply,
            data: { text: "!", requestId: "1234", workspaceFileChunks: [] },
          };
        }

        for await (const chunk of formatChatModelReplyStream(stream())) {
          expect(chunk.request_id).toBe("1234");
        }
      });

      test("does not override request ID on failure", async () => {
        async function* stream(): AsyncGenerator<ChatModelReply> {
          yield {
            type: WebViewMessageType.chatModelReply,
            data: { text: "Hello", requestId: "1234", workspaceFileChunks: [] },
          };
          yield {
            type: WebViewMessageType.chatModelReply,
            data: { text: "World", requestId: "1234", workspaceFileChunks: [] },
          };
          throw new Error("This is an error. Oh no!");
        }
        for await (const chunk of formatChatModelReplyStream(stream())) {
          expect(chunk.request_id).toBe("1234");
        }
      });

      test("error callback gets called back with the right arguments on failure", async () => {
        async function* stream(): AsyncGenerator<ChatModelReply> {
          yield {
            type: WebViewMessageType.chatModelReply,
            data: { text: "Hello", requestId: "1234", workspaceFileChunks: [] },
          };
          yield {
            type: WebViewMessageType.chatModelReply,
            data: { text: "World", requestId: "1234", workspaceFileChunks: [] },
          };
          throw new Error("This is an error. Oh no!");
        }
        const errorCallback = vi.fn();
        for await (const chunk of formatChatModelReplyStream(stream(), errorCallback)) {
          expect(chunk.request_id).toBe("1234");
        }
        expect(errorCallback).toHaveBeenCalledWith({
          originalRequestId: "1234",
          sanitizedMessage: "This is an error. Oh no!",
          stackTrace: expect.any(String),
          diagnostics: [
            {
              key: "error_class",
              value: "Extension-WebView Error",
            },
          ],
        });
      });
    });
  });

  describe("source resolution", () => {
    test("should pass source up to conversation correctly", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const exchange: ExchangeWithStatus = {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Hello",
        rich_text_json_repr: [{ type: "paragraph", content: [{ text: "Hello" }] }],
        /* eslint-enable @typescript-eslint/naming-convention */
        status: ExchangeStatus.draft,
      };
      const sourceChunk: WorkspaceFileChunk = {
        charStart: 0,
        charEnd: 10,
        blobName: "abcd0123",
      };
      conversation.sendExchange(exchange);
      await tick();

      // Get back response with source chunk
      await testKit.yieldMsg("World", [sourceChunk]);
      await testKit.yieldMsg(undefined);
      expect((conversation.chatHistory[0] as ExchangeWithStatus).workspace_file_chunks).toEqual([
        sourceChunk,
      ]);
    });
  });

  test("empty text doesn't reset exchange", async () => {
    const testKit = new ConversationModelTestKit();
    const conversation = testKit.conversation;
    conversation.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Hello",
      /* eslint-enable @typescript-eslint/naming-convention */
      status: ExchangeStatus.draft,
    });
    await tick();

    await testKit.yieldMsg("World");
    await testKit.yieldMsg("");
    await testKit.yieldMsg(undefined);
    expect(conversation.lastExchange?.response_text).toBe("World");
  });

  test("Don't send history when disable history is true for sending regular exchanges", async () => {
    // Ensure that when we call sendExchange with disableHistory, we don't send the chat history to the extension through to the extension.
    const testKit = new ConversationModelTestKit();
    const conversation = testKit.conversation;

    conversation.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Hello",
      chatHistory: [],
      /* eslint-enable @typescript-eslint/naming-convention */
      status: ExchangeStatus.draft,
    });
    await tick();

    // Verify that the received message has an empty chat history
    expect(testKit.fakeHostRcvMsg).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.asyncWrapper,
        baseMsg: expect.objectContaining({
          type: WebViewMessageType.chatUserMessage,
          data: expect.objectContaining({
            chatHistory: [],
          }),
        }),
      }),
    );

    await testKit.yieldMsg("World");
    await testKit.yieldMsg(undefined);
    expect(conversation.chatHistory.length).toBe(1);
    expect(conversation.lastExchange?.response_text).toBe("World");

    // Reset the mock to clear previous calls
    vi.clearAllMocks();

    // Send another exchange to ensure that the disableHistory flag is respected.
    conversation.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Hello again",
      chatHistory: [],
      /* eslint-enable @typescript-eslint/naming-convention */
      status: ExchangeStatus.draft,
    });
    await tick();

    // Verify that the second received message also has an empty chat history
    expect(testKit.fakeHostRcvMsg).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.asyncWrapper,
        baseMsg: expect.objectContaining({
          type: WebViewMessageType.chatUserMessage,
          data: expect.objectContaining({
            chatHistory: [],
          }),
        }),
      }),
    );

    await testKit.yieldMsg("World again");
    await testKit.yieldMsg(undefined);
    expect(conversation.chatHistory.length).toBe(2);
    expect(conversation.lastExchange?.response_text).toBe("World again");

    // Verify that fakeHostRcvMsg was called once after we cleared the mocks
    // We call twice: once for ide state, once for the chat user message.
    expect(testKit.fakeHostRcvMsg).toHaveBeenCalledTimes(2);
  });

  test("Don't send history when disable history is true for sending silent exchange", async () => {
    // Ensure that when we call sendSilentExchange with disableHistory, we don't send the chat history to the extension through to the extension.
    const testKit = new ConversationModelTestKit();
    const conversation = testKit.conversation;

    conversation.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Hello",
      chatHistory: [],
      /* eslint-enable @typescript-eslint/naming-convention */
      status: ExchangeStatus.draft,
    });
    await tick();

    // Verify that the received message has an empty chat history
    expect(testKit.fakeHostRcvMsg).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.asyncWrapper,
        baseMsg: expect.objectContaining({
          type: WebViewMessageType.chatUserMessage,
          data: expect.objectContaining({
            chatHistory: [],
          }),
        }),
      }),
    );

    await testKit.yieldMsg("World");
    await testKit.yieldMsg(undefined);
    expect(conversation.chatHistory.length).toBe(1);
    expect(conversation.lastExchange?.response_text).toBe("World");

    // Reset the mock to clear previous calls
    vi.clearAllMocks();

    // Send another exchange to ensure that the disableHistory flag is respected.
    conversation.sendSilentExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Hello again",
      chatHistory: [],
      /* eslint-enable @typescript-eslint/naming-convention */
    });
    await tick();

    // Verify that the second received message also has an empty chat history
    expect(testKit.fakeHostRcvMsg).toHaveBeenCalledWith(
      expect.objectContaining({
        type: WebViewMessageType.asyncWrapper,
        baseMsg: expect.objectContaining({
          type: WebViewMessageType.chatUserMessage,
          data: expect.objectContaining({
            chatHistory: [],
          }),
        }),
      }),
    );

    await testKit.yieldMsg("World again");
    await testKit.yieldMsg(undefined);
    expect(conversation.chatHistory.length).toBe(1);

    // Verify that fakeHostRcvMsg was called once after we cleared the mocks
    // We call twice: once for ide state, once for the chat user message.
    expect(testKit.fakeHostRcvMsg).toHaveBeenCalledTimes(2);
  });

  test("lastInteractedAtIso is updated when sending exchange", async () => {
    const testKit = new ConversationModelTestKit();
    const conversation = testKit.conversation;

    const initialLastInteractedAtIso = conversation.lastInteractedAtIso;
    expect(initialLastInteractedAtIso).toBeDefined();

    // Update system time
    vi.setSystemTime(new Date(Date.now() + 1000));
    conversation.sendExchange({
      /* eslint-disable @typescript-eslint/naming-convention */
      request_message: "Hello",
      /* eslint-enable @typescript-eslint/naming-convention */
      status: ExchangeStatus.draft,
    });
    await tick();

    const newLastInteractedAtIso = conversation.lastInteractedAtIso;
    expect(newLastInteractedAtIso).toBeDefined();
    expect(newLastInteractedAtIso).not.toBe(initialLastInteractedAtIso);
  });

  test("lastInteractedAtIso is updated when changing conversation", async () => {
    // This test verifies that when we switch conversations, the lastInteractedAtIso is updated
    // We'll create a mock implementation that simulates the behavior we want to test

    // Create a test kit with a mock implementation of the host
    const testKit = new ConversationModelTestKit();

    // Create two conversations with different timestamps
    const conversation1 = ConversationModel.create();
    const conversation2 = ConversationModel.create();

    // Set initial timestamps
    const initialTime = new Date(Date.now());
    conversation1.lastInteractedAtIso = initialTime.toISOString();

    // Set a later time for the second conversation
    const laterTime = new Date(Date.now() + 1000);
    conversation2.lastInteractedAtIso = laterTime.toISOString();

    // Save both conversations to the chat model
    testKit.chat.saveConversation(conversation1);
    testKit.chat.saveConversation(conversation2);

    // Set the current conversation to conversation1
    testKit.conversation.setConversation(conversation1);
    await tick();

    // Record the initial timestamp
    const initialLastInteractedAtIso = testKit.conversation.lastInteractedAtIso;

    // Set a new system time for when we switch conversations
    const newTime = new Date(Date.now() + 2000);
    vi.setSystemTime(newTime);

    // Switch to conversation2
    testKit.conversation.setConversation(conversation2);
    await tick();

    // Switch back to conversation1
    testKit.conversation.setConversation(conversation1);
    await tick();

    // Verify that lastInteractedAtIso is updated to the new time
    const newLastInteractedAtIso = testKit.conversation.lastInteractedAtIso;
    expect(newLastInteractedAtIso).toBeDefined();
    expect(newLastInteractedAtIso).not.toBe(initialLastInteractedAtIso);
    expect(new Date(newLastInteractedAtIso).getTime()).toBeGreaterThan(
      new Date(initialLastInteractedAtIso).getTime(),
    );
  });

  describe("_jsonToStructuredRequest", () => {
    test("should convert simple text content", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Hello world",
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "Hello world",
          },
        },
      ]);
    });

    test("should convert image content with JPEG format", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "image",
                attrs: {
                  src: "test-image.jpg",
                  title: "test-image.jpg",
                },
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.IMAGE_ID,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          image_id_node: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            image_id: "test-image.jpg",
            format: 2 as ImageFormatType,
          },
        },
      ]);
    });

    test("should convert image content with PNG format", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "image",
                attrs: {
                  src: "test.png",
                  title: "test.png",
                },
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.IMAGE_ID,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          image_id_node: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            image_id: "test.png",
            format: 1 as ImageFormatType,
          },
        },
      ]);
    });

    test("should handle unknown image format", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;
      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "image",
                attrs: {
                  src: "test.svg",
                  title: "test.svg",
                },
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.IMAGE_ID,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          image_id_node: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            image_id: "test.svg",
            format: 0 as ImageFormatType,
          },
        },
      ]);
    });

    test("should skip image nodes that are in loading state", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Text before image",
              },
            ],
          },
          {
            type: "image",
            attrs: {
              src: "loading-image.jpg",
              isLoading: true,
            },
          },
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Text after image",
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      // The loading image should be skipped, only text nodes should be included
      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "Text before imageText after image",
          },
        },
      ]);
    });

    test("should process mixed loading and non-loading images correctly", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Start text",
              },
              {
                type: "image",
                attrs: {
                  src: "loading-image1.jpg",
                  title: "ready-image1.jpg",
                  isLoading: true,
                },
              },
              {
                type: "image",
                attrs: {
                  src: "ready-image1.png",
                  title: "ready-image1.png",
                  // No isLoading flag means it's ready
                },
              },
              {
                type: "text",
                text: "Middle text",
              },
              {
                type: "image",
                attrs: {
                  src: "ready-image2.jpg",
                  title: "ready-image2.jpg",
                  isLoading: false, // Explicitly marked as not loading
                },
              },
              {
                type: "image",
                attrs: {
                  src: "loading-image2.png",
                  isLoading: true,
                },
              },
              {
                type: "text",
                text: "End text",
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      // Only non-loading images and text nodes should be included
      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "Start text",
          },
        },
        {
          id: 1,
          type: ChatRequestNodeType.IMAGE_ID,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          image_id_node: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            image_id: "ready-image1.png",
            format: 1 as ImageFormatType,
          },
        },
        {
          id: 2,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "Middle text",
          },
        },
        {
          id: 3,
          type: ChatRequestNodeType.IMAGE_ID,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          image_id_node: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            image_id: "ready-image2.jpg",
            format: 2 as ImageFormatType,
          },
        },
        {
          id: 4,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "End text",
          },
        },
      ]);
    });

    test("line breaks should be preserved correctly", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "First line",
              },
              {
                type: "hardBreak",
              },
              {
                type: "text",
                text: "Second line",
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "First line\nSecond line",
          },
        },
      ]);
    });

    test("consecutive text nodes should be combined correctly", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "First part",
              },
              {
                type: "text",
                text: "Second part",
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "First partSecond part",
          },
        },
      ]);
    });

    test("should handle mentions correctly", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Hello ",
              },
              {
                type: "mention",
                attrs: {
                  data: {
                    // absolute path
                    id: "/foo/bar/file1.ts",
                    // relative path
                    name: "bar/file1.ts",
                    // file name
                    label: "file1.ts",
                  },
                },
              },
              {
                type: "text",
                text: " World",
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "Hello @`bar/file1.ts` World",
          },
        },
      ]);
    });

    test("should handle inserted mention node correctly", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "mention",
                attrs: {
                  id: "file1",
                  label: "file1",
                  data: {
                    id: "file1",
                    name: "file1",
                  },
                },
              },
              {
                type: "text",
                text: " ",
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "@`file1` ",
          },
        },
      ]);
    });

    test("should handle complex content correctly", () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;

      const input: JSONContent = {
        type: "doc",
        content: [
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "Hello ",
              },
              {
                type: "mention",
                attrs: {
                  id: "file1",
                  label: "file1",
                  data: {
                    id: "file1",
                    name: "file1",
                  },
                },
              },
              {
                type: "text",
                text: " World",
              },
              {
                type: "hardBreak",
              },
              {
                type: "text",
                text: "This is a new line",
              },
              {
                type: "hardBreak",
              },
            ],
          },
          {
            type: "image",
            attrs: {
              src: "my-image.jpg",
              title: "my-image.jpg",
            },
          },
          {
            type: "paragraph",
            content: [
              {
                type: "text",
                text: "This is describing the image",
              },
            ],
          },
        ],
      };

      const result = (conversation as any)._jsonToStructuredRequest(input);

      expect(result).toEqual([
        {
          id: 0,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "Hello @`file1` World\nThis is a new line\n",
          },
        },
        {
          id: 1,
          type: ChatRequestNodeType.IMAGE_ID,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          image_id_node: {
            // eslint-disable-next-line @typescript-eslint/naming-convention
            image_id: "my-image.jpg",
            format: 2 as ImageFormatType,
          },
        },
        {
          id: 2,
          type: ChatRequestNodeType.TEXT,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          text_node: {
            content: "This is describing the image",
          },
        },
      ]);
    });
  });

  describe("sendExchange", () => {
    test("should convert simple text content to a node", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;
      conversation.sendExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "hello",
        status: ExchangeStatus.draft,
        /* eslint-enable @typescript-eslint/naming-convention */
      });
      await tick();
      expect(conversation.lastExchange).toStrictEqual(
        expect.objectContaining({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "hello",
          structured_request_nodes: [
            // This new text node is created for the request_message.
            {
              id: 1,
              type: ChatRequestNodeType.TEXT,
              text_node: {
                content: "hello",
              },
            },
            {
              id: 2,
              type: ChatRequestNodeType.IDE_STATE,
              ide_state_node: mockIdeState,
            },
          ],
          /* eslint-enable @typescript-eslint/naming-convention */
        }),
      );
    });

    test("should convert rich text content to a node once", async () => {
      const testKit = new ConversationModelTestKit();
      const conversation = testKit.conversation;
      conversation.sendExchange({
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "hello",
        rich_text_json_repr: [{ type: "paragraph", content: [{ text: "hello" }] }],
        status: ExchangeStatus.draft,
        /* eslint-enable @typescript-eslint/naming-convention */
      });
      await tick();
      expect(conversation.lastExchange).toStrictEqual(
        expect.objectContaining({
          /* eslint-disable @typescript-eslint/naming-convention */
          request_message: "hello",
          structured_request_nodes: [
            // We only create a single new text node for the request_message.
            {
              id: 1,
              type: ChatRequestNodeType.TEXT,
              text_node: {
                content: "hello",
              },
            },
            {
              id: 2,
              type: ChatRequestNodeType.IDE_STATE,
              ide_state_node: mockIdeState,
            },
          ],
          /* eslint-enable @typescript-eslint/naming-convention */
        }),
      );
    });
  });

  describe("IDE state node", () => {
    test("should add IDE state node when sending a message", async () => {
      const testKit = new ConversationModelTestKit();

      const expectNodesContainIdeState = expect.arrayContaining([
        expect.objectContaining({
          type: ChatRequestNodeType.IDE_STATE,
          // eslint-disable-next-line @typescript-eslint/naming-convention
          ide_state_node: mockIdeState,
        }),
      ]);

      // Send a message
      await testKit.sendMsg("Hello");
      expect(testKit.conversation.lastExchange).toStrictEqual(
        expect.objectContaining({
          // eslint-disable-next-line @typescript-eslint/naming-convention
          structured_request_nodes: expectNodesContainIdeState,
        }),
      );
      // Verify that the message was sent with the IDE state node
      expect(testKit.fakeHostRcvMsg).toHaveBeenLastCalledWith(
        expect.objectContaining({
          type: WebViewMessageType.asyncWrapper,
          baseMsg: expect.objectContaining({
            type: WebViewMessageType.chatUserMessage,
            data: expect.objectContaining({
              nodes: expectNodesContainIdeState,
              chatHistory: [],
            }),
          }),
        }),
      );
      // Get back a successful response.
      await testKit.yieldMsg("Hello back");
      await testKit.yieldMsg(undefined);

      await testKit.sendMsg("Goodbye");
      // Verify that we had the IDE state node in history.
      expect(testKit.fakeHostRcvMsg).toHaveBeenLastCalledWith(
        expect.objectContaining({
          type: WebViewMessageType.asyncWrapper,
          baseMsg: expect.objectContaining({
            type: WebViewMessageType.chatUserMessage,
            data: expect.objectContaining({
              nodes: expectNodesContainIdeState,
              chatHistory: [
                expect.objectContaining({
                  // eslint-disable-next-line @typescript-eslint/naming-convention
                  request_nodes: expectNodesContainIdeState,
                }),
              ],
            }),
          }),
        }),
      );
    });
  });
});

// Mock the getAPIClient and getIdeState functions
const mockIdeState: ChatRequestIdeState = {
  /* eslint-disable @typescript-eslint/naming-convention */
  workspace_folders: [
    {
      repository_root: "/test/repo",
      folder_root: "/test/repo/folder",
    },
  ],
  workspace_folders_unchanged: false,
  current_terminal: {
    terminal_id: 0,
    current_working_directory: "/test/terminal",
  },
  /* eslint-enable @typescript-eslint/naming-convention */
};

describe("TotalCharactersStore", () => {
  test("totalCharacters should count characters correctly", () => {
    const testKit = new ConversationModelTestKit();
    const conversation = testKit.conversation;

    // Create a conversation with some exchanges
    const convo: IConversation = {
      id: "test-id",
      name: "Test Conversation",
      createdAtIso: new Date().toISOString(),
      lastInteractedAtIso: new Date().toISOString(),
      chatHistory: [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_id: "1",
          request_message: "Hello",
          response_text: "Hi there",
          status: ExchangeStatus.success,
          structured_request_nodes: [
            {
              id: 1,
              type: ChatRequestNodeType.TEXT,
              text_node: { content: "Hello" },
            },
          ],
          structured_output_nodes: [
            {
              id: 1,
              type: ChatResultNodeType.RAW_RESPONSE,
              content: "Hi there",
            },
          ],
          /* eslint-enable @typescript-eslint/naming-convention */
        },
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_id: "2",
          request_message: "How are you?",
          response_text: "I'm doing well, thank you!",
          status: ExchangeStatus.success,
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ],
      feedbackStates: {},
      toolUseStates: {},
      draftExchange: {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Draft message",
        status: ExchangeStatus.draft,
        rich_text_json_repr: {
          type: "doc",
          content: [{ type: "paragraph", content: [{ type: "text", text: "Draft message" }] }],
        },
        /* eslint-enable @typescript-eslint/naming-convention */
      },
      requestIds: ["1", "2"],
      isPinned: false,
      isShareable: false,
      extraData: {},
      personaType: PersonaType.DEFAULT,
    };

    // Set the conversation
    conversation.setConversation(convo);

    // Test the totalCharactersStore
    // We can't directly test the value since it's a store, but we can verify it's a store with subscribe method
    const totalCharactersStore = conversation.totalCharactersStore;
    expect(typeof totalCharactersStore.subscribe).toBe("function");

    // Force the store to calculate the value by resetting the cache
    conversation.resetTotalCharactersCache();

    // Mock the store to get its value
    let storeValue: number | null = null;
    const unsubscribe = totalCharactersStore.subscribe((value) => {
      storeValue = value;
    });

    // Calculate expected character count based on the implementation
    // The implementation uses JSON.stringify on entire exchange objects
    const processedHistory = convo.chatHistory
      .filter(isChatItemExchangeWithStatus)
      .map(formatHistory);
    // Calculate the total characters from the processed history
    const historyChars = processedHistory.reduce((total, item) => {
      return total + JSON.stringify(item).length;
    }, 0);
    // Add the draft exchange characters
    const draftChars = convo.draftExchange ? JSON.stringify(convo.draftExchange).length : 0;
    const expectedCount = historyChars + draftChars;
    // Verify that the store has a numeric value
    expect(storeValue).toBe(expectedCount);

    unsubscribe();
  });

  test("totalCharactersCache is reset when switching conversations", async () => {
    const testKit = new ConversationModelTestKit();
    const conversation = testKit.conversation;

    // Create two conversations with different content
    const convo1: IConversation = {
      id: "test-id-1",
      name: "Test Conversation 1",
      createdAtIso: new Date().toISOString(),
      lastInteractedAtIso: new Date().toISOString(),
      chatHistory: [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_id: "1",
          request_message: "Hello",
          response_text: "Hi there",
          status: ExchangeStatus.success,
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ],
      feedbackStates: {},
      toolUseStates: {},
      requestIds: ["1"],
      isPinned: false,
      isShareable: false,
      extraData: {},
      personaType: PersonaType.DEFAULT,
    };

    const convo2: IConversation = {
      id: "test-id-2",
      name: "Test Conversation 2",
      createdAtIso: new Date().toISOString(),
      lastInteractedAtIso: new Date().toISOString(),
      chatHistory: [
        {
          /* eslint-disable @typescript-eslint/naming-convention */
          request_id: "2",
          request_message: "Different message",
          response_text: "Different response",
          status: ExchangeStatus.success,
          /* eslint-enable @typescript-eslint/naming-convention */
        },
      ],
      feedbackStates: {},
      toolUseStates: {},
      requestIds: ["2"],
      isPinned: false,
      isShareable: false,
      extraData: {},
      personaType: PersonaType.DEFAULT,
    };
    // add both conversations to chat-model.conversations
    conversation.setConversation(convo1);
    conversation.setConversation(convo2);

    const totalCharactersStore = conversation.totalCharactersStore;

    let currentValue = 0;
    const unsubscribe = totalCharactersStore.subscribe((value) => {
      currentValue = value;
    });

    // Set the first conversation and store
    conversation.setConversation(convo1);
    conversation.resetTotalCharactersCache();
    const firstCount = currentValue;

    // Switch to the second conversation value should change
    await testKit.chat.setCurrentConversation(convo2.id);
    expect(currentValue).not.toBe(firstCount);

    unsubscribe();
  });
  test("should cache character count and respect throttling", async () => {
    const testKit = new ConversationModelTestKit();
    const conversation = testKit.conversation;

    // Get the total characters store
    const totalCharactersStore = conversation.totalCharactersStore;

    // Set up a mock to track store updates
    let currentValue = 0;
    const unsubscribe = totalCharactersStore.subscribe((value) => {
      currentValue = value;
    });

    // Initial state should be 0 characters
    expect(currentValue).toBe(0);

    // Send a message to add content to the conversation
    await testKit.sendMsg("Hello world");
    await testKit.yieldMsg("Response text");
    await testKit.yieldMsg(undefined);
    totalCharactersStore.resetCache();

    // Store the current character count
    const firstCount = currentValue;

    // Test throttling by making rapid changes
    // Create a spy on the resetTotalCharactersCache method to track calls
    const updateSpy = vi.spyOn(totalCharactersStore, "updateStore");

    // Mock Date.now to control time for throttling test
    const originalDateNow = Date.now;
    const mockTime = originalDateNow();
    Date.now = vi.fn(() => mockTime);
    // Add another message, should use cached value
    await testKit.sendMsg("Second message");
    await testKit.yieldMsg("Another response");
    await testKit.yieldMsg(undefined);
    expect(updateSpy).toHaveBeenCalled();
    expect(currentValue).toEqual(firstCount);

    // Advance time beyond the throttle window
    Date.now = vi.fn(() => mockTime + 1200);
    updateSpy.mockClear();
    await testKit.sendMsg("Third message");
    expect(currentValue).toBeGreaterThan(firstCount);

    // Clean up
    unsubscribe();
    updateSpy.mockRestore();
  });

  describe("history summary", () => {
    let testKit: ConversationModelTestKit;
    let conversation: ConversationModel;
    beforeEach(() => {
      testKit = new ConversationModelTestKit();
      conversation = testKit.conversation;
      // Enable history summary via feature flags
      testKit.chat["_chatFlagsModel"].update({
        useHistorySummary: true,
        historySummaryMaxChars: 100_000,
        historySummaryLowerChars: 50_000,
      });
    });

    test("should identify history summary messages correctly", () => {
      const historySummaryMessage: HistorySummaryMessage = {
        /* eslint-disable @typescript-eslint/naming-convention */
        chatItemType: ChatItemType.historySummary,
        summaryVersion: conversation["historySummaryVersion"],
        request_id: "summary-request-id",
        request_message: "Summarize the conversation",
        response_text: "This is a summary of the conversation",
        structured_output_nodes: [
          {
            id: 0,
            type: ChatResultNodeType.RAW_RESPONSE,
            content: "This is a summary of the conversation",
          },
        ],
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      expect(isChatItemHistorySummary(historySummaryMessage)).toBe(true);

      const regularExchange: ExchangeWithStatus = {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_message: "Hello",
        response_text: "World",
        status: ExchangeStatus.success,
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      expect(isChatItemHistorySummary(regularExchange)).toBe(false);
    });

    test("should filter history summary nodes by version in _convertHistoryToExchanges", async () => {
      // Add regular exchanges
      await testKit.echo("Hello");
      await testKit.echo("World");

      // Add history summary with current version
      const currentVersionSummary: HistorySummaryMessage = {
        /* eslint-disable @typescript-eslint/naming-convention */
        chatItemType: ChatItemType.historySummary,
        summaryVersion: conversation["historySummaryVersion"],
        request_id: "current-summary-id",
        request_message: "Current summary",
        response_text: "Current summary text",
        structured_output_nodes: [
          {
            id: 0,
            type: ChatResultNodeType.RAW_RESPONSE,
            content: "Current summary text",
          },
        ],
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      // Add history summary with old version
      const oldVersionSummary: HistorySummaryMessage = {
        /* eslint-disable @typescript-eslint/naming-convention */
        chatItemType: ChatItemType.historySummary,
        summaryVersion: conversation["historySummaryVersion"] - 1, // Old version
        request_id: "old-summary-id",
        request_message: "Old summary",
        response_text: "Old summary text",
        structured_output_nodes: [
          {
            id: 0,
            type: ChatResultNodeType.RAW_RESPONSE,
            content: "Old summary text",
          },
        ],
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      conversation.addChatItem(oldVersionSummary);
      conversation.addChatItem(currentVersionSummary);

      // Convert history to exchanges
      const exchanges = conversation["_convertHistoryToExchanges"](conversation.chatHistory);

      // Should include current version summary but not old version
      const summaryExchanges = exchanges.filter((ex) => ex.request_id?.includes("summary"));
      expect(summaryExchanges.length).toBe(1);
      expect(summaryExchanges[0].request_id).toBe("current-summary-id");
    });

    test("should generate summary when history is long", async () => {
      // Set character limits via feature flags
      testKit.chat["_chatFlagsModel"].update({
        useHistorySummary: true,
        historySummaryLowerChars: 1500,
        historySummaryMaxChars: 2000,
      });
      conversation.sendSilentExchange = vi.fn().mockResolvedValue({
        responseText: "Summary of a, b, and c",
        requestId: "summary-id",
      });
      vi.spyOn(conversation, "maybeAddHistorySummaryNode");

      // Add some exchanges; each exchange adds (# characters * 2 + 5) characters.
      await testKit.echo("a".repeat(500)); // history is 0
      expect(conversation.maybeAddHistorySummaryNode).toHaveResolvedWith(false);
      await testKit.echo("b".repeat(500)); // history is ~1000
      expect(conversation.maybeAddHistorySummaryNode).toHaveResolvedWith(false);
      await testKit.echo("c".repeat(500)); // history is ~2000
      expect(conversation.maybeAddHistorySummaryNode).toHaveResolvedWith(true);
      expect(conversation.chatHistory).toContainEqual(
        expect.objectContaining({
          chatItemType: ChatItemType.historySummary,
        }),
      );
    });

    test("should trim history to last summary node when useHistorySummary is true", async () => {
      // Add some exchanges
      await testKit.echo("First");
      await testKit.echo("Second");

      // Add a history summary
      const historySummary: HistorySummaryMessage = {
        /* eslint-disable @typescript-eslint/naming-convention */
        chatItemType: ChatItemType.historySummary,
        summaryVersion: conversation["historySummaryVersion"],
        request_id: "summary-id",
        request_message: "Summary",
        response_text: "Summary of first two exchanges",
        structured_output_nodes: [
          {
            id: 0,
            type: ChatResultNodeType.RAW_RESPONSE,
            content: "Summary of first two exchanges",
          },
        ],
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      conversation.addChatItem(historySummary);

      // Add more exchanges after the summary
      await testKit.echo("Third");
      await testKit.echo("Fourth");

      // Convert history to exchanges
      const exchanges = conversation["_convertHistoryToExchanges"](conversation.chatHistory);

      // Should only include the summary and exchanges after it
      expect(exchanges.length).toBe(3); // summary + third + fourth
      expect(exchanges[0].request_id).toBe("summary-id");
      expect(exchanges[1].request_message).toBe("Third");
      expect(exchanges[2].request_message).toBe("Fourth");
    });

    test("should clear stale history summary nodes", async () => {
      // Add exchanges
      await testKit.echo("Hello");

      // Add history summaries with different versions
      const oldSummary: HistorySummaryMessage = {
        /* eslint-disable @typescript-eslint/naming-convention */
        chatItemType: ChatItemType.historySummary,
        summaryVersion: conversation["historySummaryVersion"] - 1, // Old version
        request_id: "old-summary-id",
        request_message: "Old summary",
        response_text: "Old summary text",
        structured_output_nodes: [],
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      const currentSummary: HistorySummaryMessage = {
        /* eslint-disable @typescript-eslint/naming-convention */
        chatItemType: ChatItemType.historySummary,
        summaryVersion: conversation["historySummaryVersion"],
        request_id: "current-summary-id",
        request_message: "Current summary",
        response_text: "Current summary text",
        structured_output_nodes: [],
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      conversation.addChatItem(oldSummary);
      conversation.addChatItem(currentSummary);

      expect(conversation.chatHistory.length).toBe(3); // exchange + old summary + current summary

      // Clear stale history summary nodes
      conversation["_clearStaleHistorySummaryNodes"]();

      // Should only have the exchange and current summary
      expect(conversation.chatHistory.length).toBe(2);
      const summaryItems = conversation.chatHistory.filter((item) =>
        isChatItemHistorySummary(item),
      );
      expect(summaryItems.length).toBe(1);
      expect((summaryItems[0] as HistorySummaryMessage).summaryVersion).toBe(
        conversation["historySummaryVersion"],
      );
    });

    test("should handle history summary in formatHistory function", () => {
      const historySummary: HistorySummaryMessage = {
        /* eslint-disable @typescript-eslint/naming-convention */
        chatItemType: ChatItemType.historySummary,
        summaryVersion: conversation["historySummaryVersion"],
        request_id: "summary-id",
        request_message: "Summary request",
        response_text: "Summary response",
        structured_output_nodes: [
          {
            id: 0,
            type: ChatResultNodeType.RAW_RESPONSE,
            content: "Summary response",
          },
          {
            id: 1,
            type: ChatResultNodeType.TOOL_USE,
            content: "",
            tool_use: {
              tool_name: "test-tool",
              tool_use_id: "test-id",
              input_json: "{}",
            },
          },
        ],
        status: ExchangeStatus.success,
        seen_state: SeenState.seen,
        timestamp: new Date().toISOString(),
        /* eslint-enable @typescript-eslint/naming-convention */
      };

      const formattedExchange = formatHistory(historySummary);

      expect(formattedExchange.request_message).toBe("Summary request");
      expect(formattedExchange.response_text).toBe("Summary response");
      expect(formattedExchange.request_id).toBe("summary-id");
      expect(formattedExchange.response_nodes).toEqual(historySummary.structured_output_nodes);
    });
  });
});
