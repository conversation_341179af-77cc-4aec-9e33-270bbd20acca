import { SendMessageTransport } from "@augment-internal/sidecar-libs/transport/grpc/ts/send-message-transport";
import { createClient } from "@connectrpc/connect";

import { type AsyncMsgSender } from "$common-webviews/src/common/hosts/async-messaging";
import { type HostInterface } from "$common-webviews/src/common/hosts/host-types";
import { sha256 } from "$common-webviews/src/utils/sha";
import {
  type AutofixCommand,
  type AutofixFixSuggestion,
  type AutofixIterationStage,
  type AutofixUserSteeringExchange,
  type IConversationAutofixExtraData,
} from "$vscode/src/autofix/autofix-state";
import { type ChatMetricName, WebviewName } from "$vscode/src/metrics/types";
import { type Diagnostic, type SelectedCodeDetails } from "$vscode/src/utils/types";
import {
  type CallToolMessage,
  type CallToolResponse,
  type CancelToolRunMessage,
  type CancelToolRunResponse,
  type ChatAgentFileChangeSummary,
  type ChatAutofixExecuteCommandRequest,
  type ChatAutofixExecuteCommandResponse,
  type ChatAutofixPlanRequestMessage,
  type ChatAutofixPlanResponseMessage,
  type ChatAutofixStateUpdateMessage,
  type ChatClearMetadataData,
  type ChatDeleteImageRequest,
  type ChatDeleteImageResponse,
  type ChatGetAgentOnboardingPromptRequest,
  type ChatGetAgentOnboardingPromptResponse,
  type ChatGetStreamRequest,
  type ChatInitialize,
  type ChatInitializeMessageData,
  type ChatInstructionMessage,
  type ChatInstructionModelReply,
  type ChatLaunchAutofixPanelMessage,
  type ChatLoaded,
  type ChatLoadImageRequest,
  type ChatLoadImageResponse,
  type ChatModeChangedMessage,
  type ChatModelReply,
  type ChatRatingDoneMessage,
  type ChatRatingMessage,
  type ChatSaveImageRequest,
  type ChatSaveImageResponse,
  type ChatSmartPasteData,
  type ChatUserCancel,
  type ChatUserMessage,
  type ChatUserMessageData,
  type CheckAgentAutoModeApproval,
  type CheckAgentAutoModeApprovalResponse,
  type CheckToolExistsRequest,
  type CheckToolExistsResponse,
  type ConfirmationModalResponse,
  type EmptyMessage,
  type ExternalSource,
  type FileDetails,
  type FindExternalSourcesRequest,
  type FindExternalSourcesResponse,
  type FindFileRequest,
  type FindFileResponse,
  type FindFolderRequest,
  type FindFolderResponse,
  type FindRecentlyOpenedFilesRequest,
  type FindRecentlyOpenedFilesResponse,
  type FindSymbolRequest,
  type FindSymbolResponse,
  type FindSymbolResponseData,
  type GenerateCommitMessage,
  type GetChatRequestIdeStateRequest,
  type GetChatRequestIdeStateResponse,
  type GetDiagnosticsRequest,
  type GetDiagnosticsResponse,
  type GetRulesListMessageRequest,
  type GetRulesListMessageResponse,
  type GetSubscriptionInfoRequest,
  type GetSubscriptionInfoResponse,
  type GetToolCallCheckpoint,
  type GetToolCallCheckpointResponse,
  type GetWorkspaceInfoRequest,
  type GetWorkspaceInfoResponse,
  type ISearchScopeArgs,
  type LoadFileMessageData,
  type OpenConfirmationModal,
  type OpenConfirmationModalData,
  type OpenFileMessageData,
  type OpenScratchFileRequestMessage,
  type ReportErrorData,
  type ReportWebviewClientMetricRequest,
  type ResolveFileRequest,
  type ResolveFileResponse,
  type ResolveWorkspaceFileChunkRequest,
  type ResolveWorkspaceFileChunkResponse,
  type SaveChatDoneMessage,
  type SaveChatMessage,
  type SaveFileMessageData,
  type SetAgentAutoModeApproved,
  type ShowAugmentPanel,
  type ToolCheckSafeRequest,
  type ToolCheckSafeResponse,
  WebViewMessageType,
  type WorkspaceFileChunk,
} from "$vscode/src/webview-providers/webview-messages";
import {
  type ChatMode,
  type ChatRequestIdeState,
  type ChatResultNode,
  ChatResultNodeType,
  type Exchange,
  type Rule,
} from "@augment-internal/sidecar-libs/src/chat/chat-types";
import {
  type AgentRequestEventData,
  type AgentSessionEventData,
} from "@augment-internal/sidecar-libs/src/metrics/types";
import type { ToolUseResponse } from "@augment-internal/sidecar-libs/src/tools/tool-types";

import {
  base64ToBytes,
  getBase64FileData,
  readFileAsDataURL,
} from "$common-webviews/src/common/utils/file-base64";
import { TestService } from "@augment-internal/sidecar-libs/protos/test_service_pb";
import type {
  HydratedTask,
  SerializedTask,
  TaskUpdatedBy,
} from "@augment-internal/sidecar-libs/src/agent/task/task-types";
import type {
  CommonWebViewMessageType,
  WebViewMessage as SidecarWebViewMessage,
} from "@augment-internal/sidecar-libs/src/webview-messages/common-webview-messages";
import {
  type AgentMigrateConversationId,
  type AgentSetCurrentConversation,
  AgentWebViewMessageType,
  type ChatGetAgentEditListData,
  type ChatGetAgentEditListRequest,
  type ChatGetAgentEditListResponse,
  type ChatReviewAgentFileMessage,
  type CheckHasEverUsedAgentResponse,
  type CheckHasEverUsedRemoteAgentResponse,
  type GetAgentEditChangesByRequestIdRequest,
  type GetAgentEditChangesByRequestIdResponse,
  type GetAgentEditContentsByRequestIdRequest,
  type GetAgentEditContentsByRequestIdResponse,
  type ReportAgentRequestEvent,
  type ReportAgentSessionEvent,
  type RevertToTimestamp,
  type SetHasEverUsedAgent,
  type SetHasEverUsedRemoteAgent,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/agent-messages";
import {
  type GetToolIdentifierRequest,
  type GetToolIdentifierResponse,
  type GetToolIdentifierResponseData,
  ToolsWebViewMessageType,
} from "@augment-internal/sidecar-libs/src/webview-messages/message-types/tool-messages";
import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
import { ChatStreamWithRetry } from "./chat-stream-with-retry";
import { ExtensionClientTask } from "./extension-client-task";
import { type GetSuggestionsFn, type IChatFlags } from "./models/types";
import { ExchangeStatus, type ExchangeWithStatus, SeenState } from "./types/chat-message";
import { type FeedbackRating } from "./types/feedback-rating";
import {
  AVAILABLE_PERSONALITIES,
  fileDetailsToMentionable,
  type GroupTypes,
  type IChatMentionable,
  ruleToMentionable,
} from "./types/mention-option";

export interface IExtensionClient {
  // Chat-related methods
  getChatInitData(): Promise<ChatInitializeMessageData>;
  startChatStream(
    userMessageData: ChatUserMessageData,
    opts?: { flags: IChatFlags },
  ): AsyncGenerator<Partial<ExchangeWithStatus>>;
  startChatStreamWithRetry(
    requestId: string,
    userMessageData: ChatUserMessageData,
    opts?: { flags: IChatFlags; maxRetries?: number },
  ): AsyncGenerator<Partial<ExchangeWithStatus>>;
  getExistingChatStream(
    exchange: ExchangeWithStatus,
    opts?: { flags: IChatFlags },
  ): AsyncGenerator<Partial<ExchangeWithStatus>>;
  cancelChatStream(requestId: string): Promise<void>;
  sendInstructionMessage(
    exchange: ExchangeWithStatus,
    selectedCodeDetails: SelectedCodeDetails,
  ): AsyncGenerator<Partial<ExchangeWithStatus>>;
  saveChat(
    conversationId: string,
    chatHistory: Exchange[],
    title: string,
  ): Promise<SaveChatDoneMessage>;
  launchAutofixPanel(
    conversationId: string,
    iterationId: string,
    stage: AutofixIterationStage,
  ): Promise<EmptyMessage>;
  sendUserRating(
    requestId: string,
    chatMode: ChatMode,
    rating: FeedbackRating,
    note?: string,
  ): Promise<ChatRatingDoneMessage["data"]>;
  triggerUsedChatMetric(): void;

  // Task-related methods
  getHydratedTask(uuid: string): Promise<HydratedTask | undefined>;
  updateHydratedTask(
    task: HydratedTask,
    updatedBy: TaskUpdatedBy,
  ): Promise<{ created: number; updated: number; deleted: number }>;
  createTask(name: string, description: string, parentTaskUuid?: string): Promise<string>;
  updateTask(
    uuid: string,
    updates: Partial<SerializedTask>,
    updatedBy: TaskUpdatedBy,
  ): Promise<void>;
  setCurrentRootTaskUuid(uuid: string): void;

  // User onboarding
  createProject(projectName: string): void;
  openProjectFolder(): void;
  closeProjectFolder(): void;
  cloneRepository(): void;
  grantSyncPermission(): void;

  // File and folder operations
  resolvePath(
    path: IQualifiedPathName,
    searchScope?: ISearchScopeArgs,
  ): Promise<FileDetails | undefined>;
  findFiles(query: IQualifiedPathName, limit?: number): Promise<FileDetails[]>;
  findFolders(query: IQualifiedPathName, limit?: number): Promise<FileDetails[]>;
  findRecentlyOpenedFiles(path: IQualifiedPathName, limit?: number): Promise<FileDetails[]>;
  openFile(file: OpenFileMessageData): void;
  saveFile(file: SaveFileMessageData): void;
  loadFile(file: LoadFileMessageData): void;
  openMemoriesFile(): void;
  resolveWorkspaceFileChunk(chunk: WorkspaceFileChunk): Promise<FileDetails | undefined>;
  /**
   * Saves an image file to the asset manager.
   *
   * @param file - The image file to save.
   * @param name - The name of the file to save as, if not provided, a hash of the file contents will be used.
   * @returns The filename of the saved image.
   */
  saveImage(file: File, name?: string): Promise<string>;
  /**
   * Load an image from the asset manager.
   *
   * @param filename - The filename of the image to render.
   * @returns A base64-encoded data URL of the rendered image or undefined if not found.
   */
  loadImage(filename: string): Promise<string | undefined>;
  /**
   * Delete an image from the asset manager.
   *
   * @param filename - The filename of the image to delete.
   */
  deleteImage(filename: string): Promise<void>;

  // Symbol and diagnostic operations
  resolveSymbols(query: string, searchScope: ISearchScopeArgs): Promise<FindSymbolResponseData[]>;
  getDiagnostics(): Promise<Diagnostic[]>;

  // Suggestions
  getSuggestions: GetSuggestionsFn<IChatMentionable>;

  // UI and interaction methods
  reportWebviewClientEvent(eventName: ChatMetricName): void;
  reportAgentSessionEvent(eventData: AgentSessionEventData): void;
  reportAgentRequestEvent(eventData: AgentRequestEventData): void;
  sendAction(action: string): void;
  showAugmentPanel(): void;
  openConfirmationModal(data: OpenConfirmationModalData): Promise<boolean>;
  clearMetadataFor(data: ChatClearMetadataData): void;
  openGuidelines(workspaceFolder: string): void;
  updateUserGuidelines(userGuidelines: string): void;
  updateWorkspaceGuidelines(guidelines: string): void;
  updateRuleFile(rulePath: string, content: string): void;
  openSettingsPage(section?: string): void;
  triggerInitialOrientation(): void;

  // Code-related operations
  createFile(code: string, relPath: string | undefined): void;
  smartPaste(smartPasteArgs: ChatSmartPasteData): void;
  openScratchFile(content: string, language?: string): void;

  // External sources
  findExternalSources(query: string, isAgentMode?: boolean): Promise<ExternalSource[]>;

  // Rules
  findRules(query: string, limit?: number): Promise<Rule[]>;

  // Specialized Slash Commands
  generateCommitMessage(): AsyncGenerator<Partial<ExchangeWithStatus>>;

  // Tools
  callTool(
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: any,
    chatHistory: Exchange[],
    conversationId: string,
  ): Promise<ToolUseResponse>;
  checkSafe(toolName: string, toolInput: any): Promise<boolean>;
  cancelToolRun(requestId: string, toolUseId: string): Promise<void>;
  checkToolExists(toolName: string): Promise<boolean>;
  closeAllToolProcesses(): Promise<void>;
  getToolIdentifier(toolName: string): Promise<GetToolIdentifierResponseData>;

  // Chat mode
  setChatMode(mode: ChatMode): void;
  getAgentEditList(fromTimestamp?: number, toTimestamp?: number): Promise<ChatGetAgentEditListData>;
  hasChangesSince(timestamp: number): Promise<boolean>;
  getToolCallCheckpoint(requestId: string): Promise<number | undefined>;
  setCurrentConversation(conversationId: string): void;
  migrateConversationId(oldConversationId: string, newConversationId: string): Promise<void>;
  showAgentReview(
    qualifiedPathName: IQualifiedPathName,
    fromTimestamp?: number,
    toTimestamp?: number,
    retainFocus?: boolean,
  ): void;
  acceptAllAgentEdits(): Promise<boolean>;
  revertToTimestamp(timestamp: number, qualifiedPathNames?: IQualifiedPathName[]): Promise<boolean>;

  // Agent onboarding
  getAgentOnboardingPrompt(): Promise<string>;

  // Workspace info
  getWorkspaceInfo(): Promise<{ trackedFileCount?: number[] }>;

  toggleCollapseUnchangedRegions(): void;

  // Autofix
  executeCommand(
    commandId: string,
    cmd: string,
    args: string[],
  ): Promise<{ output: string; returnCode: number }>;
  sendAutofixStateUpdate(data: IConversationAutofixExtraData): Promise<void>;
  autofixPlan(
    command: AutofixCommand,
    steeringHistory?: AutofixUserSteeringExchange[],
  ): Promise<AutofixFixSuggestion>;
  getAgentEditChangesByRequestId(
    requestId: string,
  ): Promise<ChatAgentFileChangeSummary | undefined>;
  getAgentEditContentsByRequestId(
    requestId: string,
  ): Promise<{ originalCode: string | undefined; modifiedCode: string | undefined } | undefined>;

  // Agent auto mode approval
  checkAgentAutoModeApproval(): Promise<boolean>;
  setAgentAutoModeApproved(approved: boolean): Promise<void>;

  // Agent usage tracking
  checkHasEverUsedAgent(): Promise<boolean>;
  setHasEverUsedAgent(hasUsed: boolean): Promise<void>;

  // Remote agent usage tracking
  checkHasEverUsedRemoteAgent(): Promise<boolean>;
  setHasEverUsedRemoteAgent(hasUsed: boolean): Promise<void>;

  // IDE state
  getChatRequestIdeState(): Promise<ChatRequestIdeState>;

  // Reporting errors
  reportError(data: ReportErrorData): void;

  // Subscription info
  getSubscriptionInfo(): Promise<GetSubscriptionInfoResponse>;
}

export class ExtensionClient implements IExtensionClient {
  /**
   * The task client for task-related operations.
   */
  private readonly _taskClient: ExtensionClientTask;

  /**
   * Constructs a new ExtensionClient.
   *
   * @param _host - The interface for communicating with the extension host.
   * @param _asyncMsgSender - The mechanism for sending asynchronous messages.
   * @param _flags - Configuration flags for controlling client behavior.
   */
  constructor(
    private readonly _host: HostInterface,
    private readonly _asyncMsgSender: AsyncMsgSender,
    private readonly _flags: IChatFlags,
  ) {
    this._taskClient = new ExtensionClientTask(_asyncMsgSender);
  }

  /**
   * Retrieves the initial chat data.
   *
   * @returns A promise that resolves to ChatInitializeMessageData.
   * @throws If the request times out after 30 seconds.
   */
  getChatInitData = async (): Promise<ChatInitializeMessageData> => {
    type TReq = ChatLoaded;
    type TRes = ChatInitialize;
    const chatInitData = await this._asyncMsgSender.send<TReq, TRes>(
      { type: WebViewMessageType.chatLoaded },
      30000 /* 30 second timeout */,
    );
    // If enable debug features, do hello world protobuf test
    if (chatInitData.data.enableDebugFeatures) {
      /* eslint-disable no-console */
      try {
        console.log("Running hello world test...");
        const result = await helloWorld(this._host);
        console.log("Hello world result:", result);
      } catch (err) {
        console.error("Hello world error:", err);
      }
      /* eslint-enable no-console */
    }
    return chatInitData.data;
  };

  /**
   * Reports a client event to the webview.
   *
   * @param eventName - The name of the event to report.
   * @remarks This method does not wait for a response and does not throw exceptions.
   */
  reportWebviewClientEvent = (eventName: ChatMetricName) => {
    type TReq = ReportWebviewClientMetricRequest;
    type TRes = EmptyMessage;
    this._asyncMsgSender.send<TReq, TRes>({
      type: WebViewMessageType.reportWebviewClientMetric,
      data: {
        webviewName: WebviewName.chat,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        client_metric: eventName,
        // Events have singleton value
        value: 1,
      },
    });
  };

  /**
   * Reports an agent session event to the webview.
   *
   * @param eventData - The data for the event to report.
   * @remarks This method does not wait for a response and does not throw exceptions.
   */
  reportAgentSessionEvent = (eventData: AgentSessionEventData) => {
    type TReq = ReportAgentSessionEvent;
    type TRes = SidecarWebViewMessage<CommonWebViewMessageType.empty>;
    this._asyncMsgSender.sendToSidecar<TReq, TRes>({
      type: AgentWebViewMessageType.reportAgentSessionEvent,
      data: eventData,
    });
  };

  /**
   * Reports an agent request event to the webview.
   *
   * @param eventData - The data for the event to report.
   * @remarks This method does not wait for a response and does not throw exceptions.
   */
  reportAgentRequestEvent = (eventData: AgentRequestEventData) => {
    type TReq = ReportAgentRequestEvent;
    type TRes = SidecarWebViewMessage<CommonWebViewMessageType.empty>;
    this._asyncMsgSender.sendToSidecar<TReq, TRes>({
      type: AgentWebViewMessageType.reportAgentRequestEvent,
      data: eventData,
    });
  };

  /**
   * Return a suggestions for the given query.
   * @param query The query string to get suggestions for
   * @param isAgentMode Whether the current conversation is in agent mode
   * @returns
   */
  getSuggestions: GetSuggestionsFn<IChatMentionable> = async (
    query: string,
    isAgentMode: boolean = false,
  ): Promise<IChatMentionable[]> => {
    const pathQuery = { rootPath: "", relPath: query };
    const filePromise = this.findFiles(pathQuery, 6);
    const recentlyOpenedFilesPromise = this.findRecentlyOpenedFiles(pathQuery, 6);
    const folderPromise = this.findFolders(pathQuery, 3);
    const externalSourcesPromise = this.findExternalSources(query, isAgentMode);
    const rulesPromise = this.findRules(query, 6);

    const [files, recentlyOpenedFiles, folders, externalSources, rules] = await Promise.all([
      resultOrDefault(filePromise, []),
      resultOrDefault(recentlyOpenedFilesPromise, []),
      resultOrDefault(folderPromise, []),
      resultOrDefault(externalSourcesPromise, []),
      resultOrDefault(rulesPromise, []),
    ]);

    const fileDetailToMentionable = (f: FileDetails, type: GroupTypes): IChatMentionable => ({
      ...fileDetailsToMentionable(f),
      [type]: f,
    });

    const results = [
      ...files.map((f) => fileDetailToMentionable(f, "file")),
      ...folders.map((f) => fileDetailToMentionable(f, "folder")),
      ...recentlyOpenedFiles.map((f) => fileDetailToMentionable(f, "recentFile")),
      ...externalSources.map((f) => ({
        label: f.name,
        name: f.name,
        id: f.id,
        externalSource: f,
      })),
      ...rules.map((rule) => ({
        ...ruleToMentionable(rule),
        rule,
      })),
    ];

    // Only add personalities if debug features are enabled
    if (this._flags.enablePersonalities) {
      // Add matching personalities to results
      const matchingPersonalities = this.getPersonalities(query);
      if (matchingPersonalities.length > 0) {
        results.push(...matchingPersonalities);
      }
    }

    return results;
  };

  /**
   * Get personalities that match the given query string.
   * Note: This feature is only available when debug features are enabled.
   *
   * @param query - The query string to match against personalities
   * @returns Array of matching personality mentionables
   */
  getPersonalities = (query: string): IChatMentionable[] => {
    // Don't include personalities if debug features are disabled
    if (!this._flags.enablePersonalities) {
      return [];
    }

    // Return all personalities if query is empty
    if (query === "") {
      return AVAILABLE_PERSONALITIES;
    }

    // Filter personalities based on keyword matches in description or label
    const queryLower = query.toLowerCase();
    return AVAILABLE_PERSONALITIES.filter((personality) => {
      const description = personality.personality.description.toLowerCase();
      const label = personality.label.toLowerCase();

      // Match if the query is found in the description or label
      return description.includes(queryLower) || label.includes(queryLower);
    });
  };

  /**
   * Sends an action to the main panel.
   *
   * @param action - The action to send.
   * @remarks This method uses synchronous messaging and does not return a value.
   */
  sendAction = (action: string) => {
    this._host.postMessage({
      type: WebViewMessageType.mainPanelPerformAction,
      data: action,
    });
  };

  /**
   * Requests to show the augment panel.
   *
   * @remarks This method does not wait for a response and does not throw exceptions.
   */
  showAugmentPanel = () => {
    this._asyncMsgSender.send<ShowAugmentPanel, EmptyMessage>({
      type: WebViewMessageType.showAugmentPanel,
    });
  };

  /**
   * Opens a confirmation modal and waits for user response.
   *
   * @param data - The data for the confirmation modal.
   * @returns A promise that resolves to a boolean indicating the user's choice.
   * @remarks This method has no timeout, potentially blocking indefinitely.
   */
  openConfirmationModal = async (data: OpenConfirmationModalData): Promise<boolean> => {
    type TReq = OpenConfirmationModal;
    type TRes = ConfirmationModalResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      { type: WebViewMessageType.openConfirmationModal, data: data },
      1e9 /* No timeout */,
    );
    return response.data.ok;
  };

  /**
   * Clears metadata for the specified data.
   *
   * @param data - The metadata to clear.
   * @remarks This method does not wait for a response and does not throw exceptions.
   */
  clearMetadataFor = (data: ChatClearMetadataData) => {
    this._host.postMessage({
      type: WebViewMessageType.chatClearMetadata,
      data,
    });
  };

  /**
   * Resolves a path to file details.
   *
   * @param path - The path to resolve.
   * @param searchScope - Optional search scope for resolution.
   * @returns A promise that resolves to FileDetails or undefined if not found.
   * @throws If the request times out after 5 seconds.
   */
  resolvePath = async (
    path: IQualifiedPathName,
    searchScope: ISearchScopeArgs | undefined = undefined,
  ): Promise<FileDetails | undefined> => {
    type TReq = ResolveFileRequest;
    type TRes = ResolveFileResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.resolveFileRequest,
        data: { ...path, exactMatch: true, maxResults: 1, searchScope },
      },
      5000 /* 5 second timeout */,
    );
    // response.data can be null.
    if (!response.data) {
      return undefined;
    }
    return response.data;
  };

  /**
   * Resolves symbols based on a query and search scope.
   *
   * @param query - The symbol query string.
   * @param searchScope - The scope to search within.
   * @returns A promise that resolves to an array of FindSymbolResponseData.
   * @throws If the request times out after 30 seconds.
   */
  resolveSymbols = async (
    query: string,
    searchScope: ISearchScopeArgs,
  ): Promise<FindSymbolResponseData[]> => {
    type TReq = FindSymbolRequest;
    type TRes = FindSymbolResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.findSymbolRequest,
        data: { query, searchScope },
      },
      30000 /* 30 second timeout */,
    );
    return response.data;
  };

  /**
   * Retrieves diagnostics.
   *
   * @returns A promise that resolves to an array of Diagnostic objects.
   * @throws If the request times out after 1 second.
   */
  getDiagnostics = async (): Promise<Diagnostic[]> => {
    type TReq = GetDiagnosticsRequest;
    type TRes = GetDiagnosticsResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      { type: WebViewMessageType.getDiagnosticsRequest },
      1000 /* 1 second timeout */,
    );
    return response.data;
  };

  /**
   * Finds files based on a query.
   *
   * @param query - The query to search for files.
   * @param limit - The maximum number of results to return (default: 12).
   * @returns A promise that resolves to an array of FileDetails.
   * @throws If the request times out after 5 seconds.
   */
  findFiles = async (query: IQualifiedPathName, limit: number = 12): Promise<FileDetails[]> => {
    type TReq = FindFileRequest;
    type TRes = FindFileResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.findFileRequest,
        data: { ...query, maxResults: limit },
      },
      5000 /* 5 second timeout */,
    );
    return response.data;
  };

  /**
   * Finds folders based on a query.
   *
   * @param query - The query to search for folders.
   * @param limit - The maximum number of results to return (default: 12).
   * @returns A promise that resolves to an array of FileDetails.
   * @throws If the request times out after 5 seconds.
   */
  findFolders = async (query: IQualifiedPathName, limit: number = 12): Promise<FileDetails[]> => {
    type TReq = FindFolderRequest;
    type TRes = FindFolderResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.findFolderRequest,
        data: { ...query, maxResults: limit },
      },
      5000 /* 5 second timeout */,
    );
    return response.data;
  };

  /** Finds recently opened files. */
  findRecentlyOpenedFiles = async (
    path: IQualifiedPathName,
    limit: number = 12,
  ): Promise<FileDetails[]> => {
    type TReq = FindRecentlyOpenedFilesRequest;
    type TRes = FindRecentlyOpenedFilesResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.findRecentlyOpenedFilesRequest,
        data: { ...path, maxResults: limit },
      },
      5000 /* 5 second timeout */,
    );
    return response.data;
  };

  /**
   * Finds external sources based on a query.
   *
   * @param query - The query string to search for external sources.
   * @param isAgentMode - Whether the current conversation is in agent mode.
   * @returns A promise that resolves to an array of ExternalSource objects.
   * @remarks This method respects the enableExternalSourcesInChat flag.
   * @remarks Documentation is hidden in Agent mode as per requirements.
   * @throws If the request times out after 5 seconds.
   */
  findExternalSources = async (
    query: string,
    isAgentMode: boolean = false,
  ): Promise<ExternalSource[]> => {
    // Don't show external sources if they're disabled
    if (!this._flags.enableExternalSourcesInChat) {
      return [];
    }

    // Don't show external sources if we're in Agent mode
    if (isAgentMode) {
      return [];
    }

    type TReq = FindExternalSourcesRequest;
    type TRes = FindExternalSourcesResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.findExternalSourcesRequest,
        // eslint-disable-next-line @typescript-eslint/naming-convention
        data: { query, source_types: [] },
      },
      5000 /* 5 second timeout */,
    );
    return response.data.sources ?? [];
  };

  /**
   * Finds rules based on a query.
   *
   * @param query - The query string to search for rules.
   * @param limit - The maximum number of results to return (default: 12).
   * @returns A promise that resolves to an array of Rule objects.
   * @remarks This method respects the enableRules flag.
   * @throws If the request times out after 5 seconds.
   */
  findRules = async (query: string, limit: number = 12): Promise<Rule[]> => {
    // Don't show rules if they're disabled
    if (!this._flags.enableRules) {
      return [];
    }

    type TReq = GetRulesListMessageRequest;
    type TRes = GetRulesListMessageResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.getRulesListRequest,
        data: { query, maxResults: limit },
      },
      5000 /* 5 second timeout */,
    );
    return response.data;
  };

  /**
   * Generates a commit message for the given workspace.
   *
   * @returns An async generator that yields commit messages.
   * @remarks This method does not throw exceptions.
   */
  async *generateCommitMessage(): AsyncGenerator<Partial<ExchangeWithStatus>> {
    type ReqT = GenerateCommitMessage;
    type ResT = ChatModelReply;

    const timeoutMs = 30000; // 30 seconds to start stream
    const streamTimeoutMs = 60000; // 1 minute to complete stream
    const request: ReqT = {
      type: WebViewMessageType.generateCommitMessage,
    };
    const dataStream = this._asyncMsgSender.stream<ReqT, ResT>(request, timeoutMs, streamTimeoutMs);
    yield* formatChatModelReplyStream(dataStream);
    return;
  }

  /**
   * Opens a file in the editor.
   *
   * @param file - The file details of the file to open.
   * @remarks This method uses synchronous messaging and does not return a value.
   */
  openFile = (file: OpenFileMessageData) => {
    this._host.postMessage({
      type: WebViewMessageType.openFile,
      data: file,
    });
  };

  /**
   *  Saves a file.
   *
   * @param file - The file details of the file to save.
   */
  saveFile = (file: SaveFileMessageData) =>
    this._host.postMessage({
      type: WebViewMessageType.saveFile,
      data: file,
    });

  loadFile = (file: LoadFileMessageData) =>
    this._host.postMessage({
      type: WebViewMessageType.loadFile,
      data: file,
    });

  /**
   * Opens the memories file in the editor.
   */
  openMemoriesFile = () => {
    this._host.postMessage({
      type: WebViewMessageType.openMemoriesFile,
    });
  };

  /**
   * Creates a new file, opens it in the editor, and inserts a codeblock into the new file
   *
   * @param code - The code to insert.
   * @param relPath - The relative path of the file to create.
   * @remarks This message is not wrapped in an async message.
   */
  createFile = (code: string, relPath: string | undefined) => {
    this._host.postMessage({
      type: WebViewMessageType.chatCreateFile,
      data: { code: code, relPath: relPath },
    });
  };

  /**
   * Opens a scratch file with the provided content
   * Creates a new untitled file with the specified language
   *
   * @param content - The content to insert into the scratch file
   * @param language - The language of the content (default: 'shellscript')
   */
  openScratchFile = async (content: string, language: string = "shellscript") => {
    await this._asyncMsgSender.send<OpenScratchFileRequestMessage, EmptyMessage>(
      {
        type: WebViewMessageType.openScratchFileRequest,
        data: { content, language },
      },
      10000,
    );
  };

  /** Resolves a workspace file chunk. */
  resolveWorkspaceFileChunk = async (
    chunk: WorkspaceFileChunk,
  ): Promise<FileDetails | undefined> => {
    try {
      type TReq = ResolveWorkspaceFileChunkRequest;
      type TRes = ResolveWorkspaceFileChunkResponse;
      const response = await this._asyncMsgSender.send<TReq, TRes>(
        {
          type: WebViewMessageType.resolveWorkspaceFileChunkRequest,
          data: chunk,
        },
        5000,
      );
      return response.data;
    } catch (e) {
      return undefined;
    }
  };

  /**
   * Initiates a smart paste operation.
   *
   * @param smartPasteArgs - The arguments for the smart paste operation.
   * @remarks This method uses synchronous messaging and does not return a value.
   */
  smartPaste = (smartPasteArgs: ChatSmartPasteData) => {
    this._host.postMessage({
      type: WebViewMessageType.chatSmartPaste,
      data: smartPasteArgs,
    });
  };

  /**
   * Gets an hydrated task.
   * @param uuid - The UUID of the task to get
   * @returns A promise that resolves to the hydrated task, or undefined if not found
   */
  getHydratedTask = async (uuid: string): Promise<HydratedTask | undefined> => {
    return this._taskClient.getHydratedTask(uuid);
  };

  /**
   * Updates a hydrated task tree by diffing it against the existing tree.
   * This allows updating an entire task tree at once.
   * @param task - The hydrated task tree to update
   * @param updatedBy - Who is updating the task
   * @returns A promise that resolves to an object containing counts of created, updated, and deleted tasks
   */
  updateHydratedTask = async (
    task: HydratedTask,
    updatedBy: TaskUpdatedBy,
  ): Promise<{ created: number; updated: number; deleted: number }> => {
    return this._taskClient.updateHydratedTask(task, updatedBy);
  };

  /**
   * Sets the current root task UUID.
   * @param uuid - The UUID of the current root task
   */
  setCurrentRootTaskUuid = (uuid: string): void => {
    this._taskClient.setCurrentRootTaskUuid(uuid);
  };

  /**
   * Creates a task.
   * @param name - The name of the task
   * @param description - The description of the task
   * @param parentTaskUuid - The UUID of the parent task, if any
   * @returns A promise that resolves to the UUID of the created task
   */
  createTask = async (
    name: string,
    description: string,
    parentTaskUuid?: string,
  ): Promise<string> => {
    return this._taskClient.createTask(name, description, parentTaskUuid);
  };

  /**
   * Updates a task.
   * @param uuid - The UUID of the task to update
   * @param updates - The updates to apply to the task
   * @param updatedBy - Who is updating the task
   * @returns A promise that resolves when the update is complete
   */
  updateTask = async (
    uuid: string,
    updates: Partial<SerializedTask>,
    updatedBy: TaskUpdatedBy,
  ): Promise<void> => {
    return this._taskClient.updateTask(uuid, updates, updatedBy);
  };

  /**
   * Saves a chat conversation.
   *
   * @param conversationId - The ID of the conversation.
   * @param chatHistory - The chat history to save.
   * @param title - The title of the conversation.
   * @returns A promise that resolves to the saved conversation.
   */
  saveChat = async (
    conversationId: string,
    chatHistory: Exchange[],
    title: string,
  ): Promise<SaveChatDoneMessage> => {
    type TReq = SaveChatMessage;
    type TRes = SaveChatDoneMessage;
    return this._asyncMsgSender.send<TReq, TRes>({
      type: WebViewMessageType.saveChat,
      data: { conversationId, chatHistory, title },
    });
  };

  launchAutofixPanel = async (
    conversationId: string,
    iterationId: string,
    stage: AutofixIterationStage,
  ): Promise<EmptyMessage> => {
    type TReq = ChatLaunchAutofixPanelMessage;
    type TRes = EmptyMessage;
    return this._asyncMsgSender.send<TReq, TRes>({
      type: WebViewMessageType.chatLaunchAutofixPanel,
      data: {
        conversationId,
        iterationId,
        stage,
      },
    });
  };

  /**
   * Sends an instruction message to the model.
   *
   * @param exchange - The exchange to send the instruction message for.
   * @param selectedCodeDetails - The selected code details to send with the instruction message.
   * @returns An AsyncGenerator that yields partial ExchangeWithStatus objects.
   */
  async *sendInstructionMessage(
    exchange: ExchangeWithStatus,
    selectedCodeDetails: SelectedCodeDetails,
  ): AsyncGenerator<Partial<ExchangeWithStatus>> {
    type ReqT = ChatInstructionMessage;
    type ResT = ChatInstructionModelReply;

    const timeoutMs = 30000; // 30 seconds to start stream
    const streamTimeoutMs = 60000; // 1 minute to complete stream
    const data = {
      instruction: exchange.request_message ?? "",
      selectedCodeDetails,
      requestId: exchange.request_id,
    };
    const request: ReqT = {
      type: WebViewMessageType.chatInstructionMessage,
      data,
    };
    const dataStream = this._asyncMsgSender.stream<ReqT, ResT>(request, timeoutMs, streamTimeoutMs);
    yield* formatChatInstructionModelReplyStream(dataStream);
    return;
  }

  async openGuidelines(workspaceFolder: string) {
    this._host.postMessage({
      type: WebViewMessageType.openGuidelines,
      data: workspaceFolder,
    });
  }

  updateUserGuidelines = (userGuidelines: string) => {
    this._host.postMessage({
      type: WebViewMessageType.updateUserGuidelines,
      data: userGuidelines,
    });
  };

  updateWorkspaceGuidelines = (guidelines: string) => {
    this._host.postMessage({
      type: WebViewMessageType.updateWorkspaceGuidelines,
      data: guidelines,
    });
  };

  updateRuleFile = (rulePath: string, content: string) => {
    this._host.postMessage({
      type: WebViewMessageType.updateRuleFile,
      data: { rulePath, content },
    });
  };

  openSettingsPage = (section?: string) => {
    this._host.postMessage({
      type: WebViewMessageType.openSettingsPage,
      data: section,
    });
  };

  /** Gets an existing chat stream. */
  async *getExistingChatStream(
    exchange: ExchangeWithStatus,
    opts?: { flags: IChatFlags },
  ): AsyncGenerator<Partial<ExchangeWithStatus>> {
    if (!exchange.request_id) {
      return;
    }
    type ReqT = ChatGetStreamRequest;
    type ResT = ChatModelReply;

    const longTimeouts = opts?.flags.enablePreferenceCollection;
    const timeoutMs = longTimeouts ? 1e9 : 60000; // 60 seconds to start stream
    const streamTimeoutMs = longTimeouts ? 1e9 : 300000; // 5 minutes to complete stream

    // Send request to extension
    const request: ReqT = {
      type: WebViewMessageType.chatGetStreamRequest,
      data: { requestId: exchange.request_id },
    };
    const stream = this._asyncMsgSender.stream<ReqT, ResT>(request, timeoutMs, streamTimeoutMs);
    yield* formatChatModelReplyStream(stream, this.reportError);
    return;
  }

  /** Starts a new chat stream. */
  async *startChatStream(
    chatMessage: ChatUserMessageData,
    opts?: { flags: IChatFlags },
  ): AsyncGenerator<Partial<ExchangeWithStatus>> {
    type ReqT = ChatUserMessage;
    type ResT = ChatModelReply;

    const longTimeouts = opts?.flags.enablePreferenceCollection;
    const timeoutMs = longTimeouts ? 1e9 : 60000; // 60 seconds to start stream
    const streamTimeoutMs = longTimeouts ? 1e9 : 300000; // 5 minutes to complete stream

    const request: ReqT = {
      type: WebViewMessageType.chatUserMessage,
      data: chatMessage,
    };

    const stream = this._asyncMsgSender.stream<ReqT, ResT>(request, timeoutMs, streamTimeoutMs);
    yield* formatChatModelReplyStream(stream, this.reportError);
    return;
  }

  /**
   * Map of active chat streams with retry capability.
   * Maps request IDs to their corresponding stream handlers.
   */
  private _activeRetryStreams = new Map<string, ChatStreamWithRetry>();

  /** Cancels an ongoing chat stream. */
  cancelChatStream = async (requestId: string): Promise<void> => {
    // Note, it's possible that the request ID is no longer the temporary one
    // used to start the stream. If the given request ID is the temporary ID
    // (e.g.  temp-fe-...), then the stream will be found in the active streams
    // map and we can cancel it directly before it starts the underlying stream.
    // If it's a real request ID, then we won't find the entry in the active
    // streams map. In this case, the chatUserCancel will terminate that stream,
    // which will then conclude the outer retry stream.

    // Cancel the stream if it exists
    this._activeRetryStreams.get(requestId)?.cancel();

    // Send the cancellation message to the extension
    await this._asyncMsgSender.send<ChatUserCancel, ChatModelReply>(
      { type: WebViewMessageType.chatUserCancel, data: { requestId } },
      10000 /* 10 seconds timeout */,
    );
  };

  /** Sends a user rating to the backend. */
  sendUserRating = async (
    requestId: string,
    chatMode: ChatMode,
    rating: FeedbackRating,
    note: string = "",
  ): Promise<ChatRatingDoneMessage["data"]> => {
    type ReqT = ChatRatingMessage;
    type ResT = ChatRatingDoneMessage;

    const data = { requestId, rating, note, mode: chatMode };
    const request: ReqT = { type: WebViewMessageType.chatRating, data };
    const msg = await this._asyncMsgSender.send<ReqT, ResT>(request, 30000);
    return msg.data;
  };

  triggerUsedChatMetric = () => {
    this._host.postMessage({
      type: WebViewMessageType.usedChat,
    });
  };

  createProject = (projectName: string) => {
    this._host.postMessage({
      type: WebViewMessageType.mainPanelCreateProject,
      data: { name: projectName },
    });
  };

  openProjectFolder = () => {
    this._host.postMessage({
      type: WebViewMessageType.mainPanelPerformAction,
      data: "open-folder",
    });
  };

  closeProjectFolder = () => {
    this._host.postMessage({
      type: WebViewMessageType.mainPanelPerformAction,
      data: "close-folder",
    });
  };

  cloneRepository = () => {
    this._host.postMessage({
      type: WebViewMessageType.mainPanelPerformAction,
      data: "clone-repository",
    });
  };

  grantSyncPermission = () => {
    this._host.postMessage({
      type: WebViewMessageType.mainPanelPerformAction,
      data: "grant-sync-permission",
    });
  };

  callTool = async (
    requestId: string,
    toolUseId: string,
    toolName: string,
    toolInput: any,
    chatHistory: Exchange[],
    conversationId: string,
  ): Promise<ToolUseResponse> => {
    type ReqT = CallToolMessage;
    type ResT = CallToolResponse;
    const request: ReqT = {
      type: WebViewMessageType.callTool,
      data: {
        chatRequestId: requestId,
        toolUseId: toolUseId,
        name: toolName,
        input: toolInput,
        chatHistory: chatHistory,
        conversationId: conversationId,
      },
    };
    const msg = await this._asyncMsgSender.send<ReqT, ResT>(request, 0 /* no timeout */);
    return msg.data;
  };

  cancelToolRun = async (requestId: string, toolUseId: string): Promise<void> => {
    type ReqT = CancelToolRunMessage;
    type ResT = CancelToolRunResponse;
    const request: ReqT = {
      type: WebViewMessageType.cancelToolRun,
      data: { requestId, toolUseId },
    };
    await this._asyncMsgSender.send<ReqT, ResT>(request, 0 /* no timeout */);
  };

  checkSafe = async (toolName: string, toolInput: any): Promise<boolean> => {
    type ReqT = ToolCheckSafeRequest;
    type ResT = ToolCheckSafeResponse;
    const request: ReqT = {
      type: WebViewMessageType.toolCheckSafe,
      data: {
        name: toolName,
        input: toolInput,
      },
    };
    const msg = await this._asyncMsgSender.send<ReqT, ResT>(request, 0 /* no timeout */);
    return msg.data.isSafe;
  };

  public async checkToolExists(toolName: string): Promise<boolean> {
    type ReqT = CheckToolExistsRequest;
    type ResT = CheckToolExistsResponse;
    const response = await this._asyncMsgSender.send<ReqT, ResT>(
      {
        type: WebViewMessageType.checkToolExists,
        toolName,
      },
      0 /* no timeout */,
    );
    return response.exists;
  }

  /**
   * Close all running tool processes.
   *
   * This is used when starting a new conversation to ensure no processes
   * from previous conversations are still running.
   */
  closeAllToolProcesses = async (): Promise<void> => {
    type ReqT = SidecarWebViewMessage<ToolsWebViewMessageType.closeAllToolProcesses>;
    type ResT = SidecarWebViewMessage<CommonWebViewMessageType.empty>;
    await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(
      {
        type: ToolsWebViewMessageType.closeAllToolProcesses,
      },
      0 /* no timeout */,
    );
  };

  /**
   * @param toolName The tool name as it appears in the tool definition.
   * @returns The canonical (hostName, toolId) pair for the tool.
   */
  getToolIdentifier = async (toolName: string): Promise<GetToolIdentifierResponseData> => {
    type ReqT = GetToolIdentifierRequest;
    type ResT = GetToolIdentifierResponse;
    const request: ReqT = {
      type: ToolsWebViewMessageType.getToolIdentifierRequest,
      data: { toolName },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, 0 /* no timeout */);
    return msg.data;
  };

  /**
   * Executes a command with the given arguments.
   *
   * @param cmd - The command to execute
   * @param args - Array of command arguments
   * @returns Promise resolving to the command output and return code
   */
  executeCommand = async (
    commandId: string,
    command: string,
    args: string[],
  ): Promise<{ output: string; returnCode: number }> => {
    type TReq = ChatAutofixExecuteCommandRequest;
    type TRes = ChatAutofixExecuteCommandResponse;

    try {
      const result = await this._asyncMsgSender.send<TReq, TRes>(
        {
          type: WebViewMessageType.chatAutofixExecuteCommandRequest,
          data: {
            iterationId: commandId,
            command,
            args,
          },
        },
        600000,
      ); // TODO: What should the timeout be?

      return {
        output: result.data.output,
        returnCode: result.data.returnCode,
      };
    } catch (error) {
      console.error(`[ExtensionClient] Execute command failed:`, error);
      throw error;
    }
  };

  sendAutofixStateUpdate = async (data: IConversationAutofixExtraData): Promise<void> => {
    type TReq = ChatAutofixStateUpdateMessage;
    type TRes = EmptyMessage;
    await this._asyncMsgSender.send<TReq, TRes>({
      type: WebViewMessageType.chatAutofixStateUpdate,
      data,
    });
  };

  autofixPlan = async (
    command: AutofixCommand,
    steeringHistory?: AutofixUserSteeringExchange[],
  ): Promise<AutofixFixSuggestion> => {
    type TReq = ChatAutofixPlanRequestMessage;
    type TRes = ChatAutofixPlanResponseMessage;

    const result = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.chatAutofixPlanRequest,
        data: {
          command,
          steeringHistory,
        },
      },
      60000,
    ); // TODO: What should the timeout be?

    return result.data.plan;
  };

  /**
   * Updates the chat mode in the extension.
   * @param mode - The new chat mode to set
   */
  setChatMode = (mode: ChatMode) => {
    this._asyncMsgSender.send<ChatModeChangedMessage, EmptyMessage>({
      type: WebViewMessageType.chatModeChanged,
      data: { mode },
    });
  };

  getAgentEditList = async (
    fromTimestamp?: number,
    toTimestamp?: number,
  ): Promise<ChatGetAgentEditListData> => {
    type ReqT = ChatGetAgentEditListRequest;
    type ResT = ChatGetAgentEditListResponse;
    const request: ReqT = {
      type: AgentWebViewMessageType.getEditListRequest,
      data: { fromTimestamp, toTimestamp },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, 30000);
    return msg.data;
  };

  hasChangesSince = async (timestamp: number): Promise<boolean> => {
    type ReqT = ChatGetAgentEditListRequest;
    type ResT = ChatGetAgentEditListResponse;
    const request: ReqT = {
      type: AgentWebViewMessageType.getEditListRequest,
      data: { fromTimestamp: timestamp, toTimestamp: Number.MAX_SAFE_INTEGER },
    };
    const response = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, 30000);
    // Get number of changes since timestamp
    const numChanges = response.data.edits.filter(
      (e) => e.changesSummary?.totalAddedLines || e.changesSummary?.totalRemovedLines,
    ).length;
    return numChanges > 0;
  };

  getToolCallCheckpoint = async (requestId: string): Promise<number | undefined> => {
    type ReqT = GetToolCallCheckpoint;
    type ResT = GetToolCallCheckpointResponse;
    const request: ReqT = {
      type: WebViewMessageType.getToolCallCheckpoint,
      data: { requestId },
    };
    const msg = await this._asyncMsgSender.send<ReqT, ResT>(request, 30000);
    return msg.data.checkpointNumber;
  };

  setCurrentConversation = (conversationId: string) => {
    type ReqT = AgentSetCurrentConversation;
    type ResT = SidecarWebViewMessage<CommonWebViewMessageType.empty>;
    this._asyncMsgSender.sendToSidecar<ReqT, ResT>({
      type: AgentWebViewMessageType.setCurrentConversation,
      data: { conversationId },
    });
  };

  migrateConversationId = async (
    oldConversationId: string,
    newConversationId: string,
  ): Promise<void> => {
    type ReqT = AgentMigrateConversationId;
    type ResT = SidecarWebViewMessage<CommonWebViewMessageType.empty>;
    await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(
      {
        type: AgentWebViewMessageType.migrateConversationId,
        data: { oldConversationId, newConversationId },
      },
      30000, // 30 second timeout for migration operations
    );
  };

  showAgentReview = (
    qualifiedPathName: IQualifiedPathName,
    fromTimestamp?: number,
    toTimestamp?: number,
    retainFocus: boolean = true,
  ) => {
    this._asyncMsgSender.sendToSidecar<
      ChatReviewAgentFileMessage,
      SidecarWebViewMessage<CommonWebViewMessageType.empty>
    >({
      type: AgentWebViewMessageType.chatReviewAgentFile,
      data: { qualifiedPathName, fromTimestamp, toTimestamp, retainFocus },
    });
  };

  acceptAllAgentEdits = async () => {
    type TReq = SidecarWebViewMessage<AgentWebViewMessageType.chatAgentEditAcceptAll>;
    type TRes = SidecarWebViewMessage<CommonWebViewMessageType.empty>;
    await this._asyncMsgSender.sendToSidecar<TReq, TRes>({
      type: AgentWebViewMessageType.chatAgentEditAcceptAll,
    });
    return true;
  };

  revertToTimestamp = async (
    timestamp: number,
    qualifiedPathNames?: IQualifiedPathName[],
  ): Promise<boolean> => {
    type TReq = RevertToTimestamp;
    type TRes = SidecarWebViewMessage<CommonWebViewMessageType.empty>;

    await this._asyncMsgSender.sendToSidecar<TReq, TRes>({
      type: AgentWebViewMessageType.revertToTimestamp,
      data: { timestamp, qualifiedPathNames },
    });
    return true;
  };

  getAgentOnboardingPrompt = async (): Promise<string> => {
    type TReq = ChatGetAgentOnboardingPromptRequest;
    type TRes = ChatGetAgentOnboardingPromptResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.chatGetAgentOnboardingPromptRequest,
        data: {},
      },
      30000 /* 30 second timeout */,
    );
    return response.data.prompt;
  };

  async saveImage(file: File, name?: string): Promise<string> {
    const data = getBase64FileData(await readFileAsDataURL(file));
    const filename =
      name ?? `${await sha256(await base64ToBytes(data))}.${file.name.split(".").at(-1)}`;
    type TReq = ChatSaveImageRequest;
    type TRes = ChatSaveImageResponse;
    const msg = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.chatSaveImageRequest,
        data: {
          filename,
          data,
        },
      },
      10000 /* 10 second timeout */,
    );
    return msg.data;
  }

  async loadImage(filename: string) {
    type TReq = ChatLoadImageRequest;
    type TRes = ChatLoadImageResponse;
    const msg = await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.chatLoadImageRequest,
        data: filename,
      },
      10000 /* 10 second timeout */,
    );
    const data = msg.data ? await base64ToBytes(msg.data) : undefined;
    if (!data) {
      return undefined;
    }
    let fileType = "application/octet-stream";
    const fileExtension = filename.split(".").at(-1);
    if (fileExtension === "png") {
      fileType = "image/png";
    } else if (fileExtension === "jpg" || fileExtension === "jpeg") {
      fileType = "image/jpeg";
    }
    const file = new File([data], filename, { type: fileType });
    return await readFileAsDataURL(file);
  }

  async deleteImage(filename: string): Promise<void> {
    type TReq = ChatDeleteImageRequest;
    type TRes = ChatDeleteImageResponse;
    await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.chatDeleteImageRequest,
        data: filename,
      },
      10000 /* 10 second timeout */,
    );
  }

  getAgentEditChangesByRequestId = async (
    requestId: string,
  ): Promise<ChatAgentFileChangeSummary | undefined> => {
    type ReqT = GetAgentEditChangesByRequestIdRequest;
    type ResT = GetAgentEditChangesByRequestIdResponse;
    const request: ReqT = {
      type: AgentWebViewMessageType.getEditChangesByRequestIdRequest,
      data: { requestId },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, 30000);
    return msg.data;
  };

  getAgentEditContentsByRequestId = async (
    requestId: string,
  ): Promise<
    { originalCode: string | undefined; modifiedCode: string | undefined } | undefined
  > => {
    type ReqT = GetAgentEditContentsByRequestIdRequest;
    type ResT = GetAgentEditContentsByRequestIdResponse;
    const request: ReqT = {
      type: AgentWebViewMessageType.getAgentEditContentsByRequestId,
      data: { requestId },
    };
    const msg = await this._asyncMsgSender.sendToSidecar<ReqT, ResT>(request, 30000);
    return msg.data;
  };

  /**
   * Triggers the initial orientation flow.
   *
   * @remarks This method uses synchronous messaging and does not return a value.
   */
  triggerInitialOrientation = () => {
    this._host.postMessage({
      type: WebViewMessageType.triggerInitialOrientation,
    });
  };

  /**
   * Gets information about the workspace, including tracked file counts.
   *
   * @returns A promise that resolves to an object containing workspace information.
   */
  getWorkspaceInfo = async (): Promise<{ trackedFileCount?: number[] }> => {
    type TReq = GetWorkspaceInfoRequest;
    type TRes = GetWorkspaceInfoResponse;
    try {
      const response = await this._asyncMsgSender.send<TReq, TRes>(
        { type: WebViewMessageType.getWorkspaceInfoRequest },
        5000 /* 5 second timeout */,
      );
      return response.data;
    } catch (e) {
      console.error("Error getting workspace info:", e);
      return {};
    }
  };

  /**
   * Toggles the collapse of unchanged regions in the diff editor.
   */
  toggleCollapseUnchangedRegions = () => {
    this._host.postMessage({
      type: WebViewMessageType.toggleCollapseUnchangedRegions,
    });
  };

  /**
   * Checks if agent auto mode has been approved by the user.
   *
   * @returns A promise that resolves to a boolean indicating if auto mode is approved.
   */
  checkAgentAutoModeApproval = async (): Promise<boolean> => {
    type TReq = CheckAgentAutoModeApproval;
    type TRes = CheckAgentAutoModeApprovalResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      { type: WebViewMessageType.checkAgentAutoModeApproval },
      5000 /* 5 second timeout */,
    );
    return response.data;
  };

  /**
   * Sets the agent auto mode approval status.
   *
   * @param approved - Whether auto mode is approved.
   */
  setAgentAutoModeApproved = async (approved: boolean): Promise<void> => {
    type TReq = SetAgentAutoModeApproved;
    type TRes = EmptyMessage;
    await this._asyncMsgSender.send<TReq, TRes>(
      {
        type: WebViewMessageType.setAgentAutoModeApproved,
        data: approved,
      },
      5000 /* 5 second timeout */,
    );
  };

  /**
   * Checks if the user has ever used the agent.
   *
   * @returns A promise that resolves to a boolean indicating whether the user has ever used the agent.
   */
  checkHasEverUsedAgent = async (): Promise<boolean> => {
    type TReq = SidecarWebViewMessage<AgentWebViewMessageType.checkHasEverUsedAgent>;
    type TRes = CheckHasEverUsedAgentResponse;
    const response = await this._asyncMsgSender.sendToSidecar<TReq, TRes>(
      { type: AgentWebViewMessageType.checkHasEverUsedAgent },
      5000 /* 5 second timeout */,
    );
    return response.data;
  };

  /**
   * Sets whether the user has ever used the agent.
   *
   * @param hasUsed - Whether the user has ever used the agent.
   */
  setHasEverUsedAgent = async (hasUsed: boolean): Promise<void> => {
    type TReq = SetHasEverUsedAgent;
    type TRes = SidecarWebViewMessage<CommonWebViewMessageType.empty>;
    await this._asyncMsgSender.sendToSidecar<TReq, TRes>(
      {
        type: AgentWebViewMessageType.setHasEverUsedAgent,
        data: hasUsed,
      },
      5000 /* 5 second timeout */,
    );
  };

  /**
   * Checks if the user has ever used a remote agent.
   *
   * @returns A promise that resolves to a boolean indicating whether the user has ever used a remote agent.
   */
  checkHasEverUsedRemoteAgent = async (): Promise<boolean> => {
    type TReq = SidecarWebViewMessage<AgentWebViewMessageType.checkHasEverUsedRemoteAgent>;
    type TRes = CheckHasEverUsedRemoteAgentResponse;
    const response = await this._asyncMsgSender.sendToSidecar<TReq, TRes>(
      { type: AgentWebViewMessageType.checkHasEverUsedRemoteAgent },
      5000 /* 5 second timeout */,
    );
    return response.data;
  };

  /**
   * Sets whether the user has ever used a remote agent.
   *
   * @param hasUsed - Whether the user has ever used a remote agent.
   */
  setHasEverUsedRemoteAgent = async (hasUsed: boolean): Promise<void> => {
    type TReq = SetHasEverUsedRemoteAgent;
    type TRes = SidecarWebViewMessage<CommonWebViewMessageType.empty>;
    await this._asyncMsgSender.sendToSidecar<TReq, TRes>(
      {
        type: AgentWebViewMessageType.setHasEverUsedRemoteAgent,
        data: hasUsed,
      },
      5000 /* 5 second timeout */,
    );
  };

  getChatRequestIdeState = async (): Promise<ChatRequestIdeState> => {
    type ReqT = GetChatRequestIdeStateRequest;
    type ResT = GetChatRequestIdeStateResponse;
    const request: ReqT = {
      type: WebViewMessageType.getChatRequestIdeStateRequest,
    };
    const msg = await this._asyncMsgSender.send<ReqT, ResT>(request, 30000);
    return msg.data;
  };

  reportError = (data: ReportErrorData): void => {
    this._host.postMessage({
      type: WebViewMessageType.reportError,
      data,
    });
  };

  /** Starts a new chat stream with retry capability. */
  async *startChatStreamWithRetry(
    requestId: string,
    chatMessage: ChatUserMessageData,
    opts?: { flags: IChatFlags; maxRetries?: number },
  ): AsyncGenerator<Partial<ExchangeWithStatus>> {
    const maxRetries = opts?.maxRetries ?? 5;
    const baseDelay = 4000;

    // Create a new ChatStreamWithRetry instance
    const chatStream = new ChatStreamWithRetry(
      requestId,
      chatMessage,
      (msg, options) => this.startChatStream(msg, options),
      maxRetries,
      baseDelay,
      opts?.flags,
    );

    // Store the stream for potential cancellation
    this._activeRetryStreams.set(requestId, chatStream);

    try {
      yield* chatStream.getStream();
    } finally {
      this._activeRetryStreams.delete(requestId);
    }
  }

  async getSubscriptionInfo(): Promise<GetSubscriptionInfoResponse> {
    type TReq = GetSubscriptionInfoRequest;
    type TRes = GetSubscriptionInfoResponse;
    const response = await this._asyncMsgSender.send<TReq, TRes>(
      { type: WebViewMessageType.getSubscriptionInfo },
      5000 /* 5 second timeout */,
    );
    return response;
  }
}

/**
 * Formats the chat instruction model reply stream into a stream of partial ExchangeWithStatus objects.
 *
 * @param stream - An AsyncGenerator that yields ChatInstructionModelReply objects.
 * @returns An AsyncGenerator that yields Partial<ExchangeWithStatus> objects.
 *
 * @remarks
 * This function processes the incoming stream of ChatInstructionModelReply objects and converts them
 * into a stream of partial ExchangeWithStatus objects. It handles both successful responses and errors,
 * updating the status accordingly.
 */
async function* formatChatInstructionModelReplyStream(
  stream: AsyncGenerator<ChatInstructionModelReply>,
): AsyncGenerator<Partial<ExchangeWithStatus>> {
  let requestId: string | undefined;
  try {
    // Process each chunk in the stream
    for await (const chunk of stream) {
      requestId = chunk.data.requestId;
      yield {
        /* eslint-disable @typescript-eslint/naming-convention */
        request_id: requestId,
        response_text: chunk.data.text,
        seen_state: SeenState.unseen,
        status: ExchangeStatus.sent,
        /* eslint-enable @typescript-eslint/naming-convention */
      };
    }
    // After successful processing of all chunks, yield a success status
    /* eslint-disable @typescript-eslint/naming-convention */
    yield {
      request_id: requestId,
      seen_state: SeenState.unseen,
      status: ExchangeStatus.success,
    };
  } catch (e) {
    // If an error occurs during processing, yield a failed status
    yield {
      request_id: requestId,
      seen_state: SeenState.unseen,
      status: ExchangeStatus.failed,
    };
    /* eslint-enable @typescript-eslint/naming-convention */
  }
}

/** Formats chat model replies into updates to the exchange. */
export async function* formatChatModelReplyStream(
  stream: AsyncGenerator<ChatModelReply>,
  errorCallback: (e: ReportErrorData) => void = () => {},
): AsyncGenerator<Partial<ExchangeWithStatus>> {
  let requestId: string | undefined;
  try {
    for await (const chunk of stream) {
      requestId = chunk.data.requestId;
      /* eslint-disable @typescript-eslint/naming-convention */
      if (chunk.data.error) {
        return yield {
          request_id: requestId,
          seen_state: SeenState.unseen,
          status: ExchangeStatus.failed,
          display_error_message: chunk.data.error.displayErrorMessage,
          isRetriable: chunk.data.error.isRetriable,
        };
      }

      const ret: Partial<ExchangeWithStatus> = {
        request_id: requestId,
        response_text: chunk.data.text,
        workspace_file_chunks: chunk.data.workspaceFileChunks,
        structured_output_nodes: processUnstructuredOutputNodes(chunk.data.nodes),
        seen_state: SeenState.unseen,
        status: ExchangeStatus.sent,
      };
      if (chunk.data.stop_reason != null) {
        ret.stop_reason = chunk.data.stop_reason;
      }
      yield ret;
    }
    yield {
      request_id: requestId,
      seen_state: SeenState.unseen,
      status: ExchangeStatus.success,
    };
  } catch (e) {
    // Errors explicitly returned by the stream via data.error
    // are expected to be reported to the backend by the extension. Any exception we catch
    // here, we are responsible for reporting to the backend
    errorCallback({
      originalRequestId: requestId || "",
      sanitizedMessage: e instanceof Error ? e.message : String(e),
      stackTrace: e instanceof Error ? e.stack || "" : "",
      diagnostics: [
        {
          key: "error_class",
          value: "Extension-WebView Error",
        },
      ],
    });
    yield {
      request_id: requestId,
      seen_state: SeenState.unseen,
      status: ExchangeStatus.failed,
    };
    /* eslint-enable @typescript-eslint/naming-convention */
  }
}

// If the promise resolves, we return it, otherwise we return the default value
async function resultOrDefault<T>(promise: Promise<T>, defaultValue: T): Promise<T> {
  try {
    return await promise;
  } catch (e) {
    console.warn(`Error while resolving promise: ${e}`);
    return defaultValue;
  }
}

/**
 * Filters an array of chat result nodes to keep only the first TOOL_USE node and any
 * non-TOOL_USE nodes.
 *
 * The model will normally only return a single TOOL_USE per response, although it is
 * possible to sometimes get multiple TOOL_USEs in a single response. Since the UI
 * is not designed to handle multiple TOOL_USEs, we filter out all but the first one.
 * The AI will still have the opportunity to reissue the next TOOL_USE in a subsequent
 * response.
 *
 * @param nodes - Array of chat result nodes to filter, or undefined
 * @returns The filtered array of nodes with only the first TOOL_USE node preserved,
 *          or undefined if the input is undefined
 *
 * @remarks
 * This function preserves all non-TOOL_USE nodes while ensuring only the first
 * occurrence of a TOOL_USE node is kept in the resulting array. If the input
 * is undefined, it returns undefined without modification.
 */
export function processUnstructuredOutputNodes(
  nodes: ChatResultNode[] | undefined,
): ChatResultNode[] | undefined {
  if (!nodes) {
    return nodes;
  }

  let foundToolUse = false;
  return nodes.filter((node) => {
    if (node.type !== ChatResultNodeType.TOOL_USE) {
      return true;
    }
    if (!foundToolUse) {
      foundToolUse = true;
      return true;
    }
    return false;
  });
}

// TEMPORARY HELLO WORLD FOR DOGFOOD FOR PROTOBUF BOOTSTRAPPING
export async function helloWorld(host: HostInterface): Promise<string> {
  const client = createClient(
    TestService,
    new SendMessageTransport({
      sendMessage: (msg: unknown) => {
        host.postMessage(msg);
      },
      onReceiveMessage: (callback: (message: unknown) => void) => {
        // When in the webview we must unwrap the message event if we attach directly to the window itself
        const cb = (e: MessageEvent) => {
          callback(e.data);
        };
        window.addEventListener("message", cb);
        return () => {
          window.removeEventListener("message", cb);
        };
      },
    }),
  );
  const response = await client.testMethod({ foo: "bar" }, { timeoutMs: 1000 });
  return response.result;
}
