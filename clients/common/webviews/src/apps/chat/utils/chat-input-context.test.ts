/* eslint-disable @typescript-eslint/naming-convention */

import { describe, it, expect, beforeEach, vi } from "vitest";
import { getChatInputContext, getPromptEnhancerOptions } from "./chat-input-context";
import type { ChatModel } from "../models/chat-model";
import type { AgentConversationModel } from "../models/agent-conversation-model";
import type { ToolsWebviewModel } from "../models/tools-webview-model";
import type { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
import { get } from "svelte/store";
import { ImageController } from "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Image/controller";
import { type GitReferenceModel } from "../../remote-agent-manager/models/git-reference-model";
import { RemoteAgentStatus } from "$vscode/src/remote-agent-manager/types";
import { SendMode } from "../types/send-mode";

// Mock the svelte/store get function
vi.mock("svelte/store", () => ({
  get: vi.fn(),
}));

// Mock the ImageController
vi.mock(
  "$common-webviews/src/design-system/components/RichTextEditorAugment/plugins/Image/controller",
  () => ({
    ImageController: {
      hasLoadingImages: vi.fn(),
    },
  }),
);

describe("getChatInputContext", () => {
  let chatModel: ChatModel;
  let agentConversationModel: AgentConversationModel;
  let toolsWebviewModel: ToolsWebviewModel;
  let remoteAgentsModel: RemoteAgentsModel;
  let mockConversationModel: any;
  let mockSlashCommandModel: any;
  let defaultSendMode: SendMode;

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Set default send mode
    defaultSendMode = SendMode.send;

    // Create mock conversation model
    mockConversationModel = {
      draftExchange: { rich_text_json_repr: null, request_message: "mock message" },
      clearDraftExchange: vi.fn(),
      sendDraftExchange: vi.fn(),
      hasDraft: true,
      canSendDraft: true,
      canCancelMessage: false,
      rootTaskUuid: "mock-root-task-uuid",
    };

    // Create mock slash command model
    mockSlashCommandModel = {
      activeCommand: "mockActiveCommand",
      runActiveCommand: vi.fn(),
    };

    // Create mock chat model
    chatModel = {
      currentConversationModel: mockConversationModel,
      flags: {
        enableChatMultimodal: true,
        enableTaskList: true,
      },
      extensionClient: {
        createTask: vi.fn().mockResolvedValue("mock-task-uuid"),
      },
    } as unknown as ChatModel;

    // Create mock agent conversation model
    agentConversationModel = {
      isCurrConversationAgentic: "mockIsCurrConversationAgentic",
      interruptAgent: vi.fn().mockResolvedValue(undefined),
    } as unknown as AgentConversationModel;

    // Create mock tools webview model
    toolsWebviewModel = {
      interruptToolsConversation: vi.fn(),
    } as unknown as ToolsWebviewModel;

    // Create mock remote agents model
    remoteAgentsModel = {
      isCurrentAgentRunning: false,
      sendMessage: vi.fn().mockResolvedValue(true),
      interruptAgent: vi.fn(),
      isActive: false,
      setIsActive: vi.fn().mockImplementation(function (this: any, value: boolean) {
        this.isActive = value;
      }),
      createRemoteAgentFromDraft: vi.fn().mockResolvedValue("mock-agent-id"),
      currentAgentId: undefined,
      setCurrentAgent: vi.fn().mockImplementation(function (this: any, agentId: string) {
        this.currentAgentId = agentId;
      }),
      isCreatingAgent: false,
      setIsCreatingAgent: vi.fn().mockImplementation(function (this: any, value: boolean) {
        this.isCreatingAgent = value;
      }),
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
          },
        },
        selectedBranch: {
          name: "main",
        },
      },
    } as unknown as RemoteAgentsModel;

    // Setup default mock return values
    (get as any).mockImplementation((store: unknown) => {
      if (store === mockSlashCommandModel.activeCommand) {
        return null; // No active slash command by default
      }
      if (store === agentConversationModel.isCurrConversationAgentic) {
        return false; // Not in agent mode by default
      }
      return store;
    });

    // Default for ImageController
    (ImageController.hasLoadingImages as any).mockReturnValue(false);
  });

  it("should return remoteAgentMode context when remoteAgentsModel is provided and isActive is true", () => {
    remoteAgentsModel.setIsActive(true);
    remoteAgentsModel.setCurrentAgent("mock-agent-id");

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
      undefined, // taskStore
      remoteAgentsModel,
    );

    expect(context.stateName).toBe("remoteAgentMode");
    expect(context.showCancelButton).toBe(false);

    // Test action
    context.action();
    expect(remoteAgentsModel.sendMessage).toHaveBeenCalled();

    // Test cancelAction
    context.cancelAction();
    expect(remoteAgentsModel.interruptAgent).toHaveBeenCalled();
  });

  it("should return remoteAgentMode with showCancelButton=true when agent is running", () => {
    remoteAgentsModel.setIsActive(true);

    // Create a new mock with isCurrentAgentRunning set to true
    remoteAgentsModel = {
      ...remoteAgentsModel,
      isCurrentAgentRunning: true,
      sendMessage: vi.fn().mockResolvedValue(true),
      interruptAgent: vi.fn(),
    } as unknown as RemoteAgentsModel;

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
      undefined, // taskStore
      remoteAgentsModel,
    );

    expect(context.stateName).toBe("remoteAgentMode");
    expect(context.showCancelButton).toBe(true);
  });

  it(`should call createRemoteAgentFromDraft when no agent is active,
    and then setCurrentAgent after creating the agent`, async () => {
    remoteAgentsModel.setIsActive(true);
    remoteAgentsModel.setCurrentAgent(undefined);

    const gitRefModel = {
      isGithubAuthenticated: vi.fn().mockResolvedValue(true),
    } as unknown as GitReferenceModel;

    let context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
      undefined, // taskStore
      remoteAgentsModel,
      gitRefModel,
    );

    // Test action
    await context.action();
    expect(remoteAgentsModel.createRemoteAgentFromDraft).toHaveBeenCalled();

    // Update the current agent id
    remoteAgentsModel.setCurrentAgent("mock-agent-id");

    context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
      undefined, // taskStore
      remoteAgentsModel,
    );

    // Test action
    await context.action();
    expect(remoteAgentsModel.setCurrentAgent).toHaveBeenCalled();
  });

  it(`should call createRemoteAgentFromDraft when no gitRefModel is provided`, async () => {
    remoteAgentsModel.setIsActive(true);
    remoteAgentsModel.setCurrentAgent(undefined);

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
      undefined, // taskStore
      remoteAgentsModel,
    );

    // Test action
    await context.action();
    expect(remoteAgentsModel.createRemoteAgentFromDraft).toHaveBeenCalled();
  });

  it("should not call createRemoteAgentFromDraft when not authenticated with GitHub", async () => {
    remoteAgentsModel.setIsActive(true);
    remoteAgentsModel.setCurrentAgent(undefined);

    const gitRefModel = {
      isGithubAuthenticated: vi.fn().mockResolvedValue(false),
    } as unknown as GitReferenceModel;

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
      undefined, // taskStore
      remoteAgentsModel,
      gitRefModel,
    );

    // Test action
    await context.action();
    expect(remoteAgentsModel.createRemoteAgentFromDraft).not.toHaveBeenCalled();
  });

  it("should return imagesLoading context when images are loading", () => {
    (ImageController.hasLoadingImages as any).mockReturnValue(true);
    mockConversationModel.draftExchange = { rich_text_json_repr: { some: "json" } };

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("imagesLoading");
    expect(context.showCancelButton).toBe(false);
    expect(context.isDisabled).toBe(true);

    // Test action (should return false)
    expect(context.action()).toBe(false);
  });

  it("should return slashCommandActive context when a slash command is active", () => {
    (get as any).mockImplementation((store: unknown) => {
      if (store === mockSlashCommandModel.activeCommand) {
        return { name: "someCommand" }; // Active slash command
      }
      return store;
    });

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("slashCommandActive");
    expect(context.showCancelButton).toBe(false);
    expect(context.isDisabled).toBe(false);

    // Test action
    context.action();
    expect(mockSlashCommandModel.runActiveCommand).toHaveBeenCalled();
    expect(mockConversationModel.clearDraftExchange).toHaveBeenCalled();

    // Test cancelAction (should return false)
    expect(context.cancelAction()).toBe(false);
  });

  it("should return agentMode context when in agent mode", () => {
    (get as any).mockImplementation((store: unknown) => {
      if (store === mockSlashCommandModel.activeCommand) {
        return null; // No active slash command
      }
      if (store === agentConversationModel.isCurrConversationAgentic) {
        return true; // In agent mode
      }
      return store;
    });

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("agentMode");
    expect(context.showCancelButton).toBe(false);
    expect(context.isDisabled).toBe(false); // canSendDraft is true by default

    // Test action
    context.action();
    expect(agentConversationModel.interruptAgent).toHaveBeenCalled();

    // Test cancelAction
    context.cancelAction();
    expect(agentConversationModel.interruptAgent).toHaveBeenCalled();
  });

  it("should disable action in agentMode when canSendDraft is false", () => {
    (get as any).mockImplementation((store: unknown) => {
      if (store === mockSlashCommandModel.activeCommand) {
        return null; // No active slash command
      }
      if (store === agentConversationModel.isCurrConversationAgentic) {
        return true; // In agent mode
      }
      return store;
    });

    mockConversationModel.canSendDraft = false;
    mockConversationModel.hasDraft = false;

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("agentMode");
    expect(context.isDisabled).toBe(true);
  });

  it("should return chatMode context by default", () => {
    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("chatMode");
    expect(context.showCancelButton).toBe(false); // canCancelMessage is false by default
    expect(context.isDisabled).toBe(false); // canSendDraft is true by default

    // Test action
    context.action();
    expect(mockConversationModel.sendDraftExchange).toHaveBeenCalled();

    // Test cancelAction
    context.cancelAction();
    expect(toolsWebviewModel.interruptToolsConversation).toHaveBeenCalled();
  });

  it("should show cancel button in chatMode when canCancelMessage is true", () => {
    mockConversationModel.canCancelMessage = true;

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("chatMode");
    expect(context.showCancelButton).toBe(true);
  });

  it("should disable action in chatMode when canSendDraft is false", () => {
    mockConversationModel.canSendDraft = false;

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("chatMode");
    expect(context.isDisabled).toBe(true);
  });

  it("should handle imagesLoading check when multimodal is disabled", () => {
    // Create a new mock with enableChatMultimodal set to false
    chatModel = {
      ...chatModel,
      flags: {
        enableChatMultimodal: false,
      },
    } as unknown as ChatModel;
    mockConversationModel.draftExchange = { rich_text_json_repr: { some: "json" } };

    // Even with JSON content, it should not check for loading images when multimodal is disabled
    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("chatMode"); // Not imagesLoading
    expect(ImageController.hasLoadingImages).not.toHaveBeenCalled();
  });

  it("should handle cancelAction in imagesLoading state for agent mode", () => {
    (ImageController.hasLoadingImages as any).mockReturnValue(true);
    mockConversationModel.draftExchange = { rich_text_json_repr: { some: "json" } };
    (get as any).mockImplementation((store: unknown) => {
      if (store === agentConversationModel.isCurrConversationAgentic) {
        return true; // In agent mode
      }
      return store;
    });

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("imagesLoading");

    // Test cancelAction in agent mode
    context.cancelAction();
    expect(agentConversationModel.interruptAgent).toHaveBeenCalled();
    expect(toolsWebviewModel.interruptToolsConversation).not.toHaveBeenCalled();
  });

  it("should handle cancelAction in imagesLoading state for chat mode", () => {
    (ImageController.hasLoadingImages as any).mockReturnValue(true);
    mockConversationModel.draftExchange = { rich_text_json_repr: { some: "json" } };

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
    );

    expect(context.stateName).toBe("imagesLoading");

    // Test cancelAction in chat mode
    context.cancelAction();
    expect(agentConversationModel.interruptAgent).not.toHaveBeenCalled();
    expect(toolsWebviewModel.interruptToolsConversation).toHaveBeenCalled();
  });

  it("should restore draft when sending message to remote agent throws an exception", async () => {
    // Define the flushPromises helper if not already defined
    const flushPromises = () => new Promise((resolve) => setTimeout(resolve, 0));

    // Setup remote agent model with a send that throws an exception
    remoteAgentsModel = {
      ...remoteAgentsModel,
      isActive: true,
      isCurrentAgentRunning: false,
      currentAgentId: "mock-agent-id",
      sendMessage: vi.fn().mockRejectedValue(new Error("Network error")), // Simulate exception
      interruptAgent: vi.fn(),
    } as unknown as RemoteAgentsModel;

    // Setup conversation model with a draft
    const draftMessage = "This is a test message that should be restored";
    const richTextJson = { some: "json data" };
    mockConversationModel = {
      ...mockConversationModel,
      draftExchange: {
        request_message: draftMessage,
        rich_text_json_repr: richTextJson,
      },
      clearDraftExchange: vi.fn().mockImplementation(function (this: typeof mockConversationModel) {
        // Simulate clearing the draft by setting it to undefined
        this.draftExchange = undefined;
      }),
      saveDraftExchange: vi.fn(),
    };

    // Make sure the chat model has the conversation model
    chatModel = {
      ...chatModel,
      currentConversationModel: mockConversationModel,
    } as unknown as ChatModel;

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
      undefined, // taskStore
      remoteAgentsModel,
    );

    expect(context.stateName).toBe("remoteAgentMode");

    // Execute the action
    await context.action();

    // Verify sendMessage was called with the correct message
    expect(remoteAgentsModel.sendMessage).toHaveBeenCalledWith(draftMessage, undefined);

    // Verify the draft was cleared initially
    expect(mockConversationModel.clearDraftExchange).toHaveBeenCalled();

    // Wait for the promise to reject and async handlers to execute
    await flushPromises();

    // Verify the draft was restored
    expect(mockConversationModel.saveDraftExchange).toHaveBeenCalledWith(
      draftMessage,
      richTextJson,
    );
  });

  it("should disable action in remoteAgentMode when no branch is selected", () => {
    remoteAgentsModel.setIsActive(true);
    remoteAgentsModel.setCurrentAgent(undefined); // No current agent, so we're creating a new one

    // Setup a draft with a message
    mockConversationModel.draftExchange = {
      request_message: "Test message",
      rich_text_json_repr: null,
    };

    // Setup newAgentDraft with no selected branch
    remoteAgentsModel = {
      ...remoteAgentsModel,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
          },
        },
        selectedBranch: {
          name: "", // No branch selected
        },
      },
    } as unknown as RemoteAgentsModel;

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
      undefined, // taskStore
      remoteAgentsModel,
    );

    expect(context.stateName).toBe("remoteAgentMode");
    expect(context.isDisabled).toBe(true);

    // Test that action returns false when disabled
    expect(context.action()).toBe(false);

    // Verify that createRemoteAgentFromDraft is not called
    expect(remoteAgentsModel.createRemoteAgentFromDraft).not.toHaveBeenCalled();
  });

  it("should enable action in remoteAgentMode when branch is selected", () => {
    remoteAgentsModel.setIsActive(true);
    remoteAgentsModel.setCurrentAgent(undefined); // No current agent, so we're creating a new one

    // Setup a draft with a message
    mockConversationModel.draftExchange = {
      request_message: "Test message",
      rich_text_json_repr: null,
    };

    // Setup newAgentDraft with a selected branch
    remoteAgentsModel = {
      ...remoteAgentsModel,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
          },
        },
        selectedBranch: {
          name: "main", // Branch is selected
        },
      },
    } as unknown as RemoteAgentsModel;

    const context = getChatInputContext(
      chatModel,
      agentConversationModel,
      toolsWebviewModel,
      mockSlashCommandModel,
      defaultSendMode,
      undefined, // taskStore
      remoteAgentsModel,
    );

    expect(context.stateName).toBe("remoteAgentMode");
    expect(context.isDisabled).toBe(false);

    // Test that action returns true when enabled
    expect(context.action()).toBe(true);
  });

  describe("agent failed state handling", () => {
    it("should disable send button when agent is in failed state", () => {
      remoteAgentsModel.setIsActive(true);
      remoteAgentsModel.setCurrentAgent("mock-agent-id");

      // Setup a draft with a message
      mockConversationModel.draftExchange = {
        request_message: "Test message",
        rich_text_json_repr: null,
      };

      // Setup current agent with failed status
      remoteAgentsModel = {
        ...remoteAgentsModel,
        currentAgent: {
          remote_agent_id: "mock-agent-id",
          status: RemoteAgentStatus.agentFailed,
          session_summary: "Failed agent",
        },
      } as unknown as RemoteAgentsModel;

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        defaultSendMode,
        undefined, // taskStore
        remoteAgentsModel,
      );

      expect(context.stateName).toBe("remoteAgentMode");
      expect(context.isDisabled).toBe(true);
      expect(context.disabledReason).toBe("Agent has failed and cannot accept messages");
    });

    it("should enable send button when agent is in running state", () => {
      remoteAgentsModel.setIsActive(true);
      remoteAgentsModel.setCurrentAgent("mock-agent-id");

      // Setup a draft with a message
      mockConversationModel.draftExchange = {
        request_message: "Test message",
        rich_text_json_repr: null,
      };

      // Setup current agent with running status
      remoteAgentsModel = {
        ...remoteAgentsModel,
        currentAgent: {
          remote_agent_id: "mock-agent-id",
          status: RemoteAgentStatus.agentRunning,
          session_summary: "Running agent",
        },
      } as unknown as RemoteAgentsModel;

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        defaultSendMode,
        undefined, // taskStore
        remoteAgentsModel,
      );

      expect(context.stateName).toBe("remoteAgentMode");
      expect(context.isDisabled).toBe(false);
    });

    it("should disable send button when agent is in starting state", () => {
      remoteAgentsModel.setIsActive(true);
      remoteAgentsModel.setCurrentAgent("mock-agent-id");

      // Setup a draft with a message
      mockConversationModel.draftExchange = {
        request_message: "Test message",
        rich_text_json_repr: null,
      };

      // Setup current agent with starting status
      remoteAgentsModel = {
        ...remoteAgentsModel,
        currentAgent: {
          remote_agent_id: "mock-agent-id",
          status: RemoteAgentStatus.agentStarting,
          session_summary: "Starting agent",
        },
      } as unknown as RemoteAgentsModel;

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        defaultSendMode,
        undefined, // taskStore
        remoteAgentsModel,
      );

      expect(context.stateName).toBe("remoteAgentMode");
      expect(context.isDisabled).toBe(true);
      expect(context.disabledReason).toBe("Agent is starting...");
    });

    it("should enable send button when agent is in idle state", () => {
      remoteAgentsModel.setIsActive(true);
      remoteAgentsModel.setCurrentAgent("mock-agent-id");

      // Setup a draft with a message
      mockConversationModel.draftExchange = {
        request_message: "Test message",
        rich_text_json_repr: null,
      };

      // Setup current agent with idle status
      remoteAgentsModel = {
        ...remoteAgentsModel,
        currentAgent: {
          remote_agent_id: "mock-agent-id",
          status: RemoteAgentStatus.agentIdle,
          session_summary: "Idle agent",
        },
      } as unknown as RemoteAgentsModel;

      const context = getChatInputContext(
        chatModel,
        agentConversationModel,
        toolsWebviewModel,
        mockSlashCommandModel,
        defaultSendMode,
        undefined, // taskStore
        remoteAgentsModel,
      );

      expect(context.stateName).toBe("remoteAgentMode");
      expect(context.isDisabled).toBe(false);
    });
  });
});

describe("getPromptEnhancerOptions", () => {
  it("should return disabled when input is disabled", () => {
    const mockRemoteAgentsModel = {
      isActive: false,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
          },
        },
      },
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      "https://github.com/test/repo",
      true, // isInputDisabled
    );

    expect(result.isDisabled).toBe(true);
    expect(result.disabledReason).toBeUndefined();
  });

  it("should return enabled when remoteAgentsModel is undefined", () => {
    const result = getPromptEnhancerOptions(
      undefined, // remoteAgentsModel
      "https://github.com/test/repo",
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(false);
    expect(result.disabledReason).toBeUndefined();
  });

  it("should return enabled when remoteAgentsModel is not active", () => {
    const mockRemoteAgentsModel = {
      isActive: false,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
          },
        },
      },
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      "https://github.com/test/repo",
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(false);
    expect(result.disabledReason).toBeUndefined();
  });

  it("should return enabled when no repository is selected in draft", () => {
    const mockRemoteAgentsModel = {
      isActive: true,
      newAgentDraft: undefined,
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      "https://github.com/test/repo",
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(false);
    expect(result.disabledReason).toBeUndefined();
  });

  it("should return enabled when newAgentDraft has no commitRef", () => {
    const mockRemoteAgentsModel = {
      isActive: true,
      newAgentDraft: {
        commitRef: undefined,
      },
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      "https://github.com/test/repo",
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(false);
    expect(result.disabledReason).toBeUndefined();
  });

  it("should return enabled when commitRef has no github_commit_ref", () => {
    const mockRemoteAgentsModel = {
      isActive: true,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: undefined,
        },
      },
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      "https://github.com/test/repo",
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(false);
    expect(result.disabledReason).toBeUndefined();
  });

  it("should return enabled when github_commit_ref has no repository_url", () => {
    const mockRemoteAgentsModel = {
      isActive: true,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: undefined,
          },
        },
      },
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      "https://github.com/test/repo",
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(false);
    expect(result.disabledReason).toBeUndefined();
  });

  it("should return disabled when different repository is selected", () => {
    const mockRemoteAgentsModel = {
      isActive: true,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/different/repo",
          },
        },
      },
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      "https://github.com/test/repo", // different from selected repo
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(true);
    expect(result.disabledReason).toBe(
      "Prompt enhancer is only available when the current repository is selected",
    );
  });

  it("should return enabled when same repository is selected", () => {
    const mockRemoteAgentsModel = {
      isActive: true,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
          },
        },
      },
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      "https://github.com/test/repo", // same as selected repo
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(false);
    expect(result.disabledReason).toBeUndefined();
  });

  it("should return enabled when currentRepositoryUrl is undefined", () => {
    const mockRemoteAgentsModel = {
      isActive: true,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
          },
        },
      },
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      undefined, // currentRepositoryUrl
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(false);
    expect(result.disabledReason).toBeUndefined();
  });

  it("should return enabled when currentRepositoryUrl is empty string", () => {
    const mockRemoteAgentsModel = {
      isActive: true,
      newAgentDraft: {
        commitRef: {
          github_commit_ref: {
            repository_url: "https://github.com/test/repo",
          },
        },
      },
    } as unknown as RemoteAgentsModel;

    const result = getPromptEnhancerOptions(
      mockRemoteAgentsModel,
      "", // empty currentRepositoryUrl
      false, // isInputDisabled
    );

    expect(result.isDisabled).toBe(false);
    expect(result.disabledReason).toBeUndefined();
  });
});
