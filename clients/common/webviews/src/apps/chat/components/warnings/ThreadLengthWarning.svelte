<script lang="ts">
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import MessagePlus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message-plus.svg?component";
  import XMark from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import ChatInputBanner from "./ChatInputBanner.svelte";

  export let onNewThreadClick: () => void;
  export let onDismiss: () => void;
  export let onNeverShowAgain: () => void;

  // State for the two-step dismiss process
  let showConfirmation = false;

  function handleDismissClick() {
    showConfirmation = true;
  }

  function handleNeverShowAgain() {
    onNeverShowAgain();
  }

  function handleCancelConfirmation() {
    onDismiss();
  }
</script>

<ChatInputBanner text="Long threads can lead to worse results.">
  <div class="l-thread-length-warning__buttons" slot="right-content">
    {#if !showConfirmation}
      <!-- Initial state: New Thread and Dismiss buttons -->
      <TextTooltipAugment
        content="New Thread"
        triggerOn={[TooltipTriggerOn.Hover, TooltipTriggerOn.Click]}
      >
        <IconButtonAugment
          size={1}
          variant="ghost-block"
          color="neutral"
          on:click={onNewThreadClick}
          data-testid="new-thread-warning-button"
        >
          <MessagePlus />
        </IconButtonAugment>
      </TextTooltipAugment>
      <TextTooltipAugment content="Dismiss" triggerOn={[TooltipTriggerOn.Hover]}>
        <IconButtonAugment
          size={1}
          variant="ghost-block"
          color="neutral"
          on:click={handleDismissClick}
          data-testid="dismiss-thread-warning-button"
        >
          <XMark />
        </IconButtonAugment>
      </TextTooltipAugment>
    {:else}
      <!-- Confirmation state: Never show again text and buttons -->
      <span class="l-thread-length-warning__confirmation-text">Never show again?</span>
      <div class="l-thread-length-warning__confirmation-buttons">
        <TextTooltipAugment content="Yes, never show again" triggerOn={[TooltipTriggerOn.Hover]}>
          <IconButtonAugment
            size={1}
            variant="ghost-block"
            color="neutral"
            on:click={handleNeverShowAgain}
            data-testid="never-show-again-button"
          >
            <Check />
          </IconButtonAugment>
        </TextTooltipAugment>
        <TextTooltipAugment content="No, dismiss once" triggerOn={[TooltipTriggerOn.Hover]}>
          <IconButtonAugment
            size={1}
            variant="ghost-block"
            color="neutral"
            on:click={handleCancelConfirmation}
            data-testid="cancel-confirmation-button"
          >
            <XMark />
          </IconButtonAugment>
        </TextTooltipAugment>
      </div>
    {/if}
  </div>
</ChatInputBanner>

<style>
  .l-thread-length-warning__buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .l-thread-length-warning__confirmation-text {
    font-size: var(--ds-font-size-1);
    color: var(--ds-color-neutral-11);
    margin-right: var(--ds-spacing-2);
    white-space: nowrap;
  }

  .l-thread-length-warning__confirmation-buttons {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .l-thread-length-warning__buttons :global(svg),
  .l-thread-length-warning__confirmation-buttons :global(svg) {
    width: 14px;
    height: 14px;
    fill: var(--icon-color, currentColor);
  }
</style>
