import { describe, it, expect, vi } from "vitest";
import { render, screen, fireEvent } from "@testing-library/svelte";
import ThreadLengthWarning from "./ThreadLengthWarning.svelte";

describe("ThreadLengthWarning", () => {
  it("renders the warning message", () => {
    const mockOnNewThreadClick = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnNeverShowAgain = vi.fn();
    render(ThreadLengthWarning, {
      onNewThreadClick: mockOnNewThreadClick,
      onDismiss: mockOnDismiss,
      onNeverShowAgain: mockOnNeverShowAgain,
    });

    expect(screen.getByText("Long threads can lead to worse results.")).toBeInTheDocument();
  });

  it("calls onNewThreadClick when the New Thread button is clicked", async () => {
    const mockOnNewThreadClick = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnNeverShowAgain = vi.fn();
    render(ThreadLengthWarning, {
      onNewThreadClick: mockOnNewThreadClick,
      onDismiss: mockOnDismiss,
      onNeverShowAgain: mockOnNeverShowAgain,
    });

    const newThreadButton = screen.getByTestId("new-thread-warning-button");
    await fireEvent.click(newThreadButton);

    expect(mockOnNewThreadClick).toHaveBeenCalledTimes(1);
  });

  it("shows confirmation state when dismiss button is clicked", async () => {
    const mockOnNewThreadClick = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnNeverShowAgain = vi.fn();
    render(ThreadLengthWarning, {
      onNewThreadClick: mockOnNewThreadClick,
      onDismiss: mockOnDismiss,
      onNeverShowAgain: mockOnNeverShowAgain,
    });

    // Initially should show the dismiss button
    expect(screen.getByTestId("dismiss-thread-warning-button")).toBeInTheDocument();
    expect(screen.queryByText("Never show again?")).not.toBeInTheDocument();

    // Click dismiss button
    const dismissButton = screen.getByTestId("dismiss-thread-warning-button");
    await fireEvent.click(dismissButton);

    // Should now show confirmation state
    expect(screen.getByText("Never show again?")).toBeInTheDocument();
    expect(screen.getByTestId("never-show-again-button")).toBeInTheDocument();
    expect(screen.getByTestId("cancel-confirmation-button")).toBeInTheDocument();
    expect(screen.queryByTestId("dismiss-thread-warning-button")).not.toBeInTheDocument();
  });

  it("calls onNeverShowAgain when checkmark button is clicked", async () => {
    const mockOnNewThreadClick = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnNeverShowAgain = vi.fn();
    render(ThreadLengthWarning, {
      onNewThreadClick: mockOnNewThreadClick,
      onDismiss: mockOnDismiss,
      onNeverShowAgain: mockOnNeverShowAgain,
    });

    // Click dismiss to enter confirmation state
    const dismissButton = screen.getByTestId("dismiss-thread-warning-button");
    await fireEvent.click(dismissButton);

    // Click the checkmark button
    const neverShowAgainButton = screen.getByTestId("never-show-again-button");
    await fireEvent.click(neverShowAgainButton);

    expect(mockOnNeverShowAgain).toHaveBeenCalledTimes(1);
  });

  it("calls onDismiss when cancel button is clicked in confirmation state", async () => {
    const mockOnNewThreadClick = vi.fn();
    const mockOnDismiss = vi.fn();
    const mockOnNeverShowAgain = vi.fn();
    render(ThreadLengthWarning, {
      onNewThreadClick: mockOnNewThreadClick,
      onDismiss: mockOnDismiss,
      onNeverShowAgain: mockOnNeverShowAgain,
    });

    // Click dismiss to enter confirmation state
    const dismissButton = screen.getByTestId("dismiss-thread-warning-button");
    await fireEvent.click(dismissButton);

    // Click the cancel button
    const cancelButton = screen.getByTestId("cancel-confirmation-button");
    await fireEvent.click(cancelButton);

    expect(mockOnDismiss).toHaveBeenCalledTimes(1);
  });
});
