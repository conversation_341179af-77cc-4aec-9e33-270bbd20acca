<script context="module" lang="ts">
  import type { TaskState } from "@augment-internal/sidecar-libs/src/agent/task/task-types";

  // Define task filter types for type safety - now only TaskState (removed "all")
  export type TaskFilter = TaskState;

  export interface TaskFilterOption {
    value: TaskFilter;
    label: string;
    count: number;
  }
</script>

<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment/index";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import FilterList from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/filter-list.svg?component";
  import TaskIcon from "../task-status/TaskIcon.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import TaskCheckIcon from "../task-status/TaskCheckIcon.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";

  // Export props
  export let taskFilters: TaskFilterOption[] = [];

  // Create a store for the active filter set - now supports multiple selections
  export let activeTaskFilter: Set<TaskState> = new Set();

  // Function to close the dropdown
  let requestCloseFilterDropdown: () => void;

  // Helper functions for multi-select operations
  function toggleFilter(taskState: TaskState) {
    const newSet = new Set(activeTaskFilter);
    if (newSet.has(taskState)) {
      newSet.delete(taskState);
    } else {
      newSet.add(taskState);
    }
    activeTaskFilter = newSet;
  }

  function selectAll() {
    const allStates = taskFilters.map((filter) => filter.value);
    activeTaskFilter = new Set(allStates);
  }

  function clearAll() {
    activeTaskFilter = new Set();
  }

  // Reactive variable to determine if filtering is active (not showing all tasks)
  // Highlight when: empty set (showing nothing) OR partial selection (not all filters)
  // Don't highlight when: all filters selected (showing everything - normal state)
  $: hasActiveFilters = activeTaskFilter.size === 0 || activeTaskFilter.size < taskFilters.length;
</script>

<DropdownMenuAugment.Root bind:requestClose={requestCloseFilterDropdown}>
  <DropdownMenuAugment.Trigger>
    <TextTooltipAugment content="Filter tasks" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant={hasActiveFilters ? "soft" : "ghost-block"}
        color={hasActiveFilters ? "accent" : "neutral"}
      >
        <FilterList />
      </IconButtonAugment>
    </TextTooltipAugment>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content side="bottom" align="start" size={1}>
    <!-- Convenience controls -->
    <DropdownMenuAugment.Item onSelect={() => selectAll()}>Select All</DropdownMenuAugment.Item>
    <DropdownMenuAugment.Item onSelect={() => clearAll()}>Clear All</DropdownMenuAugment.Item>
    <DropdownMenuAugment.Separator />

    <!-- Individual filter options -->
    {#each taskFilters as filter}
      {@const isActive = activeTaskFilter.has(filter.value)}
      <DropdownMenuAugment.Item
        onSelect={() => {
          toggleFilter(filter.value);
        }}
      >
        <svelte:fragment slot="iconLeft">
          {#if isActive}
            <TaskCheckIcon />
          {:else}
            <TaskIcon taskState={filter.value} size={1} />
          {/if}
        </svelte:fragment>
        <TextAugment size={1} color={isActive ? "neutral" : "secondary"}
          >{filter.label} ({filter.count})</TextAugment
        >
      </DropdownMenuAugment.Item>
    {/each}
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>
