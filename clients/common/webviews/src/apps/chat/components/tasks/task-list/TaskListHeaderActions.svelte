<script lang="ts">
  import { getContext } from "svelte";
  import DotsVertical from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis-vertical.svg?component";
  import FileImport from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-import.svg?component";
  import FileExport from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file-export.svg?component";
  import MessagePlus from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/message-plus.svg?component";

  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import SpinnerAugment from "$common-webviews/src/design-system/components/SpinnerAugment.svelte";

  import {
    CurrentConversationTaskStore,
    type ICurrentConversationTaskStore,
  } from "../../../models/task-store";
  import type { ChatModel } from "../../../models/chat-model";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";

  const taskStore = getContext<ICurrentConversationTaskStore>(CurrentConversationTaskStore.key);
  const { isImportingExporting, rootTask } = taskStore;
  const chatModel = getContext<ChatModel>("chatModel");

  let requestCloseDropdown: (() => void) | undefined = undefined;

  async function exportTasksToMarkdown() {
    if ($isImportingExporting) return;
    await taskStore.exportTasksToMarkdown();
    requestCloseDropdown?.();
  }

  async function importTasksFromMarkdown() {
    if ($isImportingExporting) return;
    await taskStore.importTasksFromMarkdown();
    requestCloseDropdown?.();
  }

  async function continueInNewChat() {
    if (!$rootTask) {
      return;
    }

    const currConversationId = chatModel.currentConversationId;
    const newTaskTree = await taskStore.cloneHydratedTask($rootTask);

    // If the conversation has not changed, let's create a new conversation
    if (currConversationId === chatModel.currentConversationId && newTaskTree) {
      await chatModel.setCurrentConversation(undefined, true, { newTaskUuid: newTaskTree.uuid });
    }
    requestCloseDropdown?.();
  }
</script>

<!-- Task list menu dropdown -->
<DropdownMenuAugment.Root bind:requestClose={requestCloseDropdown}>
  <DropdownMenuAugment.Trigger>
    <TextTooltipAugment content="More actions" triggerOn={[TooltipTriggerOn.Hover]}>
      <IconButtonAugment
        size={1}
        variant="ghost-block"
        color="neutral"
        disabled={$isImportingExporting}
      >
        {#if $isImportingExporting}
          <SpinnerAugment size={1} />
        {:else}
          <DotsVertical />
        {/if}
      </IconButtonAugment>
    </TextTooltipAugment>
  </DropdownMenuAugment.Trigger>
  <DropdownMenuAugment.Content size={1} side="bottom" align="end">
    <!-- Import/Export Section -->
    <DropdownMenuAugment.Label>Import/Export</DropdownMenuAugment.Label>
    <DropdownMenuAugment.Item onSelect={exportTasksToMarkdown} disabled={$isImportingExporting}>
      <FileExport slot="iconLeft" />
      Export to Markdown
    </DropdownMenuAugment.Item>
    <DropdownMenuAugment.Item onSelect={importTasksFromMarkdown} disabled={$isImportingExporting}>
      <FileImport slot="iconLeft" />
      Import from Markdown
    </DropdownMenuAugment.Item>
    <DropdownMenuAugment.Item onSelect={continueInNewChat}>
      <MessagePlus slot="iconLeft" />
      Continue in New Chat
    </DropdownMenuAugment.Item>
  </DropdownMenuAugment.Content>
</DropdownMenuAugment.Root>
