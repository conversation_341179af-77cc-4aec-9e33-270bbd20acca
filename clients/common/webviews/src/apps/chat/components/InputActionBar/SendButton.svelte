<script lang="ts">
  import { getContext } from "svelte";

  import Send from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/paper-plane-top.svg?component";
  import type { ChatModel } from "../../models/chat-model";
  import type { AgentConversationModel } from "../../models/agent-conversation-model";
  import SplitSendButton from "./SplitSendButton.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { SendMode, DEFAULT_SEND_MODE_OPTIONS } from "../../types/send-mode";

  const SEND_BUTTON_TOOLTIP_OFFSET_Y = 10; // Accomodates the top padding of the input action bar

  const chatModel = getContext<ChatModel>("chatModel");
  const agentConversationModel = getContext<AgentConversationModel>("agentConversationModel");
  if (!chatModel) {
    throw new Error("ChatModel not found in context");
  }
  if (!agentConversationModel) {
    throw new Error("AgentConversationModel not found in context");
  }

  export let primaryAction: () => boolean;
  export let isDisabled: boolean = false;
  export let disabledReason: string = "";

  $: conversationModel = $chatModel.currentConversationModel;
  $: flagsModel = $chatModel.flags;

  // Send mode logic
  $: currentSendMode = $chatModel.sendModeModel.mode;
  $: currentModeOption = DEFAULT_SEND_MODE_OPTIONS.find((opt) => opt.id === $currentSendMode);
  // Always show Send icon when not in agent mode or when task lists are disabled
  $: currentModeIcon =
    $isCurrConversationAgentic && $flagsModel.enableTaskList
      ? currentModeOption?.icon || Send
      : Send;

  // Tooltip content for the primary action
  $: primaryActionTooltip = (() => {
    if (isDisabled) {
      return disabledReason;
    }

    if ($isCurrConversationAgentic && $flagsModel.enableTaskList) {
      return currentModeOption?.label || "Send to Agent";
    }

    return "Send message";
  })();

  // Check if current conversation is agentic
  $: isCurrConversationAgentic = agentConversationModel.isCurrConversationAgentic;

  // Only show mode options in agent mode AND when task lists are enabled
  $: availableModeOptions =
    $isCurrConversationAgentic && $flagsModel.enableTaskList ? DEFAULT_SEND_MODE_OPTIONS : [];

  // Reset to send mode when switching to chat mode OR when task lists are disabled
  $: {
    if (
      (!$isCurrConversationAgentic || !$flagsModel.enableTaskList) &&
      $currentSendMode === SendMode.addTask
    ) {
      $chatModel.sendModeModel.setMode(SendMode.send);
      $chatModel.saveImmediate();
    }
  }

  // Mode selection handler
  function handleModeSelect(mode: SendMode) {
    $chatModel.sendModeModel.setMode(mode);
    // Trigger state save to persist the mode change
    $chatModel.saveImmediate();
  }

  let modelIdToDisplayName: { [modelId: string]: string } = {};
  $: {
    modelIdToDisplayName = Object.fromEntries(
      Object.entries($chatModel.flags.modelDisplayNameToId).map(([k, v]) => [v, k]),
    );
  }
</script>

<SplitSendButton
  disabled={{
    primaryDisabled: isDisabled,
    dropdownDisabled: false,
  }}
  onSelectModel={(modelId) => conversationModel.setSelectedModelId(modelId)}
  onSend={primaryAction}
  {modelIdToDisplayName}
  modeOptions={availableModeOptions}
  currentMode={$currentSendMode}
  onSelectMode={handleModeSelect}
>
  <TextTooltipAugment
    content={primaryActionTooltip}
    triggerOn={[TooltipTriggerOn.Hover]}
    side="top"
    delayDurationMs={500}
    offset={[0, SEND_BUTTON_TOOLTIP_OFFSET_Y]}
  >
    <svelte:component this={currentModeIcon} />
  </TextTooltipAugment>
</SplitSendButton>
