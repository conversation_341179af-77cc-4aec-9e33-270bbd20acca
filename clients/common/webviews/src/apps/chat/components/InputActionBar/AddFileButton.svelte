<script lang="ts">
  import Paperclip from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/paperclip.svg?component";
  import CommandContent from "./CommandContent.svelte";
  import { getRichTextEditorContext } from "$common-webviews/src/design-system/components/RichTextEditorAugment/context";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";

  export let accept = "*";
  export let disabled: boolean = false;
  export let onAddFiles = handleAddFiles;

  const fileId = `file-${Date.now()}-${Math.floor(Math.random() * 10_000)}`;
  const editorContext = getRichTextEditorContext();

  function handleAddFiles(files: File[]) {
    editorContext.commandManager?.chain()?.insertImagesIntoEditor?.(files);
  }
</script>

<CommandContent>
  <IconButtonAugment
    size={1}
    variant="ghost-block"
    color="neutral"
    {disabled}
    class="c-input-action-bar__add-file-btn"
  >
    <label for={fileId} class="c-input-action-bar__label">
      <Paperclip />
    </label>
    <input
      type="file"
      hidden
      id={fileId}
      on:click={function () {
        this.value = null;
      }}
      on:input={function (e) {
        onAddFiles([...(e.currentTarget?.files ?? [])]);
      }}
      {accept}
    />
  </IconButtonAugment>
</CommandContent>

<style>
  .c-input-action-bar__label {
    display: contents;
    position: relative;
    cursor: pointer;
  }
</style>
