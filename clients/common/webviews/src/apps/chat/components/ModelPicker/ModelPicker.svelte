<script lang="ts">
  import { getContext } from "svelte";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import CheckIcon from "$common-webviews/src/design-system/icons/check.svelte";
  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import type DropdownMenuRoot from "$common-webviews/src/design-system/components/DropdownMenuAugment/Root.svelte";
  import type { ChatModel } from "../../models/chat-model";
  import {
    DEFAULT_MODEL,
    type ModelRegistry,
  } from "@augment-internal/sidecar-libs/src/chat/model-types";
  import BrainIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/brain-circuit.svg?component";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CommandContent from "../InputActionBar/CommandContent.svelte";

  const MODLE_PICKER_OFFSET_Y = 5;

  const modelRegistry: ModelRegistry = getContext("modelRegistry");
  const chatModel: ChatModel = getContext("chatModel");
  $: conversationModel = $chatModel.currentConversationModel;

  let dropdownRoot: DropdownMenuRoot;

  // Currently selected model (default to Default model)
  $: selectedModel = modelRegistry.getModel(conversationModel?.selectedModelId ?? null);

  // Function to select a model
  function selectModel(modelId: string | null) {
    if (conversationModel) {
      conversationModel.setSelectedModelId(modelId);
    }
    dropdownRoot?.requestClose();
  }
</script>

<div class="c-model-picker">
  <DropdownMenuAugment.Root bind:this={dropdownRoot} nested={false}>
    <DropdownMenuAugment.Trigger>
      <TextTooltipAugment
        content={`Model: ${selectedModel.name}`}
        triggerOn={[TooltipTriggerOn.Hover]}
        offset={[0, MODLE_PICKER_OFFSET_Y]}
      >
        <CommandContent>
          <ButtonAugment size={1} variant="ghost-block" color="neutral">
            <BrainIcon slot="iconLeft" />
            <span class="c-model-picker__text-wrapper">
              <TextAugment size={1}>{selectedModel.name}</TextAugment>
            </span>
          </ButtonAugment>
        </CommandContent>
      </TextTooltipAugment>
    </DropdownMenuAugment.Trigger>
    <DropdownMenuAugment.Content side="top" align="start">
      <DropdownMenuAugment.Label>Internal Testing Only</DropdownMenuAugment.Label>
      <DropdownMenuAugment.Item onSelect={() => selectModel(null)}>
        {DEFAULT_MODEL.name}
      </DropdownMenuAugment.Item>
      <DropdownMenuAugment.Separator />

      {#each modelRegistry.getModels().filter((model) => model.id !== null) as model}
        <DropdownMenuAugment.Item
          highlight={model.id === selectedModel.id}
          onSelect={() => {
            selectModel(model.id);
          }}
        >
          <span class="c-model-picker__item-text">
            {model.name}
          </span>
          {#if model.id === selectedModel.id}
            <span class="c-model-picker__check-icon">
              <CheckIcon />
            </span>
          {/if}
        </DropdownMenuAugment.Item>
      {/each}
    </DropdownMenuAugment.Content>
  </DropdownMenuAugment.Root>
</div>

<style>
  .c-model-picker__item-text {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
  }

  .c-model-picker__check-icon {
    color: color-mix(in srgb, currentColor 70%, transparent);
  }

  .c-model-picker {
    display: inline-flex;
    /* Allow the component to shrink when space is limited */
    min-width: 0;
    flex-shrink: 1;
    /* Prevent text wrapping and ensure proper alignment */
    white-space: nowrap;
    align-items: center;
  }
</style>
