<script lang="ts">
  import BaseToolComponent from "../BaseToolComponent.svelte";
  import Warning from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import ToolUseHeader from "../ToolUseHeader.svelte";
</script>

<BaseToolComponent>
  <ToolUseHeader slot="header" toolName="Diagnostics">
    <span slot="icon" class="c-diagnostics__header__icon"> <Warning /></span>
  </ToolUseHeader>
</BaseToolComponent>

<style>
  .c-diagnostics__header__icon {
    width: var(--ds-icon-size-2);
    height: var(--ds-icon-size-2);
    /*  Hack to fix icon positioning */
    position: relative;
    top: 1px;
  }
</style>
