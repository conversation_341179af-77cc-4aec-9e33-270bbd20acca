<script lang="ts">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import MonacoProvider from "$common-webviews/src/design-system/components/MonacoProvider";
  import SimpleMonaco from "$common-webviews/src/design-system/components/MonacoProvider/SimpleMonaco.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ClipboardCopy from "$common-webviews/src/design-system/icons/clipboard-copy.svelte";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import { type editor } from "monaco-editor";

  export let label: string | undefined = undefined;
  export let content: string;
  export let language: string;
  export let doScrollToBottom: boolean = false;
  export let onViewInFile: (event: Event) => void;
  export let monacoOptions: Record<string, any> = {
    lineNumbers: "off",
    wordWrap: "off",
    lineDecorationsWidth: 0,
    folding: false,
    showFoldingControls: "never",
    // left padding
    readOnly: true, // Default to readOnly
    stickyScroll: { enabled: false },
    overviewRulerLanes: 0,
    // show scrollbars when focused
    scrollbar: {
      vertical: "auto",
      horizontal: "auto",
      alwaysConsumeMouseWheel: false,
      verticalScrollbarSize: 5,
    },
  };
  export let showCopyButton: boolean = true;
  export let showViewInFileButton: boolean = true;

  let copyTooltipTimer: ReturnType<typeof setTimeout> | undefined = undefined;
  let copyMessage: string | undefined = undefined;
  let requestClose: () => void = () => {};
  let isFocused = false;

  function onCopyOpenChange(open: boolean) {
    if (!open) {
      if (copyTooltipTimer) clearTimeout(copyTooltipTimer);
      copyTooltipTimer = undefined;
      copyMessage = undefined;
    }
  }

  async function handleCopy() {
    if (!content) return;
    if (copyTooltipTimer) clearTimeout(copyTooltipTimer);
    try {
      await navigator.clipboard.writeText(content);
      copyMessage = "Copied!";
      copyTooltipTimer = setTimeout(requestClose, 1500);
    } catch (err) {
      console.error("Failed to copy:", err);
      copyMessage = "Copy failed";
      copyTooltipTimer = setTimeout(requestClose, 1500);
    }
  }

  let editorInstance: editor.IStandaloneCodeEditor | undefined;
  const scrollToBottom = () => {
    if (doScrollToBottom && editorInstance) {
      editorInstance?.layout();
      editorInstance.revealLine(editorInstance.getModel()!.getLineCount());
    }
  };
  $: doScrollToBottom && editorInstance && scrollToBottom();
  const onEditorInstanceInit = () => {
    if (!editorInstance) return;
    editorInstance.onDidFocusEditorText(() => {
      isFocused = true;
    });
    editorInstance.onDidBlurEditorText(() => {
      isFocused = false;
    });
  };
  $: editorInstance && onEditorInstanceInit();
</script>

<div class="c-monaco-output-block">
  <div class="c-monaco-output-block__header">
    <slot name="label">
      <TextAugment size={1} weight="medium">{label}</TextAugment>
    </slot>

    <div class="c-monaco-output-block__actions">
      {#if showCopyButton}
        <TextTooltipAugment
          bind:requestClose
          onOpenChange={onCopyOpenChange}
          content={copyMessage || "Copy to clipboard"}
          side="bottom"
          triggerOn={[TooltipTriggerOn.Hover]}
        >
          <IconButtonAugment
            variant="ghost-block"
            color="neutral"
            size={1}
            on:click={handleCopy}
            disabled={!content}
          >
            <ClipboardCopy />
          </IconButtonAugment>
        </TextTooltipAugment>
      {/if}
      {#if showViewInFileButton}
        <TextTooltipAugment content="View full content in editor" side="bottom">
          <IconButtonAugment
            variant="ghost-block"
            color="neutral"
            size={1}
            on:click={onViewInFile}
            disabled={!content}
          >
            <OpenInNewWindow />
          </IconButtonAugment>
        </TextTooltipAugment>
      {/if}
      <slot name="actions"></slot>
    </div>
  </div>
  {#if content}
    <div class="c-monaco-output-block__monaco-container">
      {#if !isFocused}
        <!-- screen to prevent scroll when not focused -->
        <button class="c-monaco-output-block__overlay" on:click={() => editorInstance?.focus()} />
      {/if}
      <MonacoProvider.Root>
        <SimpleMonaco
          height={100}
          options={monacoOptions}
          text={content}
          lang={language}
          bind:editorInstance
        />
      </MonacoProvider.Root>
    </div>
  {:else}
    <div class="c-monaco-output-block__empty-state">
      <TextAugment size={1} color="secondary">No content available.</TextAugment>
    </div>
  {/if}
</div>

<style>
  .c-monaco-output-block {
    background-color: var(--user-theme-panel-background);
    margin-bottom: var(--ds-spacing-2);
  }

  .c-monaco-output-block__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--ds-spacing-1, 4px) var(--ds-spacing-3);
    border: 1px solid var(--augment-border-color, #e0e0e0);
    border-radius: var(--augment-border-radius, var(--ds-radius-2))
      var(--augment-border-radius, var(--ds-radius-2)) 0 0;
    border-bottom: none;
    background-color: var(--user-theme-panel-background);
    flex-wrap: wrap;
  }

  .c-monaco-output-block__actions {
    display: flex;
    gap: var(--ds-spacing-1);
    overflow: scroll;
  }

  .c-monaco-output-block__monaco-container {
    position: relative;
    overflow: hidden;
  }

  .c-monaco-output-block__overlay {
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    z-index: 10;
    background-color: transparent;
    appearance: none;
    border: none;
    cursor: pointer;
    background: var(--ds-panel-translucent);
    opacity: 0.3;
    transition: opacity 0.3s ease;
  }

  .c-monaco-output-block__overlay:hover {
    opacity: 0;
  }

  .c-monaco-output-block__monaco-container:focus-within {
    overflow: auto;
  }

  .c-monaco-output-block__monaco-container:focus-within .c-monaco-output-block__overlay {
    display: none;
  }

  .c-monaco-output-block__monaco-container :global(.c-codeblock__monaco) {
    border-radius: 0 0 var(--augment-border-radius, var(--ds-radius-2))
      var(--augment-border-radius, var(--ds-radius-2));
  }
  .c-monaco-output-block__monaco-container :global(.view-line) {
    padding-left: var(--ds-spacing-3);
  }

  .c-monaco-output-block__empty-state {
    padding: var(--space-m, 16px);
    text-align: center;
    color: var(--text-color-secondary, #666);
  }
</style>
