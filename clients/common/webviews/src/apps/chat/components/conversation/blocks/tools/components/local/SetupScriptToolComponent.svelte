<script lang="ts">
  import { type ChatModel } from "$common-webviews/src/apps/chat/models/chat-model";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import Check from "$common-webviews/src/design-system/icons/check.svelte";
  import FloppyDisk from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/floppy-disk.svg?component";
  import OpenInNewWindow from "$common-webviews/src/design-system/icons/open-in-new-window.svelte";
  import {
    type SetupScript,
    type SetupScriptLocation,
  } from "$vscode/src/utils/remote-agent-setup/types";
  import type { ChatResultToolUse } from "@augment-internal/sidecar-libs/src/chat/chat-types";
  import { getContext, onMount } from "svelte";
  import MonacoOutputBlock from "./MonacoOutputBlock.svelte";
  import { CommandStatus, type SetupScriptToolResult } from "./setup-script-types";

  import ButtonAugment from "$common-webviews/src/design-system/components/ButtonAugment.svelte";
  import ToolUseStatus from "../../ToolUseStatus.svelte";
  import SetupScriptCommandStatus from "./SetupScriptCommandStatus.svelte";

  export let toolUseState: ToolUseState;
  export const toolUseInput: Record<string, unknown> = {};
  export let toolUse: ChatResultToolUse;

  // Parse the input JSON from the toolUse
  $: parsedInput = (() => {
    try {
      // The input is in the format { input_json: "{ ... }" }
      const inputJson = toolUse?.input_json || "{}";
      return typeof inputJson === "string" ? JSON.parse(inputJson) : {};
    } catch (e) {
      console.error("Failed to parse tool input JSON:", e);
      return {};
    }
  })();

  // Parse the tool result from the response
  $: toolResult = (() => {
    try {
      // Get the result from the toolUseState result
      const resultText = toolUseState.result?.text || "{}";
      const parsedResponse =
        typeof resultText === "string"
          ? JSON.parse(resultText)
          : (resultText as SetupScriptToolResult);

      return parsedResponse as SetupScriptToolResult;
    } catch (e) {
      console.error("Failed to parse tool result:", e);
      return null;
    }
  })();

  $: scriptContent = parsedInput.script_content || "";
  $: scriptResult = toolResult?.script_result || null;
  $: testResults = toolResult?.test_results || [];
  $: isLoading = toolUseState.phase === ToolUsePhase.running;
  $: testCommands = (() => {
    try {
      // Extract test commands from the input JSON when in loading state
      if (isLoading && parsedInput.test_commands && Array.isArray(parsedInput.test_commands)) {
        return parsedInput.test_commands;
      }
      return [];
    } catch (e) {
      console.error("Failed to parse test commands:", e);
      return [];
    }
  })();
  let existingScripts: SetupScript[] = [];
  let requestOpen = () => {};

  const chatModel = getContext<ChatModel>("chatModel");
  const extensionClient = chatModel?.extensionClient;
  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);

  const SCRIPT_FILENAME = "setup.sh";

  /**
   * Helper function to open a script in the editor.
   */
  async function _openScriptInEditor(script: SetupScript) {
    try {
      await extensionClient.openFile({
        repoRoot: "", // Assuming repoRoot is not needed or handled by extensionClient
        pathName: script.path,
        allowOutOfWorkspace: true,
        openLocalUri: script.location === "home",
      });
    } catch (error) {
      console.error(`Error opening script ${script.path} in editor:`, error);
    }
  }

  /**
   * Handle save button click
   * Saves the script to the specified location and opens it in the editor
   * If the script already exists, it will open the existing file
   */
  async function handleSave(e: Event, optionId: SetupScriptLocation) {
    e.stopPropagation();

    const existingScript = existingScripts.find((script) => script.location === optionId);

    // If the script already exists, open it instead of saving again
    if (existingScript) {
      try {
        await _openScriptInEditor(existingScript);
        return;
      } catch (error) {
        console.error("Error opening existing script:", error);
      }
    }

    try {
      const location = optionId;

      const result = await remoteAgentsModel.saveSetupScript(
        SCRIPT_FILENAME,
        scriptContent,
        location,
      );

      if (result.success && result.path) {
        const newScript: SetupScript = {
          name: SCRIPT_FILENAME,
          path: result.path,
          content: scriptContent,
          location,
        };
        existingScripts.push(newScript);
        await _openScriptInEditor(newScript);
      } else {
        console.error("Failed to save script:", result.error);
      }
    } catch (error) {
      console.error("Error saving script:", error);
    }
    await checkExistingScript();
  }

  /**
   * Handle view button click
   * Opens the script in a new untitled editor or opens the existing file if already saved
   */
  async function handleView(e: Event) {
    e.stopPropagation();
    const existingScript = existingScripts.length > 0 ? existingScripts[0] : null;

    try {
      // If the script already exists, open the existing file
      if (existingScript) {
        await _openScriptInEditor(existingScript);
      } else {
        // Otherwise open in a scratch file
        extensionClient.openScratchFile(scriptContent, "shellscript");
      }
    } catch (error) {
      console.error("Error opening script in editor:", error);
    }
  }

  /**
   * Handle view output button click
   * Opens the command output in a new untitled editor
   */
  function viewInFile(text: string, language: string = "plaintext") {
    extensionClient.openScratchFile(text, language);
  }

  /**
   * Check if the script already exists in saved scripts
   */
  async function checkExistingScript() {
    try {
      const scripts = await remoteAgentsModel.listSetupScripts();
      const matchingScripts = scripts.filter(
        (script) => script.content.trim() === scriptContent.trim(),
      );
      existingScripts = matchingScripts;
    } catch (error) {
      console.error("Error checking existing scripts:", error);
    }
  }

  $: saveOptions = (() => {
    const savedToGit = existingScripts.some((script) => script.location === "git");
    const savedToHome = existingScripts.some((script) => script.location === "home");

    const gitOption = {
      id: "git" as SetupScriptLocation,
      label: savedToGit ? "Saved in git repository" : "Save in git repository",
      endIcon: savedToGit ? Check : undefined,
    };
    const homeOption = {
      id: "home" as SetupScriptLocation,
      label: savedToHome ? "Saved locally (in home directory)" : "Save locally (in home directory)",
      endIcon: savedToHome ? Check : undefined,
    };

    return [homeOption, gitOption];
  })();

  // Check for existing script when component is mounted
  onMount(async () => {
    await checkExistingScript();
  });

  const statusMap = {
    [CommandStatus.SUCCESS]: "success",
    [CommandStatus.FAILED]: "error",
    [CommandStatus.SKIPPED]: "skipped",
  } as const;

  $: statusSummary = (() => {
    if (isLoading) {
      return "Running script...";
    }
    if (toolUseState.phase === ToolUsePhase.completed) {
      return "Script ran successfully";
    }
    if (toolUseState.phase === ToolUsePhase.error) {
      if (scriptResult?.status === CommandStatus.FAILED) {
        return "Script failed to run";
      } else if (testResults.some((result) => result.status === CommandStatus.FAILED)) {
        return "Script ran with failed tests";
      } else if (testResults.some((result) => result.status === CommandStatus.SKIPPED)) {
        return "Script ran but skipped tests";
      } else {
        return "Script failed";
      }
    }
    if (toolUseState.phase === ToolUsePhase.cancelled) {
      return "Script cancelled";
    }
    if (toolUseState.phase === ToolUsePhase.runnable) {
      return "Waiting for user approval to run";
    }
    return "";
  })();
</script>

<div class="c-setup-script-tool">
  <div class="c-setup-script-tool__id" data-tool-id={toolUse.tool_use_id}></div>
  <div class="c-setup-script-tool__script">
    <TextAugment size={2} weight="medium">Setup script created</TextAugment>
    <div class="c-setup-script-tool__script__actions">
      <TextAugment size={1}>
        {statusSummary}
        <ToolUseStatus />
      </TextAugment>
    </div>
  </div>

  <div class="c-setup-script-tool__section">
    {#if scriptContent}
      <MonacoOutputBlock
        label="Generated script"
        content={scriptContent}
        language="bash"
        onViewInFile={handleView}
      >
        <div slot="actions">
          <DropdownMenuAugment.Root bind:requestOpen>
            <DropdownMenuAugment.Trigger>
              <TextTooltipAugment content="Save script" side="bottom">
                <div class="c-setup-script-tool__save-button">
                  <ButtonAugment
                    variant="ghost-block"
                    color="neutral"
                    size={1}
                    on:click={requestOpen}
                  >
                    <FloppyDisk slot="iconLeft" />
                    Save
                  </ButtonAugment>
                </div>
              </TextTooltipAugment>
            </DropdownMenuAugment.Trigger>
            <DropdownMenuAugment.Content side="bottom" align="start">
              {#each saveOptions as option}
                <DropdownMenuAugment.Item
                  onSelect={(e) => {
                    handleSave(e, option.id);
                  }}
                >
                  <div class="c-setup-script-tool__save-option-container">
                    <span>{option.label}</span>
                    {#if option.endIcon}
                      <div class="c-setup-script-tool__save-option-icon">
                        <svelte:component this={option.endIcon} />
                      </div>
                    {/if}
                  </div>
                </DropdownMenuAugment.Item>
              {/each}
            </DropdownMenuAugment.Content>
          </DropdownMenuAugment.Root>
        </div>
      </MonacoOutputBlock>
    {/if}

    {#if scriptResult || isLoading}
      {#if scriptResult?.output}
        <MonacoOutputBlock
          content={scriptResult.output}
          language="bash"
          doScrollToBottom
          onViewInFile={() => viewInFile(scriptResult.output || "No output available", "bash")}
        >
          <div class="c-setup-script-tool__row" slot="label">
            <div>
              <SetupScriptCommandStatus
                commandResult={isLoading
                  ? "loading"
                  : scriptResult.status
                    ? statusMap[scriptResult.status]
                    : "none"}
              />
            </div>
            <div class="c-setup-script-tool__row__title">
              <TextAugment size={1} weight="medium">
                {#if isLoading}
                  Running script...
                {:else if scriptResult.status === CommandStatus.SUCCESS}
                  Script ran successfully
                {:else if scriptResult.status === CommandStatus.FAILED}
                  Script failed to run
                {/if}
              </TextAugment>
            </div>
          </div>
        </MonacoOutputBlock>
      {/if}
    {/if}
  </div>

  {#if testResults.length > 0 || (isLoading && testCommands.length > 0)}
    <div class="c-setup-script-tool__section">
      <TextAugment size={1} weight="medium" class="c-setup-script-tool__section-header"
        >Test Results:</TextAugment
      >
      {#if isLoading && testCommands.length > 0}
        {#each testCommands as command}
          <div class="c-setup-script-tool__row">
            <div class="c-setup-script-tool__row__status">
              <SetupScriptCommandStatus commandResult="loading" />
            </div>
            <div class="c-setup-script-tool__row__title">
              <TextTooltipAugment
                content={command}
                side="bottom"
                class="c-setup-script-tool__row__title__container"
              >
                <TextAugment size={1} weight="medium">
                  {command}
                </TextAugment>
              </TextTooltipAugment>
            </div>
            <TextTooltipAugment content="View full test result in editor" side="bottom">
              <ButtonAugment
                variant="ghost-block"
                color="neutral"
                size={1}
                on:click={() => viewInFile(command)}
              >
                View
                <OpenInNewWindow slot="iconRight" />
              </ButtonAugment>
            </TextTooltipAugment>
          </div>
        {/each}
      {:else}
        {#each testResults as testResult}
          <div class="c-setup-script-tool__row">
            <div class="c-setup-script-tool__row__status">
              <SetupScriptCommandStatus
                commandResult={testResult.status ? statusMap[testResult.status] : "none"}
              />
            </div>
            <div class="c-setup-script-tool__row__title">
              <TextTooltipAugment
                content={testResult.command}
                side="bottom"
                class="c-setup-script-tool__row__title__container"
              >
                <TextAugment size={1} type="monospace">
                  {testResult.command}
                </TextAugment>
              </TextTooltipAugment>
            </div>
            <div class="c-setup-script-tool__row__actions">
              <TextTooltipAugment content="View full test result in editor" side="bottom">
                <ButtonAugment
                  variant="ghost-block"
                  color="neutral"
                  size={1}
                  on:click={() => viewInFile(testResult.output || "No output available")}
                >
                  View
                  <OpenInNewWindow slot="iconRight" />
                </ButtonAugment>
              </TextTooltipAugment>
            </div>
          </div>
        {/each}
      {/if}
    </div>
  {/if}
</div>

<style>
  .c-setup-script-tool {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-3);
    padding: var(--ds-spacing-2) 0 var(--ds-spacing-3);
    cursor: default;
    background-color: var(--user-theme-sidebar-background);
    border-radius: var(--ds-radius-2);
    border: 1px solid var(--augment-border-color);
  }

  .c-setup-script-tool__id {
    position: absolute;
    /* offset the scroll target to account for the top chat padding */
    top: -20px;
  }

  .c-setup-script-tool__section {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
    padding: 0 var(--ds-spacing-3);
  }

  .c-setup-script-tool__section :global(.c-setup-script-tool__section-header) {
    padding-left: var(--ds-spacing-2);
  }

  .c-setup-script-tool__script {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    border-bottom: 1px solid var(--augment-border-color);
    padding: 0 var(--ds-spacing-3) var(--ds-spacing-2) var(--ds-spacing-3);
  }

  .c-setup-script-tool__script__actions {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-setup-script-tool__save-button {
    display: inline-flex;
  }

  .c-setup-script-tool__save-option-container {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
  }

  .c-setup-script-tool__save-option-icon {
    width: 12px;
    height: 12px;
    fill: var(--ds-color-success-9);
    margin-left: var(--ds-spacing-2);
  }

  .c-setup-script-tool__row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 6px;
  }
  .c-setup-script-tool__row__status {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: none;
    gap: 6px;
  }

  .c-setup-script-tool__row__title {
    flex: 1;
  }
  .c-setup-script-tool__row__title :global(.c-setup-script-tool__row__title__container) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }
</style>
