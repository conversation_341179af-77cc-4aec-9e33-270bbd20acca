<script lang="ts" context="module">
  export const EXPAND_THREADS_BUTTON_TEST_ID = "threads-expand-button";

  // Define thread types for type safety
  /* eslint-disable @typescript-eslint/naming-convention */
  export type ChatThread = {
    id: string;
    type: "chat";
    isNew?: boolean;
    title: string;
    updated_at: string;
    started_at: string;
    isPinned: boolean | undefined;
    conversation: IConversation;
    sortTimestamp: Date;
  };

  export type LocalAgentThread = {
    id: string;
    type: "localAgent";
    isNew?: boolean;
    title: string;
    updated_at: string;
    started_at: string;
    isPinned: boolean | undefined;
    conversation: IConversation;
    sortTimestamp: Date;
  };

  export type RemoteAgentThread = {
    id: string;
    type: "remoteAgent";
    isNew?: boolean;
    title: string;
    updated_at: string;
    started_at: string;
    status: RemoteAgentStatus;
    workspace_status: RemoteAgentWorkspaceStatus;
    is_setup_script_agent: boolean | undefined;
    workspace_setup: RemoteAgentWorkspaceSetup;
    has_updates?: boolean;
    agent: RemoteAgent;
    sortTimestamp: Date;
    isPinned?: boolean | undefined;
    conversation?: IConversation;
  };

  export type Thread = ChatThread | LocalAgentThread | RemoteAgentThread;
  /* eslint-enable @typescript-eslint/naming-convention */

  // Helper function to check if a remote agent thread is recent
  export function isRecentRemoteAgent(thread: Thread): boolean {
    if (thread.id === NEW_AGENT_KEY) return false;
    // our definition is: updated within the last week
    if (thread.type !== "remoteAgent") {
      return false;
    }
    const remoteThread = thread as RemoteAgentThread;
    const updatedAt = new Date(remoteThread.updated_at);
    const now = new Date();
    const diffMs = now.getTime() - updatedAt.getTime();
    const diffHours = Math.floor(diffMs / 1000 / 60 / 60);
    return diffHours < 7 * 24;
  }
</script>

<script lang="ts">
  import DropdownMenuAugment from "$common-webviews/src/design-system/components/DropdownMenuAugment";
  import IconButtonAugment from "$common-webviews/src/design-system/components/IconButtonAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import TextFieldAugment from "$common-webviews/src/design-system/components/TextFieldAugment.svelte";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ChevronDown from "$common-webviews/src/design-system/icons/chevron-down.svelte";
  import SearchIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/magnifying-glass.svg?component";
  import RegularSidebarIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/sidebar.svg?component";
  import ExclamationTriangle from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/triangle-exclamation.svg?component";
  import XMarkIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/xmark.svg?component";
  import RegularEllipsisIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/ellipsis.svg?component";
  import Trash from "$common-webviews/src/design-system/icons/trash.svelte";
  import CloudArrowUp from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/cloud-arrow-up.svg?component";

  import { ChatModeModel } from "$common-webviews/src/apps/chat/models/chat-mode-model";
  import type {
    ChatModel,
    SortableConversationFieldType,
  } from "$common-webviews/src/apps/chat/models/chat-model";
  import { ConversationModel } from "$common-webviews/src/apps/chat/models/conversation-model";
  import type { IConversation } from "$common-webviews/src/apps/chat/models/types";
  import { getThreadsWithSearchQuery } from "$common-webviews/src/apps/chat/utils/thread-search";
  import {
    ConversationAge,
    groupThreadsByAgeWithActive,
  } from "$common-webviews/src/apps/chat/utils/thread-grouping";
  import { createOverscrollToClose } from "$common-webviews/src/apps/chat/utils/overscroll-to-close";
  import { GitReferenceModel } from "$common-webviews/src/apps/remote-agent-manager/models/git-reference-model";
  import { RemoteAgentsModel } from "$common-webviews/src/apps/remote-agent-manager/models/remote-agents-model";
  import { getRelativeTimeForStr } from "$common-webviews/src/apps/remote-agent-manager/utils";
  import {
    getOrgRepoFromUrl,
    getRepoNameFromUrl,
  } from "$common-webviews/src/apps/remote-agent-manager/utils/repository-utils";
  import { type SharedWebviewStoreModel } from "$common-webviews/src/common/models/shared-webview-store-model";
  import { TooltipTriggerOn } from "$common-webviews/src/design-system/_primitives/TooltipAugment/types";
  import {
    type RemoteAgent,
    RemoteAgentStatus,
    type RemoteAgentWorkspaceSetup,
    type RemoteAgentWorkspaceStatus,
  } from "$vscode/src/remote-agent-manager/types";
  import {
    type ChatHomeWebviewState,
    SHARED_AGENT_STORE_CONTEXT_KEY,
  } from "$vscode/src/webview-panels/remote-agents/common-webview-store";

  import { getContext, onMount } from "svelte";
  import { writable } from "svelte/store";
  import { fly, slide } from "svelte/transition";
  import NewThreadDropdown from "./NewThreadDropdown.svelte";
  import ThreadsListRowItem from "./ThreadsListRowItem.svelte";
  import ThreadTypeFilterDropdown, {
    type ThreadTypeFilter,
  } from "./ThreadTypeFilterDropdown.svelte";

  import { NEW_AGENT_KEY } from "../../models/agent-constants";
  import {
    createResizeState,
    startDrag as startResizeDrag,
    doDrag as doResizeDrag,
    stopDrag as stopResizeDrag,
    resetDragClick,
    calculateMaxHeight,
    constrainHeightToContent,
  } from "../../utils/resize-utils";

  /**
   * This is a threads menu that shows all threads (chat, local agents, and remote agents).
   */

  //#region Context and states

  const NEW_REMOTE_AGENT_THREAD_ID = NEW_AGENT_KEY;

  const remoteAgentsModel = getContext<RemoteAgentsModel>(RemoteAgentsModel.key);
  const sharedWebviewStore = getContext<SharedWebviewStoreModel<ChatHomeWebviewState>>(
    SHARED_AGENT_STORE_CONTEXT_KEY,
  );
  const gitReferenceModel = getContext<GitReferenceModel>(GitReferenceModel.key);
  const chatModeModel = getContext<ChatModeModel>(ChatModeModel.key);

  // Get the chat model from props or context
  export let chatModelProp: ChatModel | undefined = undefined;
  let chatModel = chatModelProp || getContext<ChatModel>("chatModel");

  // Variables for chat thread menu
  const enableShareService = writable(false);

  let searchInputElement: HTMLInputElement | undefined = undefined;

  // Fetch the current repository URL on mount
  onMount(async () => {
    try {
      void gitReferenceModel.getRemoteUrl();

      // Set enableShareService based on chat model flags
      if (chatModel && chatModel.flags) {
        enableShareService.update(() => chatModel.flags.enableShareService || false);
      }
    } catch (error) {
      console.error("Failed to get current repository URL:", error);
    }
  });

  $: currentAgentId = $remoteAgentsModel.currentAgentId;
  $: remoteAgents = $remoteAgentsModel.agentOverviews || [];

  // Get conversations directly from the chat model if available
  $: allChatModelConversations = chatModel ? Object.values($chatModel.orderedConversations()) : [];
  $: newLocalConversation = allChatModelConversations.find(ConversationModel.isNew);
  $: chatConversations = chatModel
    ? allChatModelConversations.filter(
        (c) => !c.extraData?.isAgentConversation && c !== newLocalConversation,
      )
    : [];
  $: localAgentConversations = chatModel
    ? allChatModelConversations.filter(
        (c) => c.extraData?.isAgentConversation === true && c !== newLocalConversation,
      )
    : [];

  $: threadsError = $remoteAgentsModel.agentThreadsError; // Use the specific error state for agent threads
  $: isLoading = $remoteAgentsModel.isLoading;
  $: hasFetchedOnce = $remoteAgentsModel.hasFetchedOnce;
  $: lastSuccessfulOverviewFetch = $remoteAgentsModel.lastSuccessfulOverviewFetch;
  $: isHomePanelOpen = $sharedWebviewStore.state?.activeWebviews.includes("home");

  // Thread counts are displayed in the filter dropdown

  // Get the current sort preference from the chat model
  $: sortConversationsBy = chatModel
    ? chatModel.sortConversationsBy
    : writable<SortableConversationFieldType>("lastMessageTimestamp");
  $: currentSortPreference = $sortConversationsBy || "lastMessageTimestamp";
  $: agentExecutionMode = $chatModel.agentExecutionMode;

  // Helper function to get the correct timestamp for sorting based on user preference
  function getConversationTimestamp(conversation: IConversation | undefined): Date {
    if (!conversation) {
      return new Date(0);
    }
    const sortBy = currentSortPreference;
    return ConversationModel.getTime(conversation, sortBy);
  }

  // Track the currently selected thread ID
  $: selectedThreadId = $remoteAgentsModel.isActive
    ? (currentAgentId ?? NEW_REMOTE_AGENT_THREAD_ID)
    : $chatModel.currentConversationId;

  //#region Search functionality

  let searchQuery = "";
  let isSearchActive = false;

  function toggleSearch() {
    isSearchActive = !isSearchActive;
    isExpanded = isSearchActive;
    if (!isSearchActive) {
      searchQuery = "";
    } else {
      setTimeout(() => {
        searchInputElement?.focus();
      }, 30);
    }
  }

  function clearSearch() {
    searchQuery = "";
    isSearchActive = false;
  }

  // Simple container tracking for resize functionality
  let heightOfList = 0;
  let heightOfListContainer = 0;
  let containerWidth = 0;

  // Reference to the agents list element
  let agentsListElement: HTMLDivElement | undefined = undefined;

  //#region Scroll to close functionality

  // Create overscroll actions using the utility
  const overscrollToCloseMainList = createOverscrollToClose(
    () => {
      isExpanded = false;
    },
    () => isExpanded,
  );

  const overscrollToCloseActiveAgents = createOverscrollToClose(
    () => {
      isRecentRemoteAgentsListExpanded.set(false);
    },
    () => $isRecentRemoteAgentsListExpanded,
  );

  //#region Threads

  /**
   * Toggles the pinned status of a thread
   * @param thread The thread to toggle pinned status for
   */
  async function toggleThreadPinned(thread: Thread, event?: Event) {
    if (event) {
      event.stopPropagation(); // Prevent triggering thread selection
    }

    if (thread.isNew) {
      return;
    } else if (thread.type === "remoteAgent") {
      // Get the updated pinnedAgents from the toggleAgentPinned method
      const pinnedAgents = await remoteAgentsModel.toggleAgentPinned(
        thread.id,
        thread.isPinned || false,
      );

      // Update the shared store with the returned pinnedAgents
      sharedWebviewStore.update((state) => {
        if (!state) return;
        return {
          ...state,
          pinnedAgents,
        };
      });
    } else if (["chat", "localAgent"].includes(thread.type)) {
      // For chat and local agent threads, use the chat model's pinning functionality
      if (chatModel) {
        chatModel.toggleConversationPinned(thread.id);
      }
    }
  }
  // Create thread objects for chat and local agent conversations
  /* eslint-disable @typescript-eslint/naming-convention */
  $: newThread = (() => {
    if ($remoteAgentsModel.isActive) {
      if ($remoteAgentsModel.isCreatingAgent) {
        return undefined;
      }
      const draft = $chatModel.currentConversationModel.draftExchange?.request_message;
      return {
        id: NEW_REMOTE_AGENT_THREAD_ID,
        isNew: true,
        type: "remoteAgent",
        title: draft,
        updated_at: new Date().toISOString(),
        started_at: new Date().toISOString(),
        isPinned: false,
        conversation: undefined,
        sortTimestamp: new Date(),
      };
    }

    // newConversation is the draft local agent or chat conversation
    if (newLocalConversation) {
      return {
        id: newLocalConversation.id,
        isNew: true,
        type: newLocalConversation.extraData?.isAgentConversation ? "localAgent" : "chat",
        title: "",
        updated_at: newLocalConversation.lastInteractedAtIso,
        started_at: newLocalConversation.createdAtIso,
        isPinned: false,
        conversation: newLocalConversation,
        sortTimestamp: getConversationTimestamp(newLocalConversation),
      };
    }
  })();

  $: chatThreads = chatConversations.map(
    (conversation: IConversation): ChatThread => ({
      id: conversation.id,
      isNew: false,
      type: "chat",
      title: conversation.name || "",
      updated_at: conversation.lastInteractedAtIso,
      started_at: conversation.createdAtIso,
      isPinned: conversation.isPinned,
      conversation: conversation,
      sortTimestamp: getConversationTimestamp(conversation),
    }),
  );

  $: localAgentThreads = localAgentConversations.map(
    (conversation: IConversation): LocalAgentThread => ({
      id: conversation.id,
      isNew: false,
      type: "localAgent",
      title: conversation.name || "",
      updated_at: conversation.lastInteractedAtIso,
      started_at: conversation.createdAtIso,
      isPinned: conversation.isPinned,
      conversation: conversation,
      sortTimestamp: getConversationTimestamp(conversation),
    }),
  );

  // Get pinned agents from both the model and the shared store to ensure they're in sync
  $: pinnedAgents = $sharedWebviewStore.state?.pinnedAgents || {};

  // Convert remote agents to thread objects
  $: remoteAgentThreads = remoteAgents.map(
    (agent: RemoteAgent): RemoteAgentThread => ({
      id: agent.remote_agent_id,
      isNew: false,
      type: "remoteAgent",
      title: agent.session_summary,
      updated_at: agent.updated_at,
      started_at: agent.started_at,
      status: agent.status,
      workspace_status: agent.workspace_status,
      is_setup_script_agent: agent.is_setup_script_agent,
      workspace_setup: agent.workspace_setup || {},
      agent: agent,
      // For remote agents, use the updated_at timestamp for sorting
      sortTimestamp: new Date(agent.updated_at || agent.started_at),
      // Get pinned status from the global store
      isPinned: pinnedAgents[agent.remote_agent_id] || false,
      has_updates: agent.has_updates,
    }),
  );
  /* eslint-enable @typescript-eslint/naming-convention */

  $: doShowNewThread = newThread && selectedThreadId === newThread.id;

  // Combine all thread types
  $: allThreads = [
    doShowNewThread && newThread,
    ...chatThreads,
    ...localAgentThreads,
    ...remoteAgentThreads,
  ].filter(Boolean) as Thread[];

  // Thread type filter
  $: threadTypeFilters = [
    { value: "all" as ThreadTypeFilter, label: "All threads", count: allThreads.length },
    { value: "chat" as ThreadTypeFilter, label: "Chats", count: chatThreads.length },
    {
      value: "localAgent" as ThreadTypeFilter,
      label: "Local agents",
      count: localAgentThreads.length,
    },
    {
      value: "remoteAgent" as ThreadTypeFilter,
      label: "Remote agents",
      count: remoteAgentThreads.length,
    },
  ].filter((filter) => filter.count > 0);
  const activeThreadTypeFilter = writable<ThreadTypeFilter>("all");

  // Filter threads by type and search query
  $: filteredThreads = allThreads.filter((thread) => {
    // First filter by type
    if ($activeThreadTypeFilter !== "all" && thread.type !== $activeThreadTypeFilter) {
      return false;
    }

    // Then filter by search query if active
    return getThreadsWithSearchQuery(
      thread,
      searchQuery,
      $remoteAgentsModel.currentAgentId,
      $remoteAgentsModel.currentConversation,
    );
  });

  //#region Expand/collapse

  // State for manual expansion/collapse
  let isExpanded = false;

  // State for showing remote agents when collapsed
  const isRecentRemoteAgentsListExpanded = writable(false);

  // Toggle the expanded state
  function toggleExpanded() {
    isExpanded = !isExpanded;
    if (isExpanded) {
      isRecentRemoteAgentsListExpanded.set(false);
      // constrain height to make sure we're not too large

      const defaultHeight = resizeState.currentHeight || baseResizeConfig.defaultHeight;
      const maxHeight = calculateMaxHeight(
        resizeConfig.windowHeight,
        resizeConfig.maxContentHeight,
      );

      // Constrain height within min and max bounds
      const constrainedHeight = Math.max(
        resizeConfig.minHeight,
        Math.min(maxHeight, defaultHeight),
      );

      // Update the resize state with the constrained height
      resizeState = {
        ...resizeState,
        currentHeight: constrainedHeight,
      };
    }
  }

  // Toggle showing remote agents when collapsed
  function toggleShowRemoteAgents() {
    isRecentRemoteAgentsListExpanded.update((value) => !value);
    isExpanded = false;
    if ($isRecentRemoteAgentsListExpanded) {
      // constrain height to make sure we're not too large
      const maxHeight = calculateMaxHeight(
        activeAgentsResizeConfig.windowHeight,
        activeAgentsResizeConfig.maxContentHeight,
      );

      // Constrain height within min and max bounds
      const constrainedHeight = Math.max(
        activeAgentsResizeConfig.minHeight,
        Math.min(maxHeight, activeAgentsResizeConfig.defaultHeight),
      );

      // Update the resize state with the constrained height
      activeAgentsResizeState = {
        ...activeAgentsResizeState,
        currentHeight: constrainedHeight,
      };
    }
  }

  /**
   * Switches to the appropriate mode based on the thread type and updates all necessary state.
   * This function centralizes the mode switching logic to ensure consistency across the application.
   *
   * @param thread The thread to switch to
   * @returns A boolean indicating whether the switch was successful
   */
  function switchToThread(thread: Thread): boolean {
    if (!thread) return false;

    // Optimistically clear has_updates for remote agent threads
    if (thread.type === "remoteAgent" && thread.has_updates) {
      remoteAgentsModel.optimisticallyClearAgentUpdates(thread.id);
    }

    // Set the active state based on thread type
    remoteAgentsModel.setIsActive(thread.type === "remoteAgent");

    // Use the ChatModeModel's switchToThread method to handle the mode switching
    const success = chatModeModel.switchToThread(thread.type, thread.id, $agentExecutionMode);

    if (success) {
      // close expanded lists
      isExpanded = false;
      isRecentRemoteAgentsListExpanded.set(false);
    }

    return success;
  }

  function onWindowFocus() {
    remoteAgentsModel.setIsPanelFocused(true);
  }

  function onWindowBlur() {
    remoteAgentsModel.setIsPanelFocused(false);
  }

  function onKeyDown(event: KeyboardEvent) {
    if (event.key === "Escape") {
      // Close expanded lists on Esc key
      if (isExpanded) {
        isExpanded = false;
        event.preventDefault();
        event.stopPropagation();
      } else if ($isRecentRemoteAgentsListExpanded) {
        isRecentRemoteAgentsListExpanded.set(false);
        event.preventDefault();
        event.stopPropagation();
      }
    }
  }

  // Number of skeleton rows to show when loading
  const skeletonRowCount = 3;

  // Create an array of skeleton rows
  $: skeletonRows = Array(skeletonRowCount).fill(null);

  // Override the actual state with debug state when in debug mode
  $: overviewError = threadsError?.errorMessage;
  $: effectiveIsLoading = isLoading && !hasFetchedOnce;
  $: effectiveHasError = !!overviewError && hasFetchedOnce;
  $: effectiveIsEmpty = hasFetchedOnce && filteredThreads.length === 0;

  // Calculate displayed groups - only show threads when expanded
  $: displayedGroups = isExpanded ? groupThreadsByAgeWithActive(filteredThreads) : [];

  //#region Resizing

  const THREAD_ITEM_HEIGHT = 25;
  let windowHeight = 0;

  // Resize configuration for main threads list
  const baseResizeConfig = {
    threadItemHeight: THREAD_ITEM_HEIGHT,
    minHeight: THREAD_ITEM_HEIGHT * 3, // Minimum 3 rows
    defaultHeight: THREAD_ITEM_HEIGHT * 15,
  };
  $: resizeConfig = {
    ...baseResizeConfig,
    windowHeight,
    maxContentHeight: filteredThreads.length * THREAD_ITEM_HEIGHT,
  };

  // Resize configuration for active remote agents list
  const baseActiveAgentsResizeConfig = {
    threadItemHeight: THREAD_ITEM_HEIGHT,
    minHeight: THREAD_ITEM_HEIGHT * 1,
    defaultHeight: THREAD_ITEM_HEIGHT * 4,
  };
  $: activeAgentsResizeConfig = {
    ...baseActiveAgentsResizeConfig,
    windowHeight,
    maxContentHeight: recentRemoteAgents.length * THREAD_ITEM_HEIGHT,
  };

  // Initialize resize state using utility functions for main threads list
  let resizeState = createResizeState(baseResizeConfig.defaultHeight);

  // Initialize resize state for active remote agents list
  let activeAgentsResizeState = createResizeState(baseActiveAgentsResizeConfig.defaultHeight);

  // Reactively constrain height when content changes
  $: if (isExpanded) {
    resizeState = constrainHeightToContent(resizeState, resizeConfig);
  }

  $: if (!isExpanded && $isRecentRemoteAgentsListExpanded) {
    activeAgentsResizeState = constrainHeightToContent(
      activeAgentsResizeState,
      activeAgentsResizeConfig,
    );
  }

  // Drag handlers for main threads list using utility functions
  function startDrag(event: MouseEvent) {
    resizeState = startResizeDrag(event, resizeState, isExpanded);
  }

  function doDrag(event: MouseEvent) {
    resizeState = doResizeDrag(event, resizeState, resizeConfig, isExpanded);
  }

  function stopDrag() {
    resizeState = stopResizeDrag(resizeState);
    setTimeout(() => {
      resizeState = resetDragClick(resizeState);
    }, 0);
  }

  // Drag handlers for active remote agents list
  function startActiveAgentsDrag(event: MouseEvent) {
    activeAgentsResizeState = startResizeDrag(
      event,
      activeAgentsResizeState,
      !isExpanded && $isRecentRemoteAgentsListExpanded,
    );
  }

  function doActiveAgentsDrag(event: MouseEvent) {
    activeAgentsResizeState = doResizeDrag(
      event,
      activeAgentsResizeState,
      activeAgentsResizeConfig,
      !isExpanded && $isRecentRemoteAgentsListExpanded,
    );
  }

  function stopActiveAgentsDrag() {
    activeAgentsResizeState = stopResizeDrag(activeAgentsResizeState);
    setTimeout(() => {
      activeAgentsResizeState = resetDragClick(activeAgentsResizeState);
    }, 0);
  }

  // Reference to the active remote agents list element
  let activeAgentsListElement: HTMLDivElement | undefined = undefined;

  // Compute height values reactively instead of manipulating DOM directly
  $: agentsListHeight = isExpanded ? `${resizeState.currentHeight}px` : "";
  $: agentsListMaxHeight = isExpanded ? `${resizeState.currentHeight}px` : "";
  $: activeAgentsListHeight =
    !isExpanded && $isRecentRemoteAgentsListExpanded
      ? `${activeAgentsResizeState.currentHeight}px`
      : "";
  $: activeAgentsListMaxHeight =
    !isExpanded && $isRecentRemoteAgentsListExpanded
      ? `${activeAgentsResizeState.currentHeight}px`
      : "";

  //#region Repo

  // Helper function to get repository URL from agent
  function getAgentRepoUrl(agent: RemoteAgent): string {
    return agent.workspace_setup?.starting_files?.github_commit_ref?.repository_url || "";
  }

  const currentRepoUrl = gitReferenceModel.currentRepositoryUrl;

  // Helper function to check if an agent is from a different repo
  function isFromDifferentRepo(agent: RemoteAgent): boolean {
    if (!agent || typeof agent !== "object" || !("workspace_setup" in agent)) {
      return false;
    }
    const agentRepoUrl = getAgentRepoUrl(agent);
    return !!agentRepoUrl && !!$currentRepoUrl && agentRepoUrl !== $currentRepoUrl;
  }

  // Helper function to get repo info for display
  function getRepoInfo(agent: RemoteAgent): { name: string; fullPath: string } {
    if (!agent || typeof agent !== "object" || !("workspace_setup" in agent)) {
      return { name: "Unknown", fullPath: "Unknown" };
    }
    const repoUrl = getAgentRepoUrl(agent);
    return {
      name: getRepoNameFromUrl(repoUrl),
      fullPath: getOrgRepoFromUrl(repoUrl),
    };
  }

  //#region Active remote agents

  // Calculate active remote agents (remote workspaces that are not paused)
  $: recentRemoteAgents = filteredThreads.filter(isRecentRemoteAgent);
  $: recentRemoteAgentCount = recentRemoteAgents.length;

  // Calculate running agents (agentRunning or agentStarting)
  $: runningRemoteAgents = remoteAgentThreads.filter(
    (thread) =>
      (thread.status === RemoteAgentStatus.agentRunning ||
        thread.status === RemoteAgentStatus.agentStarting) &&
      !thread.has_updates,
  );
  $: runningRemoteAgentsCount = runningRemoteAgents.length;

  // Calculate agents with changes (has_updates: true)
  $: agentsWithChanges = remoteAgentThreads.filter((thread) => !!thread.has_updates);
  $: agentsWithChangesCount = agentsWithChanges.length;

  /**
   * Deletes all threads in a group
   * @param threads - Array of threads to delete
   */
  async function deleteAllThreadsInGroup(threads: Thread[]) {
    // Separate conversation IDs from remote agent IDs
    const conversationIds: string[] = [];
    const remoteAgentIds: string[] = [];

    for (const thread of threads) {
      if (thread.type === "remoteAgent") {
        remoteAgentIds.push(thread.id);
      } else {
        conversationIds.push(thread.id);
      }
    }

    await chatModel.deleteConversations(
      conversationIds,
      undefined, // nextConvoId
      remoteAgentIds, // Remote agent IDs to delete
      remoteAgentsModel, // Pass the remote agents model for handling remote agent deletions
    );
  }
</script>

<svelte:window
  on:mousemove={(event) => {
    doDrag(event);
    doActiveAgentsDrag(event);
  }}
  on:mouseup={() => {
    stopDrag();
    stopActiveAgentsDrag();
  }}
  bind:innerHeight={windowHeight}
  on:focus={onWindowFocus}
  on:blur={onWindowBlur}
  on:keydown={onKeyDown}
/>

<div class="remote-agent-threads-container">
  <div class="remote-agent-threads" bind:clientWidth={containerWidth}>
    <div
      class="agents-list-container"
      class:is-dragging={resizeState.isDragging || activeAgentsResizeState.isDragging}
      class:is-expanded={isExpanded}
      role="region"
      aria-label="Remote agents list"
    >
      <div class="header">
        <!-- Expand/collapse toggle button with expanded click target -->
        <div class="header-left">
          <button
            class="expand-toggle-button-container {isExpanded
              ? 'expand-toggle-button-container--active'
              : ''}"
            on:click={toggleExpanded}
            on:keydown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                toggleExpanded();
                e.stopPropagation();
                e.preventDefault();
              }
            }}
            aria-label={isExpanded ? "Collapse threads" : "Expand threads"}
            aria-expanded={isExpanded}
            type="button"
            data-testid={EXPAND_THREADS_BUTTON_TEST_ID}
          >
            <div class="expand-toggle-button {isExpanded ? 'expand-toggle-button--active' : ''}">
              <ChevronDown />
            </div>
            <!-- Threads text inside clickable area -->
            <div class="header-text">
              <TextAugment size={1} class="header-text-main">Threads</TextAugment>
            </div>

            <div class="thread-type-filter">
              <!-- Thread type filter dropdown (when expanded) or remote agents toggle (when collapsed) -->
              {#if !isSearchActive && isExpanded}
                <div transition:slide={{ axis: "x", duration: 150 }}>
                  <ThreadTypeFilterDropdown {threadTypeFilters} {activeThreadTypeFilter} />
                </div>
              {/if}
            </div>
            {#if isExpanded}
              <div
                class="search-container action-button"
                class:is-active={isSearchActive}
                transition:fly={{ y: -2, duration: 150 }}
              >
                {#if isSearchActive}
                  <div
                    class="search-field-container"
                    transition:slide={{ axis: "x", duration: 150 }}
                  >
                    <TextFieldAugment
                      bind:value={searchQuery}
                      placeholder={`Search ${$activeThreadTypeFilter === "all" ? "threads" : threadTypeFilters.find((f) => f.value === $activeThreadTypeFilter)?.label?.toLowerCase()}`}
                      size={1}
                      variant="surface"
                      class="search-input"
                      bind:textInput={searchInputElement}
                      on:keydown={(event) => {
                        if (event.key === "Escape") {
                          clearSearch();
                        }
                      }}
                      on:focus={() => {
                        isSearchActive = true;
                      }}
                      on:blur={() => {
                        if (!searchQuery.trim()) {
                          isSearchActive = false;
                        }
                      }}
                    >
                      <div class="search-field-container__icon" slot="iconLeft">
                        <SearchIcon />
                      </div>
                    </TextFieldAugment>
                    {#if searchQuery}
                      <div class="clear-search-button">
                        <TextTooltipAugment content="Clear search" nested={false}>
                          <IconButtonAugment
                            variant="ghost"
                            color="neutral"
                            size={1}
                            on:click={clearSearch}
                          >
                            <XMarkIcon />
                          </IconButtonAugment>
                        </TextTooltipAugment>
                      </div>
                    {/if}
                  </div>
                {/if}
                <div class="search-icon" class:is-active={isSearchActive}>
                  <TextTooltipAugment
                    content="Search threads"
                    nested={false}
                    hasPointerEvents={false}
                  >
                    <IconButtonAugment
                      variant="ghost"
                      color={searchQuery.trim() !== "" ? "accent" : "neutral"}
                      size={1}
                      on:click={toggleSearch}
                    >
                      <SearchIcon />
                    </IconButtonAugment>
                  </TextTooltipAugment>
                </div>
              </div>
            {/if}
          </button>
        </div>
        <div class="header-right">
          {#if overviewError && !effectiveHasError}
            <TextTooltipAugment
              content="Data may be outdated. Last refreshed {lastSuccessfulOverviewFetch > 0
                ? getRelativeTimeForStr(new Date(lastSuccessfulOverviewFetch).toISOString())
                : 'never'}"
              nested={false}
            >
              <div class="stale-indicator">
                <div class="stale-dot"></div>
              </div>
            </TextTooltipAugment>
          {/if}

          <div class="new-thread-dropdown">
            <NewThreadDropdown />
          </div>
        </div>
      </div>

      <!-- Main threads list - only show when expanded -->
      {#if isExpanded && !effectiveIsLoading && !effectiveIsEmpty}
        <div
          class="agents-list"
          bind:this={agentsListElement}
          bind:clientHeight={heightOfList}
          style="height: {agentsListHeight}; max-height: {agentsListMaxHeight};"
          use:overscrollToCloseMainList
        >
          <div class="agents-list-container__inner" bind:clientHeight={heightOfListContainer}>
            {#each displayedGroups as group (`${group.groupTitle}`)}
              <!-- Group header -->
              {#if group.groupTitle !== ConversationAge.New}
                <div class="thread-group-header" transition:slide|global={{ duration: 150 }}>
                  <div class="thread-group-header-content">
                    <TextAugment size={1} weight="medium" color="secondary"
                      >{group.groupTitle}</TextAugment
                    >
                    <div class="thread-group-options">
                      <DropdownMenuAugment.Root>
                        <DropdownMenuAugment.Trigger>
                          <IconButtonAugment
                            variant="ghost-block"
                            color="neutral"
                            size={1}
                            title="Group options"
                          >
                            <RegularEllipsisIcon />
                          </IconButtonAugment>
                        </DropdownMenuAugment.Trigger>
                        <DropdownMenuAugment.Content size={1} side="bottom" align="end">
                          <DropdownMenuAugment.Item
                            color="error"
                            onSelect={() => deleteAllThreadsInGroup(group.threads)}
                          >
                            <Trash slot="iconLeft" />
                            Delete {group.threads.length} thread{group.threads.length > 1
                              ? "s"
                              : ""}
                          </DropdownMenuAugment.Item>
                        </DropdownMenuAugment.Content>
                      </DropdownMenuAugment.Root>
                    </div>
                  </div>
                </div>
              {/if}

              <!-- Group threads -->
              {#each group.threads as thread, index (`${thread.id}-${thread.isPinned}-${index}`)}
                <ThreadsListRowItem
                  {thread}
                  {containerWidth}
                  isSelected={selectedThreadId === thread.id}
                  afterDelete={() => {
                    if (selectedThreadId !== thread.id) return;
                    const nextThread = group.threads[index + 1] || group.threads[index - 1];
                    if (nextThread) {
                      switchToThread(nextThread);
                    }
                  }}
                  onSelect={() => switchToThread(thread)}
                  onTogglePinned={(event) => toggleThreadPinned(thread, event)}
                  {isFromDifferentRepo}
                  {getRepoInfo}
                  enableShareService={$enableShareService}
                  {isExpanded}
                />
              {/each}
            {/each}
          </div>
        </div>
      {/if}

      <!-- Active Remote Agents Section (when collapsed and toggle is enabled) -->
      {#if recentRemoteAgentCount > 0}
        <div class="active-remote-agents-section" transition:fly={{ y: 5, duration: 150 }}>
          <!-- Header -->
          <div class="active-remote-agents-header">
            <button
              class="expand-toggle-button-container {$isRecentRemoteAgentsListExpanded
                ? 'expand-toggle-button-container--active'
                : ''}"
              on:click={toggleShowRemoteAgents}
              aria-label={$isRecentRemoteAgentsListExpanded
                ? "Hide recent remote agents"
                : "Show recent remote agents"}
              aria-expanded={$isRecentRemoteAgentsListExpanded}
              type="button"
            >
              <div
                class="expand-toggle-button {$isRecentRemoteAgentsListExpanded
                  ? 'expand-toggle-button--active'
                  : ''}"
              >
                <ChevronDown />
              </div>

              <CloudArrowUp class="remote-agent-icon" />

              <TextAugment size={1}>
                {recentRemoteAgentCount} recent remote agent{recentRemoteAgentCount === 1
                  ? ""
                  : "s"}
              </TextAugment>

              {#if runningRemoteAgentsCount > 0 || agentsWithChangesCount > 0}
                <div class="remote-agent-indicators">
                  {#if runningRemoteAgentsCount > 0}
                    <div
                      class="indicator running-indicator"
                      title="{runningRemoteAgentsCount} running agent{runningRemoteAgentsCount === 1
                        ? ''
                        : 's'}"
                    >
                      <div class="indicator-dot running-dot"></div>
                      <span class="indicator-count">{runningRemoteAgentsCount}</span>
                    </div>
                  {/if}
                  {#if agentsWithChangesCount > 0}
                    <div
                      class="indicator changes-indicator"
                      title="{agentsWithChangesCount} agent{agentsWithChangesCount === 1
                        ? ''
                        : 's'} with changes"
                    >
                      <div class="indicator-dot changes-dot"></div>
                      <span class="indicator-count">{agentsWithChangesCount}</span>
                    </div>
                  {/if}
                </div>
              {/if}

              {#if $isRecentRemoteAgentsListExpanded}
                <!-- show home panel button -->
                <div class="home-panel-button" transition:fly={{ y: -2, duration: 150 }}>
                  <TextTooltipAugment
                    content="Expand remote agents dashboard"
                    nested={false}
                    triggerOn={[TooltipTriggerOn.Hover]}
                    hasPointerEvents={false}
                  >
                    <IconButtonAugment
                      variant="ghost"
                      color={isHomePanelOpen ? "accent" : "neutral"}
                      size={1}
                      on:click={(event) => {
                        event.stopPropagation();
                        if (isHomePanelOpen) {
                          remoteAgentsModel.closeRemoteAgentHomePanel();
                        } else {
                          remoteAgentsModel.showRemoteAgentHomePanel();
                        }
                      }}
                    >
                      <RegularSidebarIcon />
                    </IconButtonAugment>
                  </TextTooltipAugment>
                </div>
              {/if}
            </button>
          </div>

          <!-- Active Remote Agents List -->
          <div
            class="active-remote-agents-list"
            bind:this={activeAgentsListElement}
            transition:slide={{ duration: 150 }}
            style="height: {activeAgentsListHeight}; max-height: {activeAgentsListMaxHeight};"
            use:overscrollToCloseActiveAgents
          >
            {#each recentRemoteAgents as thread, index (thread.id)}
              {#if $isRecentRemoteAgentsListExpanded}
                <ThreadsListRowItem
                  {thread}
                  {containerWidth}
                  isSelected={selectedThreadId === thread.id}
                  afterDelete={() => {
                    if (selectedThreadId !== thread.id) return;
                    const nextThread =
                      recentRemoteAgents[index + 1] || recentRemoteAgents[index - 1];
                    if (nextThread) {
                      switchToThread(nextThread);
                    }
                  }}
                  onSelect={() => switchToThread(thread)}
                  onTogglePinned={(event) => toggleThreadPinned(thread, event)}
                  {isFromDifferentRepo}
                  {getRepoInfo}
                  enableShareService={$enableShareService}
                  isExpanded={false}
                />
              {/if}
            {/each}
          </div>
        </div>
      {/if}
    </div>

    <!-- Loading and error states below the agent list -->
    {#if effectiveIsLoading}
      <div class="loading-state">
        <!-- Skeleton loading state -->
        {#each skeletonRows as _, i (i)}
          <div class="agent-row skeleton-row">
            <div class="agent-content">
              <div class="agent-details">
                <div class="agent-details--left">
                  <div class="agent-icon">
                    <div class="skeleton-dot"></div>
                  </div>
                  <div class="skeleton-text"></div>
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>
    {:else if effectiveHasError}
      <!-- Error state - only show full error state if we have no data -->
      {#if remoteAgents.length === 0}
        <div class="error-message">
          <ExclamationTriangle />
          <TextAugment size={1}>
            Failed to load Remote Agent threads. {overviewError}
          </TextAugment>
        </div>
      {:else}
        <!-- Show a non-disruptive error banner if we have data -->
        <div class="error-banner">
          <ExclamationTriangle />
          <TextAugment size={1} color="error">{overviewError}</TextAugment>
        </div>
      {/if}
    {:else if effectiveIsEmpty}
      <!-- Empty state -->
      <div class="empty-message">
        {#if searchQuery.trim() !== ""}
          <TextAugment size={1} color="secondary"
            >No threads with "{searchQuery}" in the title.</TextAugment
          >
        {:else}
          <TextAugment size={1} color="secondary">Create a new thread to get started.</TextAugment>
        {/if}
      </div>
    {/if}

    {#if (!searchQuery && isExpanded) || (!isExpanded && $isRecentRemoteAgentsListExpanded)}
      <div class="resize-handle-container">
        <TextTooltipAugment
          content={(isExpanded ? resizeState.isDragging : activeAgentsResizeState.isDragging)
            ? undefined
            : "Drag to resize. Click or Esc to collapse"}
          side="bottom"
          hasPointerEvents={false}
          offset={[0, 8]}
        >
          <button
            class="resize-handle"
            class:resize-handle--dragging={isExpanded
              ? resizeState.isDragging
              : activeAgentsResizeState.isDragging}
            class:resize-handle--expanded={isExpanded}
            on:mousedown={isExpanded ? startDrag : startActiveAgentsDrag}
            on:click={() => {
              if (isExpanded) {
                if (resizeState.hasDraggedThisClick) return;
                toggleExpanded();
              } else {
                if (activeAgentsResizeState.hasDraggedThisClick) return;
                toggleShowRemoteAgents();
              }
            }}
          >
            <div class="resize-handle-line">
              <div class="resize-handle-dash" />
            </div>
          </button>
        </TextTooltipAugment>
      </div>
    {/if}
  </div>
</div>

<style>
  .remote-agent-threads-container {
    margin-top: calc(0px - var(--ds-spacing-3));
    margin-left: calc(0px - var(--chat-padding));
    margin-right: calc(0px - var(--chat-padding));
    border-bottom: 1px solid var(--augment-border-color);
    background-color: var(--augment-window-background);
    width: 100dvw;
  }
  .remote-agent-threads {
    position: relative;
    width: 100%;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1px var(--ds-spacing-3) 3px 0;
  }

  .header-text {
    display: flex;
    align-items: center;
    padding: 4px 4px 4px 0;
  }

  .header-text :global(.chat-btn) {
    color: var(--ds-color-neutral-9);
  }
  .header-left {
    display: flex;
    align-items: center;
    appearance: none;
    flex: 1;
    background: none;
    border: none;
    color: inherit;
  }

  .remote-agent-threads :global(.expand-toggle-button-container) {
    flex: 1;
    min-width: 0;
    width: 100%;
  }
  .remote-agent-threads :global(.expand-toggle-button-container:focus) {
    outline: none;
  }

  .header-right {
    display: flex;
    align-items: center;
    margin-left: auto;
    gap: var(--ds-spacing-1);
  }

  .active-remote-agents-header {
    display: flex;
    padding-right: var(--ds-spacing-3);
    align-items: center;
    color: var(--ds-color-neutral-10);
    /* to account for margin underneath threads header */
    margin-top: -3px;
    transition: margin-top 0.2s ease;
  }
  .is-expanded .active-remote-agents-header {
    margin-top: 0;
  }

  .active-remote-agents-header :global(.expand-toggle-button-container) {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: var(--ds-color-neutral-11);
    border-radius: 4px;
    transition: background-color 0.2s ease;
    text-align: left;
    width: 100%;
    justify-content: flex-start;
  }

  .active-remote-agents-header :global(.expand-toggle-button-container:hover) {
    background-color: var(--ds-color-panel-hover);
    color: var(--ds-color-neutral-11);
  }

  .home-panel-button {
    margin-left: auto;
    margin-right: 5px;
  }

  .active-remote-agents-list {
    padding-left: 14.5px;
    overflow: auto;
    position: relative;
    max-height: 0;
    transition: max-height 0.2s ease;
  }

  .action-button {
    transition: all 0.2s ease-out;
  }

  .agents-list-container {
    display: flex;
    width: 100%;
    flex-direction: column;
  }

  .agents-list {
    display: flex;
    flex-direction: column;
    flex: none;
    position: relative;
    z-index: var(--z-thread-list);
    width: 100%;
    /* to account for margin underneath threads header */
    margin-top: -3px;
    overflow: auto;
    /* Ensure scrollbar is always visible when content overflows */
    scrollbar-width: thin;
    scrollbar-color: var(--augment-scrollbar-color) transparent;
    max-height: 0;
    transition: max-height 0.2s ease;
  }

  /* Webkit scrollbar styling for consistency */
  .agents-list::-webkit-scrollbar {
    width: 8px;
  }

  .agents-list::-webkit-scrollbar-track {
    background: transparent;
  }

  .agents-list::-webkit-scrollbar-thumb {
    background-color: var(--augment-scrollbar-color);
    border-radius: 4px;
  }

  .agents-list::-webkit-scrollbar-thumb:hover {
    background-color: var(--vscode-scrollbarSlider-hoverBackground, var(--augment-scrollbar-color));
  }

  .resize-handle-container :global(.l-tooltip-trigger) {
    width: 100%;
  }
  .resize-handle {
    position: absolute;
    bottom: -5.5px;
    left: 0;
    right: 0;
    height: 11px;
    cursor: ns-resize;
    display: flex;
    padding: 0;
    align-items: center;
    justify-content: center;
    background-color: var(--ds-color-panel);
    z-index: 20;
    appearance: none;
    border: none;
    color: inherit;
  }
  .resize-handle-line {
    width: 100%;
    height: 2px;
    background-color: var(--ds-color-neutral-7);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: background-color 0.2s ease;
  }

  .resize-handle-dash {
    width: 20px;
    height: 3px;
    background-color: var(--ds-color-neutral-9);
    border-radius: 1px;
    position: absolute;
    top: -0.5px;
    transition: background-color 0.2s ease;
  }

  .resize-handle:hover {
    background-color: var(--ds-color-panel-hover);
  }
  .resize-handle:hover .resize-handle-line {
    background-color: var(--ds-color-neutral-9);
  }
  .resize-handle:hover .resize-handle-dash {
    background-color: var(--ds-color-neutral-11);
  }
  .agents-list-container.is-dragging {
    user-select: none;
    cursor: ns-resize;
    pointer-events: none;
  }
  .agents-list-container.is-dragging .agents-list,
  .agents-list-container.is-dragging .active-remote-agents-list {
    transition: none;
    max-height: none !important;
  }

  /* Disable pointer events on child elements while dragging */
  .agents-list-container.is-dragging .agents-list * {
    pointer-events: none;
  }
  .resize-handle--dragging {
    background-color: var(--ds-color-neutral-5);
  }

  .agent-row {
    width: 100%;
    transition: all 0.2s ease;
    flex: none;
    height: 25px;
    padding: 0 var(--ds-spacing-3);
    cursor: pointer;
    max-width: 100%;
    box-sizing: border-box;
  }

  .agent-row:hover {
    color: var(--ds-color-neutral-12);
  }

  .agent-row :global(.c-agent-header__delete-btn) {
    opacity: 0;
    width: 0;
    transition: all 0.03s ease;
  }

  .agent-row:hover :global(.c-agent-header__delete-btn) {
    opacity: 1;
    width: 22px;
  }
  .agent-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 100%;
  }

  .agent-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    min-width: 0;
  }

  .agent-details--left {
    display: inline-flex;
    gap: 6px;
    flex: 1;
    min-width: 0;
  }

  @media (max-width: 400px) {
    .agent-details {
      max-width: calc(100% - 25px);
    }
  }

  @keyframes hide-scroll {
    from,
    to {
      overflow: hidden;
    }
  }
  .agents-list-container.is-expanded .agents-list {
    background: none;
    padding-left: 0;
    animation: hide-scroll 0.2s backwards;
    max-height: calc(80vh - 10rem);
    overflow: auto;
  }

  .remote-agent-threads :global(.expand-toggle-button-container) {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: var(--ds-color-neutral-11);
    border-radius: 4px;
    transition: background-color 0.2s ease;
    text-align: left;
    width: 100%;
    flex: 1;
  }

  .remote-agent-threads :global(.expand-toggle-button-container:hover) {
    background-color: var(--ds-color-panel-hover);
    color: var(--ds-color-neutral-11);
  }

  .remote-agent-threads :global(.expand-toggle-button) {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 1px 4px 4px;
    margin-left: 3px;
    border-radius: 4px;
  }

  .remote-agent-threads :global(.expand-toggle-button svg) {
    width: 14px;
    height: 14px;
    transform: rotate(-90deg);
    transition: transform 0.2s ease;
  }

  .remote-agent-threads :global(.expand-toggle-button--active svg) {
    transform: rotate(0deg);
  }

  /* Search styles */
  .search-container {
    position: relative;
    min-width: 25px;
    max-width: 25px;
    width: 100%;
    margin-left: var(--ds-spacing-1);
  }

  .search-field-container {
    width: 100%;
    min-width: 25px;
    max-width: 25px;
  }
  .search-container.is-active .search-field-container {
    min-width: 150px;
    max-width: 150px;
  }

  .search-container.is-active {
    max-width: 150px;
  }

  .search-container :global(.search-field-container__icon) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 14px;
    margin-left: 2px;
    margin-right: 2px;
  }

  .search-icon {
    position: absolute;
    left: 4px;
    top: 50%;
    transform: translateY(-50%);
  }
  .search-icon.is-active {
    opacity: 0;
    pointer-events: none;
  }

  .search-field-container {
    position: relative;
    width: 100%;
  }

  .search-icon :global(svg) {
    width: 14px;
    height: 14px;
  }

  .clear-search-button {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :global(.search-input input) {
    padding-left: 30px !important;
  }

  /* Indicator styles */
  .stale-indicator {
    display: flex;
    align-items: center;
    margin-left: 6px;
  }

  .stale-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--ds-color-warning-9);
  }

  /* Skeleton loading styles */
  .skeleton-row {
    pointer-events: none;
  }

  .skeleton-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--ds-color-neutral-8);
    animation: pulse 1.5s infinite;
  }

  .skeleton-text {
    height: 14px;
    width: 90%;
    background-color: var(--ds-color-neutral-8);
    border-radius: 3px;
    animation: pulse 1.5s infinite;
  }
  .skeleton-row:first-child .skeleton-text {
    width: 70%;
  }
  .skeleton-row:nth-child(2) .skeleton-text {
    width: 60%;
  }
  .skeleton-row:nth-child(3) .skeleton-text {
    width: 90%;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 0.3;
    }
    100% {
      opacity: 0.6;
    }
  }

  /* Loading, error and empty state styles */
  .empty-message {
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: italic;
    padding: var(--ds-spacing-3) var(--ds-spacing-2) var(--ds-spacing-4);
    text-align: center;
    color: var(--ds-color-neutral-11);
    min-height: 30px;
  }

  .error-message {
    padding: var(--ds-spacing-2);
    display: flex;
    gap: var(--ds-spacing-2);
    color: var(--ds-color-error-11);
  }

  .error-banner {
    padding: var(--ds-spacing-2) var(--ds-spacing-3);
    display: flex;
    gap: var(--ds-spacing-3);
    align-items: center;
    background-color: var(--ds-color-error-1);
  }

  .error-message :global(svg),
  .error-banner :global(svg) {
    margin-top: 2px;
    flex: none;
    fill: currentColor;
    width: 13px;
  }

  /* Make skeleton rows in the loading state match the agent rows */
  .loading-state .agent-row {
    margin-bottom: var(--ds-spacing-2);
  }

  /* Thread group styles */
  .thread-group-header {
    padding: 0 var(--ds-spacing-3);
    border-bottom: 1px solid var(--ds-color-border);
    flex: none;
  }

  .thread-group-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .thread-group-options {
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  .thread-group-header:hover .thread-group-options,
  .thread-group-header:focus-within .thread-group-options {
    opacity: 1;
  }

  .thread-type-filter {
    margin-left: var(--ds-spacing-1);
  }

  /* Remote agent indicators styles */
  .remote-agent-indicators {
    display: flex;
    align-items: center;
    gap: var(--ds-spacing-1);
    margin-left: var(--ds-spacing-2);
  }

  .indicator {
    display: flex;
    align-items: center;
    gap: 2px;
    padding: 2px 4px;
    border-radius: 8px;
    background-color: var(--ds-color-panel);
    border: 1px solid var(--ds-color-border);
    font-size: 11px;
    font-weight: 500;
    cursor: default;
  }

  .indicator-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    flex-shrink: 0;
  }

  .indicator-count {
    color: var(--ds-color-neutral-11);
    font-size: 10px;
    font-weight: 600;
    line-height: 1;
  }

  /* Running agents indicator */
  .running-indicator {
    background-color: var(--ds-color-success-2);
    border-color: var(--ds-color-success-6);
  }

  .running-dot {
    background-color: var(--ds-color-accent-9);
    animation: pulse-running 2s infinite;
  }

  .running-indicator .indicator-count {
    color: var(--ds-color-accent-9);
  }

  /* Agents with changes indicator */
  .changes-indicator {
    background-color: var(--ds-color-success-2);
    border-color: var(--ds-color-success-6);
  }

  .changes-dot {
    background-color: var(--ds-color-success-9);
  }

  .changes-indicator .indicator-count {
    color: var(--ds-color-success-10);
  }

  @keyframes pulse-running {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .remote-agent-threads :global(.remote-agent-icon) {
    width: 14px;
  }
  .active-remote-agents-section {
    position: relative;
  }

  .is-expanded .active-remote-agents-section:before {
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    right: 0;
    height: 1px;
    background-color: var(--augment-border-color);
    opacity: 0.2;
  }

  @media (max-height: 550px) {
    .active-remote-agents-section {
      display: none;
    }
  }
</style>
