<script lang="ts">
  import * as rive from "@rive-app/canvas";
  import TextTooltipAugment from "$common-webviews/src/design-system/components/TextTooltipAugment.svelte";
  import ContextBadge from "./ContextBadge.svelte";
  import { memoryStore } from "../../models/memory-store";
  import darkMemoriesSrc from "$common-webviews/src/design-system/icons/augment/rive/memories-dark.riv?url";
  import lightMemoriesSrc from "$common-webviews/src/design-system/icons/augment/rive/memories-light.riv?url";
  import { onDestroy, onMount } from "svelte";
  import { attachAugmentThemeObserver } from "$common-webviews/src/common/hosts/user-themes/theme-store";
  import {
    getCategory,
    UserThemeCategory,
  } from "$common-webviews/src/common/hosts/user-themes/augment-theme-attributes";

  let memoriesSrc = lightMemoriesSrc;
  let canvas: HTMLCanvasElement | undefined = undefined;
  let r: rive.Rive | undefined = undefined;

  function updateRive() {
    // Cleanup old rive
    if (r) {
      r?.cleanup();
      r = undefined;
    }

    // Create new rive
    if (canvas && !r) {
      r = new rive.Rive({
        src: memoriesSrc,
        canvas,
        autoplay: false,
        onLoad: () => {
          r?.resizeDrawingSurfaceToCanvas();
          r?.play();
          r?.stop();
        },
      });
    }
  }
  // On initial canvas load
  $: canvas && !r && updateRive();

  function onThemeUpdate(category: UserThemeCategory | undefined) {
    memoriesSrc = category === UserThemeCategory.dark ? darkMemoriesSrc : lightMemoriesSrc;
    updateRive();
  }

  onMount(() => {
    attachAugmentThemeObserver(onThemeUpdate);
    onThemeUpdate(getCategory());
  });

  function startAnimation() {
    r?.reset();
    r?.play();

    // After 2.5 seconds, stop the animation
    setTimeout(stopAnimation, 2500);
  }

  function stopAnimation() {
    r?.reset();
  }

  $: $memoryStore?.timestamp && startAnimation();

  onDestroy(() => {
    r?.cleanup();
  });
</script>

<ContextBadge>
  <!-- We want to render at a larger size to make sure it's not blurry, but we scale it down
   in the canvas style tag to the actual size -->
  <canvas
    slot="leftIcon"
    bind:this={canvas}
    width="32"
    height="32"
    style="width: 14px; height: 14px;"
  ></canvas>
  <TextTooltipAugment
    content="Information that Agent remembered from previous interactions (click to open)"
  >
    <span on:click on:keydown role="button" tabindex="0">Augment Memories</span>
  </TextTooltipAugment>
</ContextBadge>
