@import "./_z-index.css";

:root {
  /* Fonts */
  --fallback-font-family: system-ui, sans-serif;
  --augment-font-family: var(
    --vscode-font-family,
    var(--intellij-panel-font-family, var(--fallback-font-family))
  );
  --augment-monospace-font-family: var(
    --vscode-editor-font-family,
    var(--intellij-editorPane-font-family, monospace),
  );
  --augment-font-size: var(--vscode-font-size, var(--intellij-ui-font-size, 0.9rem));

  /* Background and foreground colors */
  --augment-foreground: var(--vscode-foreground, var(--intellij-panel-foreground));
  --augment-window-background: var(
    --vscode-sideBar-background,
    var(--intellij-tree-background, transparent)
  );

  /* Scrollbar colors */
  --augment-scrollbar-color: var(
    --vscode-scrollbarSlider-background,
    /* For IntelliJ, there are a few thumb colors themes may or may not set, ordered by preference */
      var(
        --intellij-scrollBar-transparent-thumbColor,
        var(--intellij-scrollBar-thumbColor, var(--intellij-scrollBar-thumb)),
      )
  );

  /* Border Styles */
  --augment-border-color: var(--ds-color-neutral-a6);
  --augment-focus-border-color: var(--ds-color-accent-a6);
  --augment-border: 1px solid var(--augment-border-color);
  --augment-border-radius: var(--ds-radius-2);

  /* Text Colors */

  --augment-text-color: var(--ds-color-secondary-a12);
  --augment-text-color-secondary: var(--ds-color-secondary-a10);
  --augment-text-color-tertiary: var(--ds-color-secondary-a7);

  /* Sizing */
  /* outside of DS because we want to match the IDE here */
  --augment-tree-list-item-height: 22px;
}

.dark {
  --augment-text-color: var(--ds-default-white);
  --augment-text-color-secondary: var(--ds-color-secondary-a9);
  --augment-text-color-tertiary: var(--ds-color-secondary-a6);
  --augment-border-color: var(--ds-color-neutral-a9);
}
