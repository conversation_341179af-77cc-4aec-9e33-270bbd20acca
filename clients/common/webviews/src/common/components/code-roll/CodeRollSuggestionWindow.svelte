<script lang="ts">
  import { renderViewZone } from "$common-webviews/src/apps/diff-view/models/monaco-render-utils";
  import { ChangeType, type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import { editor as Editor } from "monaco-editor";
  import { stop } from "./code-roll-util";
  import type { ActionConfig, OnCodeAction } from "./types";
  import { onKey } from "../../utils/keypress";
  import { getCodeRollSelection, getCurrentSuggestion } from "./code-roll-context";
  import ActionButtons from "./ActionButtons.svelte";
  import { scrollIntoView } from "../suggestion-tree/scroll-view-util";
  import { selectionState, styleForSelection } from "./suggestion-util";
  import { areSameSuggestion } from "../suggestion-tree/navigation-utils";
  import PencilIcon from "../pencil-icons/PencilIcon.svelte";

  export let diffEditor: Editor.IStandaloneDiffEditor;
  export let suggestion: IEditSuggestion;
  export let codeActions: ActionConfig[] = [];
  export let onCodeAction: OnCodeAction;
  export let afterLineNumber: number;
  export let lineCount: number = 0;
  export let scrollContainer: HTMLElement | undefined;

  let top = 0;
  const ctx = getCodeRollSelection();
  $: state = selectionState(suggestion, $ctx, false);
  $: heightInPx = diffEditor.getModifiedEditor().getOption(Editor.EditorOption.lineHeight);
  $: windowHeight = lineCount * heightInPx;
  $: isInsertionOrModification =
    suggestion.changeType === ChangeType.insertion ||
    suggestion.changeType === ChangeType.modification;
  $: lineEndNumber = isInsertionOrModification ? afterLineNumber + lineCount : afterLineNumber;
  $: leftAlignmentPx = lineEndNumber > 999 ? 0 : 5;
</script>

<div
  class="c-code-roll-suggestion-window__view-zone"
  style="--augment-code-roll-left-alignment:{leftAlignmentPx}px"
  use:renderViewZone={{
    editor: diffEditor,
    afterLineNumber,
    heightInPx,
    onDomNodeTop(domTop) {
      top = domTop;
    },
  }}
></div>

<div
  style="
    --augment-code-roll-left-alignment:{leftAlignmentPx}px;
    --augment-code-roll-suggestion-window-line-height:{heightInPx}px;
    top:{top}px;
  "
  class="c-code-roll-suggestion-window"
  data-result-id={`${suggestion.result.suggestionId}:${suggestion.requestId}`}
>
  <div
    class="c-code-roll-suggestion-window__item"
    style={styleForSelection(selectionState(suggestion, $ctx, false))}
    use:scrollIntoView={{
      scrollContainer,
      doScroll: areSameSuggestion(suggestion, getCurrentSuggestion($ctx)),
      scrollIntoView: { behavior: "smooth", block: "center" },
      useSmartBlockAlignment: true,
    }}
  >
    <div class="c-code-roll-suggestion-window__item-title" style="height:{heightInPx}px">
      <button
        class="c-code-roll-suggestion-window__item-title-text"
        tabindex="0"
        on:keydown={onKey("Enter", stop(onCodeAction, "active", suggestion))}
        on:click={stop(onCodeAction, "active", suggestion)}
      >
        <PencilIcon mask={state !== "none"} {suggestion} />
        {suggestion.result.changeDescription}
      </button>
      <ActionButtons compact actions={codeActions} onAction={onCodeAction} value={suggestion} />
    </div>
    <div class="c-code-roll-suggestion-window__border">
      <div class="c-code-roll-suggestion-window__window" style="height:{windowHeight}px;">
        <!--this is where the diff will show-->
      </div>
    </div>
  </div>
</div>

<style>
  .c-code-roll-suggestion-window {
    --augment-code-roll-item-background: #3f3f3f;
    --augment-code-roll-item-radius: 5px;
    --augment-code-roll-item-action-button-size: 16px;
    --augment-code-roll-button-padding: 0;
    --augment-code-roll-button-background: transparent;
    --augment-code-roll-button-radius: 0;
    --base-btn-color: var(--augment-code-roll-selection-color);
    --vscode-scrollbarSlider-background: var(--augment-window-background);
    --vscode-scrollbarSlider-hoverBackground: var(--augment-window-background);
    position: absolute;
    display: flex;
    flex: 1;
    width: calc(100% - var(--augment-code-roll-left-alignment, 0) - 5px);
    flex-direction: row;
    flex-wrap: nowrap;
    left: var(--augment-code-roll-left-alignment, 5px);
    margin-top: 1px;
    pointer-events: none;
  }
  .c-code-roll-suggestion-window__item :global(.c-action-buttons) {
    margin-right: -2px;
  }
  .c-code-roll-suggestion-window,
  .c-code-roll-suggestion-window__window {
    background-color: transparent;
  }
  .c-code-roll-suggestion-window__window {
    border: 1px solid var(--user-theme-panel-background);
    border-radius: 3px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  .c-code-roll-suggestion-window button:focus {
    padding-top: 1px;
  }

  .c-code-roll-suggestion-window__item {
    display: flex;
    flex-direction: column;
    padding: 0;
    background-color: transparent;
    color: var(--augment-code-roll-selection-color);
    border: none;
    width: 100%;
    margin-top: -1.5px;
  }
  .c-code-roll-suggestion-window__border {
    border-top: 0;
    border-radius: var(--augment-code-roll-item-radius);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    border: 2px solid var(--augment-code-roll-selection-border);
  }
  .c-code-roll-suggestion-window__item-title {
    min-height: var(--augment-code-roll-suggestion-window-line-height);
    max-height: var(--augment-code-roll-suggestion-window-line-height);
    display: flex;
    flex: 1;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    color: inherit;
    background: var(--augment-code-roll-selection-background);
    padding: 1px 2px 0;
    border: none;
    border-radius: var(--augment-code-roll-item-radius);
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    margin-bottom: -1px;
    pointer-events: all;
  }
  .c-code-roll-suggestion-window__item-title-text {
    display: flex;
    align-items: center;
    gap: 8px;
    color: inherit;
    background-color: transparent;
    outline: none;
    border: none;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    font-size: 12px;
    text-align: left;
    flex: 1;
  }
  .c-code-roll-suggestion-window__item-title
    :global(.c-action-buttons)
    :global(.c-icon-btn)
    :global(.c-base-btn) {
    --base-btn-color: var(--augment-code-roll-selection-color);
  }
</style>
