<script lang="ts">
  import { type IEditSuggestion } from "$vscode/src/next-edit/next-edit-types";
  import Detail from "../detail/Detail.svelte";
  import { isSameFile, noop } from "./code-roll-util";
  import type { ActionConfig, FileReader, OnCodeAction, OnFileAction } from "./types";
  import { type IQualifiedPathName } from "@augment-internal/sidecar-libs/src/workspace/workspace-types";
  import CodeRollItemHeader from "./CodeRollItemHeader.svelte";
  import CodeRollItemDiff from "./CodeRollItemDiff.svelte";
  import isEqual from "lodash/isEqual";

  export let suggestions: IEditSuggestion[];
  export let filepath: IQualifiedPathName;
  export let readFile: FileReader;
  export let onFileAction: OnFileAction = noop;
  export let onCodeAction: OnCodeAction = noop;
  export let fileActions: ActionConfig[] = [];
  export let codeActions: ActionConfig[] = [];
  export let expandable = true;
  export let scrollContainer: HTMLElement | undefined = undefined;

  let originalCode = "";
  let loaded = false;
  let lastSuggestions: IEditSuggestion[] | null = null;
  let lastFilepath: IQualifiedPathName | null = null;

  async function loadCode(filepath: IQualifiedPathName) {
    originalCode = await readFile(filepath);
    loaded = true;
  }

  $: {
    if (
      !isEqual(suggestions, lastSuggestions) ||
      lastFilepath === null ||
      !isSameFile(filepath, lastFilepath)
    ) {
      if (lastFilepath === null || !isSameFile(filepath, lastFilepath)) {
        // when we are switching files, lets show a loading spinner,
        // when we are not switching files, we'll just update the code when
        // we get it without a spinner.
        loaded = false;
      }

      lastFilepath = filepath;
      lastSuggestions = suggestions;
      loadCode(filepath);
    }
  }
</script>

<Detail open={true} class="c-code-roll-item " {expandable}>
  <CodeRollItemHeader {filepath} {onFileAction} {fileActions} slot="summary" {suggestions} />
  <CodeRollItemDiff
    {codeActions}
    {loaded}
    {suggestions}
    {filepath}
    {originalCode}
    {onCodeAction}
    {scrollContainer}
  />
</Detail>

<style>
  :global(.c-code-roll-item) {
    display: flex;
    flex-direction: column;
    padding: 0;
    width: 100%;
    border-radius: var(--augment-code-roll-item-radius, 5px);
    border: 1px solid var(--augment-code-roll-item-border, #ffffff26);
    background-color: var(--augment-code-roll-item-background);
  }
  :global(.c-code-roll-item__loading) {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    min-height: 20ch;
    align-self: center;
  }

  :global(.c-code-roll-item) > :global(.c-detail__summary) {
    padding: 4px 10px;
    border-width: 0;
    border-radius: calc(var(--augment-code-roll-item-radius, 5px) - 1px)
      /* needed so our sharp corners dont break the parents round corners*/
      calc(var(--augment-code-roll-item-radius, 5px) - 1px) 0 0;
    border-bottom: 1px solid var(--augment-code-roll-item-border, #ffffff26);
    transition-delay: 0.3s;
    transition: border-radius 0.3s ease;
  }
  :global(.c-code-roll-item) > :global(.c-detail__summary):focus,
  :global(.c-code-roll-item) > :global(.c-detail__summary):focus-within,
  :global(.c-code-roll-item) > :global(.c-detail__summary):focus-visible {
    background-color: var(--user-theme-panel-background) !important;
    --parent-bg-color: var(--user-theme-panel-background) !important;
  }
  :global(.c-code-roll-item.c-detail__rotate) > :global(.c-detail__summary) {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
  :global(.c-code-roll-item) > :global(.c-detail__summary) {
    flex: 1;
  }
</style>
