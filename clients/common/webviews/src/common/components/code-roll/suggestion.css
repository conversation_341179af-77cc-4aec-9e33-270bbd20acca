:root {
  --augment-code-roll-item-background-active: var(--ds-color-accent-10);
  --augment-code-roll-item-color-active: var(--ds-color-neutral-12);

  /* Unfortunately we must use color-mix here because we want an opaque color, it means we have hardcoded the makeshift "alpha" value */
  --augment-code-roll-item-background-select: color-mix(
    in srgb,
    rgb(from var(--ds-color-neutral-a4) r g b / 1) 10%,
    var(--user-theme-panel-background)
  );
  --augment-code-roll-item-color-select: var(--ds-color-neutral-12);

  --augment-code-roll-item-background-next: color-mix(
    in srgb,
    rgb(from var(--ds-color-neutral-a4) r g b / 1) 10%,
    var(--user-theme-panel-background)
  );
  --augment-code-roll-item-color-next: var(--ds-color-neutral-12);

  --augment-code-roll-item-background-none: var(--user-theme-panel-background);
  --augment-code-roll-item-color-none: var(--ds-color-neutral-12);
}

html[data-augment-theme-category="light"]:root {
  --augment-code-roll-item-color-active: var(--ds-color-neutral-1);
}
