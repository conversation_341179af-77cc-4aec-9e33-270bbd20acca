<script lang="ts" context="module">
  import type { NavigationItem, Props, WithComponent } from "./navigation-types";
  import { isWithComponent } from "./navigation-types";
  import type { ComponentType, SvelteComponent } from "svelte";
  /**
   * Helper function to create a navigation items, with correct types.
   * @param name
   * @param description
   * @param icon
   * @param id
   * @param component
   * @param props
   */
  export function createNavigationItem<T extends Props | undefined>(
    name: string,
    description: string,
    icon: ComponentType<SvelteComponent>,
    id: string,
    component?: T extends Props ? ComponentType<SvelteComponent<Props>> : undefined,
    props?: T,
  ): NavigationItem<T> {
    if (component !== undefined) {
      return {
        name,
        description,
        icon,
        id,
        component,
        props,
      } as any;
    }
    return {
      name,
      description,
      icon,
      id,
    } as any;
  }
</script>

<script lang="ts" generics="T extends Props | undefined = undefined">
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import Toolbox from "$common-webviews/src/design-system/icons/augment/toolbox.svelte";
  import Drawer from "$common-webviews/src/common/components/drawer/Drawer.svelte";
  import NavigationContent from "./NavigationContent.svelte";
  import { scrollTo } from "./scrollTo";

  export let group = "Workspace Settings";
  export let items: (NavigationItem<T> | undefined)[] = [];
  export let item: NavigationItem<T> | undefined = undefined;
  export let mode: "tree" | "flat" = "tree";
  export let selectedId: string | undefined = undefined;
  export let onNavigationChangeItem = (_selectedId: string | undefined) => {};
  export let showButton = true;

  $: {
    if (selectedId) {
      item = items.find((i) => i?.id === selectedId);
    } else {
      selectedId = item?.id;
    }
  }
  let clazz = "";
  export { clazz as class };

  let toolMap: Map<string, NavigationItem<T>[]> = new Map();
  $: {
    toolMap = items.reduce((acc, tool) => {
      if (!tool) return acc;
      const g = tool.group ?? group;
      const groupItems = acc.get(g) ?? [];
      groupItems.push(tool);
      acc.set(g, groupItems);
      return acc;
    }, new Map<string, NavigationItem<T>[]>());
  }

  $: {
    if (!item) {
      item = items[0];
    }
  }
  function click(i: NavigationItem<T>) {
    item = i;
    selectedId = i?.id;
  }
  function isRenderable(
    item: { id: string } | undefined,
    mode: "tree" | "flat",
    selectedId?: string,
  ): item is WithComponent<any> {
    if (mode === "tree") {
      return item?.id === selectedId;
    }
    return true;
  }
  $: {
    onNavigationChangeItem(selectedId);
  }
</script>

<div class="c-navigation c-navigation--mode__{mode} {clazz}">
  {#if mode === "tree"}
    <Drawer
      initialWidth={200}
      expandedMinWidth={150}
      columnLayoutThreshold={0}
      {showButton}
      minimized={false}
    >
      <nav class="c-navigation__nav" slot="left">
        {#key selectedId}
          {#each toolMap as [label, groupItems]}
            <div class="c-navigation__group">
              <slot name="group" {label} {mode}>
                <div class="c-navigation__head">
                  <Toolbox />
                  <TextAugment size={2} color="primary">{label}</TextAugment>
                </div>
              </slot>
              <div class="c-navigation__items">
                {#each groupItems as t}<button
                    class="c-navigation__item"
                    class:is-active={t.id === selectedId}
                    on:click={() => click(t)}
                  >
                    <TextAugment size={2} weight="regular" color="primary">
                      <span class="c-navigation__head-icon">
                        <svelte:component this={t.icon} />
                      </span>
                      {t.name}
                    </TextAugment>
                  </button>
                {/each}
              </div>
            </div>
          {/each}
        {/key}
      </nav>
      <NavigationContent {item} slot="right">
        <slot name="header" slot="header" {item} {selectedId} />
        <slot name="content" slot="content" {item} isSelected={item?.id === selectedId}>
          {#if isWithComponent(item) && isRenderable(item, mode, selectedId)}
            <svelte:component this={item.component} {...item.props} />
          {/if}
        </slot>
      </NavigationContent>
    </Drawer>
  {:else}
    <div class="c-navigation__flat">
      <slot name="header" {item} />

      {#each toolMap as [label, groupItems]}
        <div class="c-navigation__head">
          <slot name="group" {label} {mode}>
            <TextAugment color="secondary" size={2} weight="medium">
              <span class="c-navigation__head-icon"><Toolbox /></span>
              <span>{label}</span>
            </TextAugment>
          </slot>
        </div>

        {#each groupItems as i}
          <span
            use:scrollTo={{
              scrollTo: mode === "flat" && i.id === selectedId,
              delay: 300,
              options: { behavior: "smooth" },
            }}
          >
            <NavigationContent item={i}>
              <slot name="content" slot="content" item={i} />
            </NavigationContent>
          </span>
        {/each}
      {/each}
    </div>
  {/if}
</div>

<style>
  .c-navigation {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-4);
    width: 100%;

    &.c-navigation--mode__flat {
      flex-direction: column;
      justify-content: center;
    }
  }
  .c-navigation__flat {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-4);
    width: 100%;
    max-width: 600px;
    align-self: center;
  }
  .c-navigation > :global(.c-drawer.is-hidden .c-navigation__content) {
    max-width: 100%;
    outline: 2px solid pink;
    justify-content: center;
    overflow: auto;
    height: 100%;
  }

  .c-navigation.c-navigation--mode__flat :global(.c-navigation__content) {
    padding: var(--ds-spacing-2) 0;
  }
  .c-navigation.c-navigation--mode__flat {
    gap: 0;
    justify-content: center;
    padding: 0 var(--ds-spacing-4);
    width: 100%;
    max-height: 100vh;
    overflow: auto;
    justify-self: center;
  }
  .c-navigation.c-navigation--mode__flat .c-navigation__head {
    align-self: flex-start;
    width: 100%;
    padding: var(--ds-spacing-4) 0 0 0;
  }

  .c-navigation__nav {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-4);
    flex: 1;
    overflow: auto;
    height: 100%;
    background-color: var(--user-theme-panel-background);
  }
  .c-navigation__head {
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-2);
    padding: var(--ds-spacing-4);
    flex: 0;
    align-items: center;
  }

  .c-navigation__item {
    all: unset;
    display: flex;
    flex-direction: row;
    gap: var(--ds-spacing-2);
    align-items: center;
    padding: var(--ds-spacing-2);
    align-items: center;
    border: none;
    background: none;
    cursor: pointer;
    font-size: inherit;
    padding-left: var(--ds-spacing-6);
    &:hover {
      opacity: 0.5;
    }
    &.is-active {
      background-color: var(--ds-color-accent-a5);
    }
  }

  .c-navigation__items {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }
</style>
