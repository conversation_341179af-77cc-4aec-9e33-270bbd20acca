<!--
This details component is a wrapper around the HTML details element. It provides a
custom animation for the content of the details element.  It animates the height of the
content and the chevron, while also pinning the content to the bottom of the details element.

-->
<script lang="ts">
  import VsCodeCodicon from "../vscode/VSCodeCodicon.svelte";
  import { animateDetails } from "./animate-details";
  export let open: boolean | undefined = true;
  export let duration: number | KeyframeAnimationOptions = 300;
  export let expandable = true;
  export let onChangeOpen: undefined | ((open: boolean) => void) = undefined;
  let clazz = "";
  export { clazz as class };
  const details = typeof duration === "number" ? { duration } : duration;
  let rotate = open;
  //bind rotate to open.
  $: rotate = open;

  const onEnd = (name: string) => () => {
    rotate = name !== "close";
    onChangeOpen?.(name === "open");
  };

  const onStart = (name: string) => () => {
    rotate = name === "open";
  };
</script>

{#if expandable}
  <details
    {...$$restProps}
    bind:open
    style="--au-detail-duration: {details.duration}ms"
    use:animateDetails={details}
    on:close-start={onStart("close")}
    on:open-start={onStart("open")}
    on:close-end={onEnd("close")}
    on:open-end={onEnd("open")}
    class:c-detail__rotate={rotate}
    class="c-detail {clazz}"
  >
    <summary class="c-detail__summary">
      <VsCodeCodicon icon="chevron-down" class="c-detail__chevron" />
      <slot name="summary" />
    </summary>
    <div class="c-detail__content">
      <slot />
    </div>
  </details>
{:else}
  <div {...$$restProps} class="c-detail {clazz}">
    <div class="c-detail__summary">
      <slot name="summary" />
    </div>
    <div class="c-detail__content">
      <slot />
    </div>
  </div>
{/if}

<style>
  .c-detail__content {
    overflow: hidden;
    display: flex;
    align-items: flex-end;
  }
  .c-detail__summary:focus-within,
  .c-detail__summary:focus,
  .c-detail__summary:focus-visible {
    outline: none;
    background-color: var(--ds-color-neutral-6);
    --parent-bg-color: var(--ds-color-neutral-6);
  }

  .c-detail__summary {
    list-style: none;
    display: flex;
    flex-direction: row;
    flex: 1;
    margin: 0;
    cursor: pointer;
    align-items: center;
    gap: 0.25em;
    background-color: var(--user-theme-panel-background);
    --parent-bg-color: var(--user-theme-panel-background);
  }
  .c-detail__summary > :global(.c-detail__chevron):before {
    transition: transform var(--au-detail-duration) ease-in-out;
    transform: rotate(-90deg);
    display: inline-block;
    font-weight: bold;
  }
  .c-detail.c-detail__rotate > .c-detail__summary > :global(.c-detail__chevron):before {
    transform: rotate(0deg);
  }
</style>
