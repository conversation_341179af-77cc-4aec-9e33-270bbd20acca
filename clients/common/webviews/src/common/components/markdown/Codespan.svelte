<script lang="ts">
  import type { Tokens } from "marked";

  export let token: Tokens.Codespan;
  export let element: HTMLSpanElement | undefined = undefined;
  $: codespanContents = token.raw.slice(1, token.raw.length - 1);
</script>

<span bind:this={element}>
  <code class="markdown-codespan">
    <slot {codespanContents}>
      {codespanContents}
    </slot>
  </code>
</span>

<style>
  .markdown-codespan {
    --codepsan-border-color: var(--augment-border-color);
    --codepsan-bg-color: var(--user-theme-sidebar-background);
    --codespan-fg-color: var(--ds-color-accent-11);

    font-family: var(--augment-monospace-font-family);
    border-radius: 2px;
    padding-inline: 2px;

    /* Adjust font-size to account for border */
    font-size: 0.9em;

    /* Allow override of these values (used for linkable code spans) */
    background-color: var(--codepsan-bg-color);
    color: var(--codespan-fg-color);
    border: 1px solid var(--codepsan-border-color);

    /* Fixes line wrapping */
    word-break: break-all;

    display: inline;
  }
</style>
