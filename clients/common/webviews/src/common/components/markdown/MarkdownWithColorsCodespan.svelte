<script lang="ts">
  import type { Tokens } from "marked";
  import { isColorDark } from "../../utils/colors";

  export let token: Tokens.Codespan;
  export let element: HTMLSpanElement | undefined = undefined;
  $: codespanContents = token.raw.slice(1, token.raw.length - 1);
  $: isString = codespanContents.startsWith('"');
  $: isHexColor = /^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(codespanContents);
  $: isDarkColor = isHexColor && isColorDark(codespanContents);
</script>

<span bind:this={element}>
  <code
    class="markdown-codespan"
    class:markdown-string={isString}
    style={isHexColor
      ? `background-color: ${codespanContents}; color: ${isDarkColor ? "white" : "black"}`
      : ""}
  >
    {#if isHexColor}
      {codespanContents}
    {:else}
      {codespanContents}
    {/if}
  </code>
</span>

<style>
  .markdown-codespan {
    --codepsan-border-color: var(--augment-border-color);
    --codepsan-bg-color: var(--user-theme-sidebar-background);
    --codespan-fg-color: var(--ds-color-accent-11);

    font-family: var(--augment-monospace-font-family);
    border-radius: 2px;
    padding: 1.5px 3px;
    margin: -1.5px -0.5px;
    z-index: 0;

    font-size: 1em;

    /* Allow override of these values (used for linkable code spans) */
    background-color: var(--codepsan-bg-color);
    color: var(--codespan-fg-color);
    /* border: 1px solid var(--codepsan-border-color); */

    /* Fixes line wrapping */
    word-break: break-word;

    display: inline;
  }
  .markdown-string {
    color: var(--ds-color-info-11);
  }
</style>
