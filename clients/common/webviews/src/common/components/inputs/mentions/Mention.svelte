<script lang="ts" generics="TOption extends IMentionable">
  import type { MentionStore } from "./store";

  import SuggestionTooltip from "../../tooltips/SuggestionTooltip.svelte";
  import MentionTooltip from "./MentionTooltip.svelte";
  import type { IMentionable } from "../types";

  export let mentionStore: MentionStore<TOption>;

  $: suggestionsData = mentionStore.mentionSuggestionsData;
  $: chipData = mentionStore.mentionChipData;
  $: getClientRect = $suggestionsData?.props?.clientRect;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const SuggestionItem = mentionStore.suggestionItem;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  const MentionHoverContents = mentionStore.mentionHoverContents;
</script>

<SuggestionTooltip
  {suggestionsData}
  tippyProps={{
    getReferenceClientRect: () => getClientRect?.() || new DOMRect(),
    placement: "top-end",
    popperOptions: {
      strategy: "fixed",
    },
  }}
  onSelectItem={mentionStore.selectItem}
>
  <SuggestionItem slot="optionItem" let:option {option} />
</SuggestionTooltip>
<MentionTooltip {chipData}>
  <!-- Pass the option up from the slot, because it may cache the state for the slot -->
  <svelte:fragment slot="tooltipContents" let:option>
    {#if option}
      <div class="l-mention-tooltip">
        <MentionHoverContents {option} />
      </div>
    {/if}
  </svelte:fragment>
</MentionTooltip>

<style>
  :global(.tiptap .c-context-chip) {
    display: inline-block;
    padding: 0 var(--ds-spacing-1);
    margin: var(--ds-spacing-1) 0;
    border-radius: var(--augment-border-radius);
    background-color: var(--ds-color-neutral-9);
    color: var(--ds-white-contrast);

    /* Enable full line wrapping */
    word-break: break-all;
    white-space: pre-wrap;

    /* Hover on chips should not be a text selector */
    cursor: default;
  }

  .l-mention-tooltip {
    background-color: var(--user-theme-sidebar-background);
    border-radius: var(--augment-border-radius);
    padding: 2px;
  }
</style>
