<script lang="ts" context="module">
  import type { Readable } from "svelte/store";
  export type ContentSize = 1 | 2;
  export type ContentContext = {
    size: Readable<ContentSize>;
  };
  export const CONTENT_CONTEXT_KEY = "augment-dropdown-menu-content";
</script>

<script lang="ts">
  import TooltipContents from "$common-webviews/src/design-system/_primitives/TooltipAugment/Content.svelte";
  import { writable } from "svelte/store";
  import CardAugment from "../CardAugment.svelte";
  import { getContext, setContext } from "svelte";
  import type {
    TooltipContentAlign,
    TooltipContentSide,
  } from "../../_primitives/TooltipAugment/types";
  import DropdownMenuFocusContext from "./focus-context";
  import { TooltipContext } from "../../_primitives/TooltipAugment/context";

  export let size: ContentSize = 2;
  export let onEscapeKeyDown: (event: KeyboardEvent) => void = () => {};
  export let onClickOutside: (event: MouseEvent) => void = () => {};
  export let onRequestClose: (e: Event) => void = () => {};
  export let side: TooltipContentSide = "top";
  export let align: TooltipContentAlign = "center";

  const context = { size: writable(size) };
  const sizeState = context.size;
  setContext<ContentContext>(CONTENT_CONTEXT_KEY, context);
  $: sizeState.set(size);

  // Register the content element with the focus context so we manage just the content's focus
  const focusContext = getContext<DropdownMenuFocusContext>(DropdownMenuFocusContext.CONTEXT_KEY);
  const tooltipContext = getContext<TooltipContext>(TooltipContext.CONTEXT_KEY);
  $: openState = tooltipContext.state;

  /**
   * When the dropdown menu is open, no elements are in focus, and the user
   * presses tab, focus the first item in the dropdown menu.
   *
   * Tippy is appended to the document body, so we need to manage focus manually.
   * https://atomiks.github.io/tippyjs/v6/faq/#my-tooltip-appears-cut-off-or-is-not-showing-at-all
   */
  function focusOnDropdown(event: KeyboardEvent) {
    if (!$openState.open) return;
    if (event.key === "Tab" && !event.shiftKey) {
      const unfocused = focusContext.getCurrentFocusedIdx() === undefined;
      if (!unfocused) return;
      event.preventDefault();
      focusContext?.focusIdx(0);
    }
  }
</script>

<svelte:window on:keydown={focusOnDropdown} />
<TooltipContents on:keydown {onEscapeKeyDown} {onClickOutside} {onRequestClose} {side} {align}>
  <div class="l-dropdown-menu-augment__container">
    <CardAugment size={$sizeState} insetContent includeBackground={false}>
      <div
        use:focusContext.registerRoot
        class={`l-dropdown-menu-augment__contents l-dropdown-menu-augment__contents--size-${$sizeState}`}
      >
        <slot />
      </div>
    </CardAugment>
  </div>
</TooltipContents>

<style>
  .l-dropdown-menu-augment__contents {
    display: flex;
    flex-direction: column;
  }

  /* We want to support overflows in the dropdown for nested dropdowns and tooltips */
  .l-dropdown-menu-augment__container {
    display: contents;
    & .c-card:has(> .l-dropdown-menu-augment__contents) {
      overflow: visible;

      /* Make background color opaque */
      background-color: var(--user-theme-sidebar-background);
      color: initial;
    }
  }
  .l-dropdown-menu-augment__contents--size-1 {
    padding: var(--ds-spacing-1);
  }

  .l-dropdown-menu-augment__contents--size-2 {
    padding: var(--ds-spacing-2);
  }

  /* Ensure dropdown content appears above other elements */
  :global([data-tippy-root]:has(.l-dropdown-menu-augment__container)) {
    z-index: var(--z-dropdown-menu);
  }

  :global(.tippy-box:has(.l-dropdown-menu-augment__container)) {
    z-index: var(--z-dropdown-menu);
  }
</style>
