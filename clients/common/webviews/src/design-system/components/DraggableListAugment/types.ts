import type { SortableOptions } from "sortablejs";

export type DraggableListOptions = SortableOptions;

// Keep this in sync with the CSS classes in DraggableListAugment.svelte
// eslint-disable-next-line @typescript-eslint/naming-convention
export const SortableJsClasses = {
  ghost: "c-draggable-list-item__sortable-ghost",
  chosen: "c-draggable-list-item__sortable-chosen",
  drag: "c-draggable-list-item__sortable-drag",
} as const;
